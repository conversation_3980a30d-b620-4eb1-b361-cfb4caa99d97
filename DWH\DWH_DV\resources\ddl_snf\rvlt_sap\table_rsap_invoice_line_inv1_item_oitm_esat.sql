/*
 * 表名: rvlt_sap.rsap_invoice_line_inv1_item_oitm_esat
 * 
 * 描述：
 * 此表是SAP发票行项目与物料Link的扩展卫星表(Extended Satellite)，存储关联关系的扩展属性。
 * 作为Data Vault模型中的扩展卫星表，它保存Link关系的额外属性和历史变化。
 * 每条记录代表发票行与物料之间关联关系在特定时间点的扩展属性状态。
 * 
 * 字段说明：
 * - MD_INSERTED_AT: 记录插入时间戳，表示数据何时被加载到扩展卫星表中
 * - MD_RUN_ID: 加载过程的运行ID，用于跟踪ETL过程
 * - MD_RECORD_SOURCE: 数据来源系统，标识数据的来源
 * - MD_IS_DELETED: 标识记录是否被删除的标志
 * - MD_VALID_BEFORE: 记录有效期结束时间戳，用于历史追踪
 * - LK_RSAP_INVOICE_LINE_INV1_ITEM_OITM: 关联到Link表的哈希键
 * 
 * 相关表：
 * - rsap_invoice_line_inv1_item_oitm_lnk: 发票行与物料的Link表
 * - rsap_invoice_line_inv1_hub: 发票行的Hub表
 * - rsap_item_oitm_hub: 物料的Hub表
 * 
 * 在Data Vault模型中的作用：
 * 此扩展卫星表存储发票行与物料关联关系的额外属性，如特定于此关联的数量、价格等信息，
 * 并通过时间戳跟踪这些属性的历史变化。它与Link表一起，提供了发票行与物料关联的完整视图，
 * 支持详细的销售分析和产品性能评估。
 */

-- DROP TABLE rvlt_sap.rsap_invoice_line_inv1_item_oitm_esat;

CREATE TABLE rvlt_sap.rsap_invoice_line_inv1_item_oitm_esat (
MD_INSERTED_AT TIMESTAMP NOT NULL,
MD_RUN_ID INT NOT NULL,
MD_RECORD_SOURCE VARCHAR(255) NOT NULL,
MD_IS_DELETED BOOLEAN NOT NULL,
MD_VALID_BEFORE TIMESTAMP NOT NULL,
LK_RSAP_INVOICE_LINE_INV1_ITEM_OITM CHAR(28) NOT NULL
);

--COMMENT STATEMENTS

-- end of script --