/*
 * 表名: rvlt_sap.rsap_invoice_line_inv1_hub
 * 
 * 描述：
 * 此表是SAP发票行项目数据的Hub表，存储发票行的唯一标识信息。
 * 作为Data Vault模型中的Hub表，它保存业务实体的唯一标识符和业务键。
 * 每条记录代表一个唯一的发票行项目，不包含描述性属性。
 * 发票行是SAP发票系统中的基本业务实体，代表发票中的单个商品或服务项目。
 * 
 * 字段说明：
 * - MD_INSERTED_AT: 记录插入时间戳，表示数据何时被加载到Hub表中
 * - MD_RUN_ID: 加载过程的运行ID，用于跟踪ETL过程
 * - MD_RECORD_SOURCE: 数据来源系统，标识数据的来源
 * - HK_RSAP_INVOICE_LINE_INV1: 发票行的哈希键（主键），由业务键计算得出的唯一标识符
 * - COMPANY: 公司代码（业务键），标识发票所属的公司
 * - DOCENTRY: 文档编号（业务键），标识发票在SAP系统中的唯一编号
 * - LINENUM: 行号（业务键），标识发票中特定行项目的序号
 * 
 * 业务键组合（COMPANY + DOCENTRY + LINENUM）唯一标识一个发票行项目。
 * 
 * 相关表：
 * - rsap_invoice_line_inv1_j1_l10_sat: 存储发票行的描述性属性，如商品数量、单价、折扣等
 * - rsap_invoice_line_inv1_item_oitm_lnk: 连接发票行与物料主数据，建立发票行与销售物料之间的关系
 * - rsap_invoice_line_inv1_inv2_dlnk: 依赖链接表，连接发票行与其他相关数据，如分配规则等
 * - rsap_invoice_oinv_hub: 发票头Hub表，包含与此发票行相关的发票主数据
 * 
 * 在Data Vault模型中的作用：
 * 此Hub表是发票行业务实体的核心，通过各种Link表与其他业务实体（如发票头、物料等）建立关系，
 * 形成完整的发票数据结构。通过这种方式，可以灵活地分析发票行数据与其他业务实体的关系。
 */

-- DROP TABLE rvlt_sap.rsap_invoice_line_inv1_hub;

CREATE TABLE rvlt_sap.rsap_invoice_line_inv1_hub (
MD_INSERTED_AT TIMESTAMP NOT NULL, -- 记录插入时间戳
MD_RUN_ID INT NOT NULL, -- 加载过程的运行ID
MD_RECORD_SOURCE VARCHAR(255) NOT NULL, -- 数据来源系统
HK_RSAP_INVOICE_LINE_INV1 CHAR(28) NOT NULL, -- 发票行的哈希键（主键）
COMPANY VARCHAR(50) NULL, -- 公司代码（业务键）
DOCENTRY INTEGER NULL, -- 文档编号（业务键）
LINENUM INTEGER NULL -- 行号（业务键）
);

-- end of script --