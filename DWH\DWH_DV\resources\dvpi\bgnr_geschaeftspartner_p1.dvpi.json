{"dvdp_compiler": "dvpdc reference compiler,  release 0.6.2", "dvpi_version": "0.6.2", "compile_timestamp": "2024-11-13 17:30:21", "dvpd_version": "0.6.2", "pipeline_name": "bgnr_geschaeftspartner_p1", "dvpd_filename": "bgnr_geschaeftspartner_p1.dvpd.json", "tables": [{"table_name": "bgnr_geschaeftspartner_hub", "table_stereotype": "hub", "schema_name": "bvlt_general", "storage_component": "", "columns": [{"column_name": "MD_INSERTED_AT", "is_nullable": false, "column_class": "meta_load_date", "column_type": "TIMESTAMP"}, {"column_name": "MD_RUN_ID", "is_nullable": false, "column_class": "meta_load_process_id", "column_type": "INT"}, {"column_name": "MD_RECORD_SOURCE", "is_nullable": false, "column_class": "meta_record_source", "column_type": "VARCHAR(255)"}, {"column_name": "HK_BGNR_GESCHAEFTSPARTNER", "is_nullable": false, "column_class": "key", "column_type": "CHAR(28)"}, {"column_name": "XD_GESCHAEFTSPARTNER_ID", "is_nullable": true, "column_class": "business_key", "column_type": "VARCHAR(255)", "prio_for_column_position": 50000}]}, {"table_name": "bgnr_geschaeftspartner_p1_b10_sat", "table_stereotype": "sat", "schema_name": "bvlt_general", "storage_component": "", "has_deletion_flag": true, "is_effectivity_sat": false, "is_enddated": true, "is_multiactive": false, "compare_criteria": "key+current", "uses_diff_hash": true, "columns": [{"column_name": "MD_INSERTED_AT", "is_nullable": false, "column_class": "meta_load_date", "column_type": "TIMESTAMP"}, {"column_name": "MD_RUN_ID", "is_nullable": false, "column_class": "meta_load_process_id", "column_type": "INT"}, {"column_name": "MD_RECORD_SOURCE", "is_nullable": false, "column_class": "meta_record_source", "column_type": "VARCHAR(255)"}, {"column_name": "MD_IS_DELETED", "is_nullable": false, "column_class": "meta_deletion_flag", "column_type": "BOOLEAN"}, {"column_name": "MD_VALID_BEFORE", "is_nullable": false, "column_class": "meta_load_enddate", "column_type": "TIMESTAMP"}, {"column_name": "HK_BGNR_GESCHAEFTSPARTNER", "is_nullable": false, "column_class": "parent_key", "column_type": "CHAR(28)", "parent_key_column_name": "HK_BGNR_GESCHAEFTSPARTNER", "parent_table_name": "bgnr_geschaeftspartner_hub"}, {"column_name": "RH_BGNR_GESCHAEFTSPARTNER_P1_B10_SAT", "is_nullable": false, "column_class": "diff_hash", "column_type": "CHAR(28)"}, {"column_name": "GESELLSCHAFT", "is_nullable": true, "column_class": "content", "column_type": "VARCHAR(255)", "exclude_from_change_detection": false, "prio_for_column_position": 50000}, {"column_name": "NAME", "is_nullable": true, "column_class": "content", "column_type": "VARCHAR(255)", "exclude_from_change_detection": false, "prio_for_column_position": 50000}, {"column_name": "KUNDENGRUPPE ", "is_nullable": true, "column_class": "content", "column_type": "VARCHAR(255)", "exclude_from_change_detection": false, "prio_for_column_position": 50000}, {"column_name": "VERKAUFSGEBIET ", "is_nullable": true, "column_class": "content", "column_type": "VARCHAR(255)", "exclude_from_change_detection": false, "prio_for_column_position": 50000}, {"column_name": "VERKAEUFERNUMMER ", "is_nullable": true, "column_class": "content", "column_type": "VARCHAR(255)", "exclude_from_change_detection": false, "prio_for_column_position": 50000}]}, {"table_name": "bgnr_geschaeftspartner_rsap_business_partner_lnk", "table_stereotype": "lnk", "schema_name": "bvlt_general", "storage_component": "", "columns": [{"column_name": "MD_INSERTED_AT", "is_nullable": false, "column_class": "meta_load_date", "column_type": "TIMESTAMP"}, {"column_name": "MD_RUN_ID", "is_nullable": false, "column_class": "meta_load_process_id", "column_type": "INT"}, {"column_name": "MD_RECORD_SOURCE", "is_nullable": false, "column_class": "meta_record_source", "column_type": "VARCHAR(255)"}, {"column_name": "HK_BGNR_GESCHAEFTSPARTNER", "is_nullable": false, "column_class": "parent_key", "column_type": "CHAR(28)", "parent_key_column_name": "HK_BGNR_GESCHAEFTSPARTNER", "parent_table_name": "bgnr_geschaeftspartner_hub"}, {"column_name": "HK_RSAP_BUSINESS_PARTNER_OCRD", "is_nullable": false, "column_class": "parent_key", "column_type": "CHAR(28)", "parent_key_column_name": "HK_RSAP_BUSINESS_PARTNER_OCRD", "parent_table_name": "rsap_business_partner_ocrd_hub"}, {"column_name": "LK_BGNR_GESCHAEFTSPARTNER_RSAP_BUSINESS_PARTNER", "is_nullable": false, "column_class": "key", "column_type": "CHAR(28)"}]}, {"table_name": "bgnr_geschaeftspartner_rsap_business_partner_esat", "table_stereotype": "sat", "schema_name": "bvlt_general", "storage_component": "", "has_deletion_flag": true, "is_effectivity_sat": true, "is_enddated": true, "is_multiactive": false, "compare_criteria": "key+current", "uses_diff_hash": false, "driving_keys": ["HK_BGNR_GESCHAEFTSPARTNER"], "columns": [{"column_name": "MD_INSERTED_AT", "is_nullable": false, "column_class": "meta_load_date", "column_type": "TIMESTAMP"}, {"column_name": "MD_RUN_ID", "is_nullable": false, "column_class": "meta_load_process_id", "column_type": "INT"}, {"column_name": "MD_RECORD_SOURCE", "is_nullable": false, "column_class": "meta_record_source", "column_type": "VARCHAR(255)"}, {"column_name": "MD_IS_DELETED", "is_nullable": false, "column_class": "meta_deletion_flag", "column_type": "BOOLEAN"}, {"column_name": "MD_VALID_BEFORE", "is_nullable": false, "column_class": "meta_load_enddate", "column_type": "TIMESTAMP"}, {"column_name": "LK_BGNR_GESCHAEFTSPARTNER_RSAP_BUSINESS_PARTNER", "is_nullable": false, "column_class": "parent_key", "column_type": "CHAR(28)", "parent_key_column_name": "LK_BGNR_GESCHAEFTSPARTNER_RSAP_BUSINESS_PARTNER", "parent_table_name": "bgnr_geschaeftspartner_rsap_business_partner_lnk"}]}, {"table_name": "bgnr_geschaeftspartner_rbbe_shop_lnk", "table_stereotype": "lnk", "schema_name": "bvlt_general", "storage_component": "", "columns": [{"column_name": "MD_INSERTED_AT", "is_nullable": false, "column_class": "meta_load_date", "column_type": "TIMESTAMP"}, {"column_name": "MD_RUN_ID", "is_nullable": false, "column_class": "meta_load_process_id", "column_type": "INT"}, {"column_name": "MD_RECORD_SOURCE", "is_nullable": false, "column_class": "meta_record_source", "column_type": "VARCHAR(255)"}, {"column_name": "HK_BGNR_GESCHAEFTSPARTNER", "is_nullable": false, "column_class": "parent_key", "column_type": "CHAR(28)", "parent_key_column_name": "HK_BGNR_GESCHAEFTSPARTNER", "parent_table_name": "bgnr_geschaeftspartner_hub"}, {"column_name": "HK_RBBE_SHOP", "is_nullable": false, "column_class": "parent_key", "column_type": "CHAR(28)", "parent_key_column_name": "HK_RBBE_SHOP", "parent_table_name": "rbbe_shop_hub"}, {"column_name": "LK_BGNR_GESCHAEFTSPARTNER_RBBE_SHOP", "is_nullable": false, "column_class": "key", "column_type": "CHAR(28)"}]}, {"table_name": "bgnr_geschaeftspartner_rbbe_shop_esat", "table_stereotype": "sat", "schema_name": "bvlt_general", "storage_component": "", "has_deletion_flag": true, "is_effectivity_sat": true, "is_enddated": true, "is_multiactive": false, "compare_criteria": "key+current", "uses_diff_hash": false, "driving_keys": ["HK_BGNR_GESCHAEFTSPARTNER"], "columns": [{"column_name": "MD_INSERTED_AT", "is_nullable": false, "column_class": "meta_load_date", "column_type": "TIMESTAMP"}, {"column_name": "MD_RUN_ID", "is_nullable": false, "column_class": "meta_load_process_id", "column_type": "INT"}, {"column_name": "MD_RECORD_SOURCE", "is_nullable": false, "column_class": "meta_record_source", "column_type": "VARCHAR(255)"}, {"column_name": "MD_IS_DELETED", "is_nullable": false, "column_class": "meta_deletion_flag", "column_type": "BOOLEAN"}, {"column_name": "MD_VALID_BEFORE", "is_nullable": false, "column_class": "meta_load_enddate", "column_type": "TIMESTAMP"}, {"column_name": "LK_BGNR_GESCHAEFTSPARTNER_RBBE_SHOP", "is_nullable": false, "column_class": "parent_key", "column_type": "CHAR(28)", "parent_key_column_name": "LK_BGNR_GESCHAEFTSPARTNER_RBBE_SHOP", "parent_table_name": "bgnr_geschaeftspartner_rbbe_shop_lnk"}]}], "data_extraction": {"fetch_module_name": "none - ddl generation only"}, "parse_sets": [{"stage_properties": [{"stage_schema": "stage_bvlt", "stage_table_name": "bgnr_geschaeftspartner_p1_stage", "storage_component": ""}], "record_source_name_expression": "bgnr_geschaeftspartner_p1", "fields": [{"field_type": "VARCHAR(255)", "field_position": 1, "needs_encryption": false, "field_name": "XD_GESCHAEFTSPARTNER_ID"}, {"field_type": "VARCHAR(255)", "field_position": 2, "needs_encryption": false, "field_name": "GESELLSCHAFT"}, {"field_type": "VARCHAR(255)", "field_position": 3, "needs_encryption": false, "field_name": "NAME"}, {"field_type": "VARCHAR(255)", "field_position": 4, "needs_encryption": false, "field_name": "KUNDENGRUPPE "}, {"field_type": "VARCHAR(255)", "field_position": 5, "needs_encryption": false, "field_name": "VERKAUFSGEBIET "}, {"field_type": "VARCHAR(255)", "field_position": 6, "needs_encryption": false, "field_name": "VERKAEUFERNUMMER "}, {"field_type": "VARCHAR(255)", "field_position": 7, "needs_encryption": false, "field_name": "COMPANY"}, {"field_type": "VARCHAR(255)", "field_position": 8, "needs_encryption": false, "field_name": "CARDCODE"}, {"field_type": "NUMBER(36,0)", "field_position": 9, "needs_encryption": false, "field_name": "BILLBEESHOPID"}], "hashes": [{"stage_column_name": "HK_RSAP_BUSINESS_PARTNER_OCRD", "hash_origin_table": "rsap_business_partner_ocrd_hub", "column_class": "key", "hash_fields": [{"field_name": "COMPANY", "prio_in_key_hash": 0, "field_target_table": "rsap_business_partner_ocrd_hub", "field_target_column": "COMPANY"}, {"field_name": "CARDCODE", "prio_in_key_hash": 0, "field_target_table": "rsap_business_partner_ocrd_hub", "field_target_column": "CARDCODE"}], "column_type": "CHAR(28)", "hash_encoding": "BASE64", "hash_function": "sha-1", "hash_concatenation_seperator": "|", "hash_timestamp_format_sqlstyle": "YYYY-MM-DD HH24:MI:SS.US", "hash_null_value_string": "", "model_profile_name": "_default", "hash_name": "KEY_OF_RSAP_BUSINESS_PARTNER_OCRD_HUB"}, {"stage_column_name": "HK_RBBE_SHOP", "hash_origin_table": "rbbe_shop_hub", "column_class": "key", "hash_fields": [{"field_name": "BILLBEESHOPID", "prio_in_key_hash": 0, "field_target_table": "rbbe_shop_hub", "field_target_column": "BILLBEESHOPID"}], "column_type": "CHAR(28)", "hash_encoding": "BASE64", "hash_function": "sha-1", "hash_concatenation_seperator": "|", "hash_timestamp_format_sqlstyle": "YYYY-MM-DD HH24:MI:SS.US", "hash_null_value_string": "", "model_profile_name": "_default", "hash_name": "KEY_OF_RBBE_SHOP_HUB"}, {"stage_column_name": "HK_BGNR_GESCHAEFTSPARTNER", "hash_origin_table": "bgnr_geschaeftspartner_hub", "column_class": "key", "hash_fields": [{"field_name": "XD_GESCHAEFTSPARTNER_ID", "prio_in_key_hash": 0, "field_target_table": "bgnr_geschaeftspartner_hub", "field_target_column": "XD_GESCHAEFTSPARTNER_ID"}], "column_type": "CHAR(28)", "hash_encoding": "BASE64", "hash_function": "sha-1", "hash_concatenation_seperator": "|", "hash_timestamp_format_sqlstyle": "YYYY-MM-DD HH24:MI:SS.US", "hash_null_value_string": "", "model_profile_name": "_default", "hash_name": "KEY_OF_BGNR_GESCHAEFTSPARTNER_HUB"}, {"stage_column_name": "LK_BGNR_GESCHAEFTSPARTNER_RSAP_BUSINESS_PARTNER", "hash_origin_table": "bgnr_geschaeftspartner_rsap_business_partner_lnk", "column_class": "key", "hash_fields": [{"field_name": "XD_GESCHAEFTSPARTNER_ID", "prio_in_key_hash": 0, "field_target_table": "bgnr_geschaeftspartner_hub", "field_target_column": "XD_GESCHAEFTSPARTNER_ID", "parent_declaration_position": 1}, {"field_name": "COMPANY", "prio_in_key_hash": 0, "field_target_table": "rsap_business_partner_ocrd_hub", "field_target_column": "COMPANY", "parent_declaration_position": 2}, {"field_name": "CARDCODE", "prio_in_key_hash": 0, "field_target_table": "rsap_business_partner_ocrd_hub", "field_target_column": "CARDCODE", "parent_declaration_position": 2}], "column_type": "CHAR(28)", "hash_encoding": "BASE64", "hash_function": "sha-1", "hash_concatenation_seperator": "|", "hash_timestamp_format_sqlstyle": "YYYY-MM-DD HH24:MI:SS.US", "hash_null_value_string": "", "model_profile_name": "_default", "hash_name": "KEY_OF_BGNR_GESCHAEFTSPARTNER_RSAP_BUSINESS_PARTNER_LNK"}, {"stage_column_name": "LK_BGNR_GESCHAEFTSPARTNER_RBBE_SHOP", "hash_origin_table": "bgnr_geschaeftspartner_rbbe_shop_lnk", "column_class": "key", "hash_fields": [{"field_name": "XD_GESCHAEFTSPARTNER_ID", "prio_in_key_hash": 0, "field_target_table": "bgnr_geschaeftspartner_hub", "field_target_column": "XD_GESCHAEFTSPARTNER_ID", "parent_declaration_position": 1}, {"field_name": "BILLBEESHOPID", "prio_in_key_hash": 0, "field_target_table": "rbbe_shop_hub", "field_target_column": "BILLBEESHOPID", "parent_declaration_position": 2}], "column_type": "CHAR(28)", "hash_encoding": "BASE64", "hash_function": "sha-1", "hash_concatenation_seperator": "|", "hash_timestamp_format_sqlstyle": "YYYY-MM-DD HH24:MI:SS.US", "hash_null_value_string": "", "model_profile_name": "_default", "hash_name": "KEY_OF_BGNR_GESCHAEFTSPARTNER_RBBE_SHOP_LNK"}, {"stage_column_name": "RH_BGNR_GESCHAEFTSPARTNER_P1_B10_SAT", "hash_origin_table": "bgnr_geschaeftspartner_p1_b10_sat", "multi_row_content": false, "column_class": "diff_hash", "hash_fields": [{"field_name": "GESELLSCHAFT", "prio_in_diff_hash": 0, "prio_for_row_order": 50000, "field_target_table": "bgnr_geschaeftspartner_p1_b10_sat", "field_target_column": "GESELLSCHAFT"}, {"field_name": "NAME", "prio_in_diff_hash": 0, "prio_for_row_order": 50000, "field_target_table": "bgnr_geschaeftspartner_p1_b10_sat", "field_target_column": "NAME"}, {"field_name": "KUNDENGRUPPE ", "prio_in_diff_hash": 0, "prio_for_row_order": 50000, "field_target_table": "bgnr_geschaeftspartner_p1_b10_sat", "field_target_column": "KUNDENGRUPPE "}, {"field_name": "VERKAUFSGEBIET ", "prio_in_diff_hash": 0, "prio_for_row_order": 50000, "field_target_table": "bgnr_geschaeftspartner_p1_b10_sat", "field_target_column": "VERKAUFSGEBIET "}, {"field_name": "VERKAEUFERNUMMER ", "prio_in_diff_hash": 0, "prio_for_row_order": 50000, "field_target_table": "bgnr_geschaeftspartner_p1_b10_sat", "field_target_column": "VERKAEUFERNUMMER "}], "column_type": "CHAR(28)", "hash_encoding": "BASE64", "hash_function": "sha-1", "hash_concatenation_seperator": "|", "hash_timestamp_format_sqlstyle": "YYYY-MM-DD HH24:MI:SS.US", "hash_null_value_string": "", "model_profile_name": "_default", "related_key_hash": "KEY_OF_BGNR_GESCHAEFTSPARTNER_HUB", "hash_name": "DIFF_OF_BGNR_GESCHAEFTSPARTNER_P1_B10_SAT"}], "load_operations": [{"table_name": "bgnr_geschaeftspartner_hub", "relation_name": "/", "operation_origin": "field mapping relation", "hash_mappings": [{"hash_class": "key", "column_name": "HK_BGNR_GESCHAEFTSPARTNER", "is_nullable": false, "hash_name": "KEY_OF_BGNR_GESCHAEFTSPARTNER_HUB", "stage_column_name": "HK_BGNR_GESCHAEFTSPARTNER"}], "data_mapping": [{"column_name": "XD_GESCHAEFTSPARTNER_ID", "field_name": "XD_GESCHAEFTSPARTNER_ID", "column_class": "business_key", "is_nullable": true, "stage_column_name": "XD_GESCHAEFTSPARTNER_ID"}]}, {"table_name": "bgnr_geschaeftspartner_p1_b10_sat", "relation_name": "/", "operation_origin": "field mapping relation", "hash_mappings": [{"hash_class": "parent_key", "column_name": "HK_BGNR_GESCHAEFTSPARTNER", "is_nullable": false, "hash_name": "KEY_OF_BGNR_GESCHAEFTSPARTNER_HUB", "stage_column_name": "HK_BGNR_GESCHAEFTSPARTNER"}, {"hash_class": "diff_hash", "column_name": "RH_BGNR_GESCHAEFTSPARTNER_P1_B10_SAT", "is_nullable": false, "hash_name": "DIFF_OF_BGNR_GESCHAEFTSPARTNER_P1_B10_SAT", "stage_column_name": "RH_BGNR_GESCHAEFTSPARTNER_P1_B10_SAT"}], "data_mapping": [{"column_name": "GESELLSCHAFT", "field_name": "GESELLSCHAFT", "column_class": "content", "is_nullable": true, "stage_column_name": "GESELLSCHAFT"}, {"column_name": "NAME", "field_name": "NAME", "column_class": "content", "is_nullable": true, "stage_column_name": "NAME"}, {"column_name": "KUNDENGRUPPE ", "field_name": "KUNDENGRUPPE ", "column_class": "content", "is_nullable": true, "stage_column_name": "KUNDENGRUPPE "}, {"column_name": "VERKAUFSGEBIET ", "field_name": "VERKAUFSGEBIET ", "column_class": "content", "is_nullable": true, "stage_column_name": "VERKAUFSGEBIET "}, {"column_name": "VERKAEUFERNUMMER ", "field_name": "VERKAEUFERNUMMER ", "column_class": "content", "is_nullable": true, "stage_column_name": "VERKAEUFERNUMMER "}]}, {"table_name": "bgnr_geschaeftspartner_rsap_business_partner_lnk", "relation_name": "/", "operation_origin": "implicit unnamed relation, since all parents are universal", "hash_mappings": [{"hash_class": "parent_key_1", "column_name": "HK_BGNR_GESCHAEFTSPARTNER", "is_nullable": false, "hash_name": "KEY_OF_BGNR_GESCHAEFTSPARTNER_HUB", "stage_column_name": "HK_BGNR_GESCHAEFTSPARTNER"}, {"hash_class": "parent_key_2", "column_name": "HK_RSAP_BUSINESS_PARTNER_OCRD", "is_nullable": false, "hash_name": "KEY_OF_RSAP_BUSINESS_PARTNER_OCRD_HUB", "stage_column_name": "HK_RSAP_BUSINESS_PARTNER_OCRD"}, {"hash_class": "key", "column_name": "LK_BGNR_GESCHAEFTSPARTNER_RSAP_BUSINESS_PARTNER", "is_nullable": false, "hash_name": "KEY_OF_BGNR_GESCHAEFTSPARTNER_RSAP_BUSINESS_PARTNER_LNK", "stage_column_name": "LK_BGNR_GESCHAEFTSPARTNER_RSAP_BUSINESS_PARTNER"}]}, {"table_name": "bgnr_geschaeftspartner_rsap_business_partner_esat", "relation_name": "/", "operation_origin": "following parent operation list", "hash_mappings": [{"hash_class": "parent_key", "column_name": "LK_BGNR_GESCHAEFTSPARTNER_RSAP_BUSINESS_PARTNER", "is_nullable": false, "hash_name": "KEY_OF_BGNR_GESCHAEFTSPARTNER_RSAP_BUSINESS_PARTNER_LNK", "stage_column_name": "LK_BGNR_GESCHAEFTSPARTNER_RSAP_BUSINESS_PARTNER"}]}, {"table_name": "bgnr_geschaeftspartner_rbbe_shop_lnk", "relation_name": "/", "operation_origin": "implicit unnamed relation, since all parents are universal", "hash_mappings": [{"hash_class": "parent_key_1", "column_name": "HK_BGNR_GESCHAEFTSPARTNER", "is_nullable": false, "hash_name": "KEY_OF_BGNR_GESCHAEFTSPARTNER_HUB", "stage_column_name": "HK_BGNR_GESCHAEFTSPARTNER"}, {"hash_class": "parent_key_2", "column_name": "HK_RBBE_SHOP", "is_nullable": false, "hash_name": "KEY_OF_RBBE_SHOP_HUB", "stage_column_name": "HK_RBBE_SHOP"}, {"hash_class": "key", "column_name": "LK_BGNR_GESCHAEFTSPARTNER_RBBE_SHOP", "is_nullable": false, "hash_name": "KEY_OF_BGNR_GESCHAEFTSPARTNER_RBBE_SHOP_LNK", "stage_column_name": "LK_BGNR_GESCHAEFTSPARTNER_RBBE_SHOP"}]}, {"table_name": "bgnr_geschaeftspartner_rbbe_shop_esat", "relation_name": "/", "operation_origin": "following parent operation list", "hash_mappings": [{"hash_class": "parent_key", "column_name": "LK_BGNR_GESCHAEFTSPARTNER_RBBE_SHOP", "is_nullable": false, "hash_name": "KEY_OF_BGNR_GESCHAEFTSPARTNER_RBBE_SHOP_LNK", "stage_column_name": "LK_BGNR_GESCHAEFTSPARTNER_RBBE_SHOP"}]}], "stage_columns": [{"stage_column_name": "MD_INSERTED_AT", "is_nullable": false, "stage_column_class": "meta_load_date", "column_type": "TIMESTAMP"}, {"stage_column_name": "MD_RUN_ID", "is_nullable": false, "stage_column_class": "meta_load_process_id", "column_type": "INT"}, {"stage_column_name": "MD_RECORD_SOURCE", "is_nullable": false, "stage_column_class": "meta_record_source", "column_type": "VARCHAR(255)"}, {"stage_column_name": "MD_IS_DELETED", "is_nullable": false, "stage_column_class": "meta_deletion_flag", "column_type": "BOOLEAN"}, {"stage_column_name": "HK_RSAP_BUSINESS_PARTNER_OCRD", "stage_column_class": "hash", "is_nullable": false, "hash_name": "KEY_OF_RSAP_BUSINESS_PARTNER_OCRD_HUB", "column_type": "CHAR(28)"}, {"stage_column_name": "HK_RBBE_SHOP", "stage_column_class": "hash", "is_nullable": false, "hash_name": "KEY_OF_RBBE_SHOP_HUB", "column_type": "CHAR(28)"}, {"stage_column_name": "HK_BGNR_GESCHAEFTSPARTNER", "stage_column_class": "hash", "is_nullable": false, "hash_name": "KEY_OF_BGNR_GESCHAEFTSPARTNER_HUB", "column_type": "CHAR(28)"}, {"stage_column_name": "LK_BGNR_GESCHAEFTSPARTNER_RSAP_BUSINESS_PARTNER", "stage_column_class": "hash", "is_nullable": false, "hash_name": "KEY_OF_BGNR_GESCHAEFTSPARTNER_RSAP_BUSINESS_PARTNER_LNK", "column_type": "CHAR(28)"}, {"stage_column_name": "LK_BGNR_GESCHAEFTSPARTNER_RBBE_SHOP", "stage_column_class": "hash", "is_nullable": false, "hash_name": "KEY_OF_BGNR_GESCHAEFTSPARTNER_RBBE_SHOP_LNK", "column_type": "CHAR(28)"}, {"stage_column_name": "RH_BGNR_GESCHAEFTSPARTNER_P1_B10_SAT", "stage_column_class": "hash", "is_nullable": false, "hash_name": "DIFF_OF_BGNR_GESCHAEFTSPARTNER_P1_B10_SAT", "column_type": "CHAR(28)"}, {"stage_column_name": "XD_GESCHAEFTSPARTNER_ID", "stage_column_class": "data", "field_name": "XD_GESCHAEFTSPARTNER_ID", "is_nullable": true, "column_type": "VARCHAR(255)", "column_classes": ["business_key"]}, {"stage_column_name": "GESELLSCHAFT", "stage_column_class": "data", "field_name": "GESELLSCHAFT", "is_nullable": true, "column_type": "VARCHAR(255)", "column_classes": ["content"]}, {"stage_column_name": "NAME", "stage_column_class": "data", "field_name": "NAME", "is_nullable": true, "column_type": "VARCHAR(255)", "column_classes": ["content"]}, {"stage_column_name": "KUNDENGRUPPE ", "stage_column_class": "data", "field_name": "KUNDENGRUPPE ", "is_nullable": true, "column_type": "VARCHAR(255)", "column_classes": ["content"]}, {"stage_column_name": "VERKAUFSGEBIET ", "stage_column_class": "data", "field_name": "VERKAUFSGEBIET ", "is_nullable": true, "column_type": "VARCHAR(255)", "column_classes": ["content"]}, {"stage_column_name": "VERKAEUFERNUMMER ", "stage_column_class": "data", "field_name": "VERKAEUFERNUMMER ", "is_nullable": true, "column_type": "VARCHAR(255)", "column_classes": ["content"]}, {"stage_column_name": "COMPANY", "stage_column_class": "data", "field_name": "COMPANY", "is_nullable": true, "column_type": "VARCHAR(255)", "column_classes": ["business_key"]}, {"stage_column_name": "CARDCODE", "stage_column_class": "data", "field_name": "CARDCODE", "is_nullable": true, "column_type": "VARCHAR(255)", "column_classes": ["business_key"]}, {"stage_column_name": "BILLBEESHOPID", "stage_column_class": "data", "field_name": "BILLBEESHOPID", "is_nullable": true, "column_type": "NUMBER(36,0)", "column_classes": ["business_key"]}]}]}