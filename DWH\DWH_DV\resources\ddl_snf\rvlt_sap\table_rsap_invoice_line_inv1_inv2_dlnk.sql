/*
 * 表名: rvlt_sap.rsap_invoice_line_inv1_inv2_dlnk
 * 
 * 描述：
 * 此表是SAP发票行项目的依赖Link表(Dependent Link)，连接发票行与其他相关数据。
 * 作为Data Vault模型中的依赖Link表，它表示发票行与其他业务实体之间的依赖关系。
 * 每条记录代表一个发票行与其关联的分组信息之间的依赖关系。
 * 
 * 字段说明：
 * - MD_INSERTED_AT: 记录插入时间戳，表示依赖关系何时被记录
 * - MD_RUN_ID: 加载过程的运行ID，用于跟踪ETL过程
 * - MD_RECORD_SOURCE: 数据来源系统，标识数据的来源
 * - HK_RSAP_INVOICE_LINE_INV1: 发票行的哈希键，关联到发票行Hub表
 * - LK_RSAP_INVOICE_LINE_INV1_INV2: 依赖关系的哈希键，唯一标识此依赖关系
 * - GROUPNUM: 分组编号，用于标识发票行所属的分组或分类
 * 
 * 相关表：
 * - rsap_invoice_line_inv1_hub: 发票行的Hub表
 * - rsap_invoice_line_inv1_inv2_j1_l10_sat: 存储依赖关系的描述性属性
 * 
 * 在Data Vault模型中的作用：
 * 此依赖Link表扩展了发票行的业务上下文，允许将发票行与其他业务概念（如分配规则、成本中心等）
 * 关联起来。通过这种方式，可以进行更复杂的业务分析，如按分组或分类的销售分析。
 */

-- DROP TABLE rvlt_sap.rsap_invoice_line_inv1_inv2_dlnk;

CREATE TABLE rvlt_sap.rsap_invoice_line_inv1_inv2_dlnk (
MD_INSERTED_AT TIMESTAMP NOT NULL,
MD_RUN_ID INT NOT NULL,
MD_RECORD_SOURCE VARCHAR(255) NOT NULL,
HK_RSAP_INVOICE_LINE_INV1 CHAR(28) NOT NULL,
LK_RSAP_INVOICE_LINE_INV1_INV2 CHAR(28) NOT NULL,
GROUPNUM INTEGER NULL
);

--COMMENT STATEMENTS

-- end of script --