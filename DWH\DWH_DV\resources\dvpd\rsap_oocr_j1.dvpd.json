{"dvpd_version": "0.6.2", "stage_properties": [{"stage_schema": "stage_rvlt", "stage_table_name": "rsap_oocr_j1_stage"}], "pipeline_name": "rsap_oocr_j1", "record_source_name_expression": "sap.oocr", "data_extraction": {"fetch_module_name": "none - ddl generation only"}, "fields": [{"field_name": "company", "field_type": "VARCHAR(50)", "targets": [{"table_name": "RSAP_DISTRIBUTION_RULE_OOCR_HUB"}]}, {"field_name": "ocrcode", "field_type": "VARCHAR(20)", "targets": [{"table_name": "RSAP_DISTRIBUTION_RULE_OOCR_HUB"}]}, {"field_name": "json_text", "field_type": "VARCHAR", "targets": [{"table_name": "RSAP_DISTRIBUTION_RULE_OOCR_J1_L10_SAT"}]}], "data_vault_model": [{"schema_name": "rvlt_sap", "tables": [{"table_name": "rsap_distribution_rule_oocr_hub", "table_stereotype": "hub", "hub_key_column_name": "HK_RSAP_DISTRIBUTION_RULE_OOCR"}, {"table_name": "rsap_distribution_rule_oocr_j1_l10_sat", "table_stereotype": "sat", "satellite_parent_table": "RSAP_DISTRIBUTION_RULE_OOCR_HUB", "diff_hash_column_name": "RH_RSAP_DISTRIBUTION_RULE_OOCR_J1_L10_SAT"}]}], "deletion_detection_rules": [{"procedure": "stage_comparison", "tables_to_cleanup": ["rsap_distribution_rule_oocr_j1_l10_sat"]}]}