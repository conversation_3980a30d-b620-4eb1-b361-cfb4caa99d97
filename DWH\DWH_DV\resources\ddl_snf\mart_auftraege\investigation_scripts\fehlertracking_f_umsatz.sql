select * from DV_D_MAIN_DATABASE.RVLT_GENERAL.RGNR_GESELLSCHAFT_HUB
where company ='BINGOLD_DE'

/* detailbuchunge<PERSON> von <PERSON>_<PERSON> im Oktober, insbesondere 24. und 30.Oktober */
	select
		gesellschaft_lnk.hk_rgnr_gesellschaft 
		,item_lnk.hk_rsap_item_oitm
		,bpartner_semployee_lnk.hk_rsap_business_partner_ocrd 
		,bpartner_semployee_lnk.hk_rsap_sales_employee_oslp 
		,line_sat.md_inserted_at -- DEBUG
		,head_sat.docdate
		,line_sat.ocrcode
		,line_sat.ocrcode2
		,line_korr_sat.linetotal_korrigiert 
		,line_sat.currency 
	from   RVLT_SAP.RSAP_INVOICE_LINE_INV1_P1_L20_SAT line_sat
	join bvlt_sap.bsap_INVOICE_LINE_INV1_korrigiert_v1_b10_current_sat  line_korr_sat
		on line_korr_sat.hk_rsap_INVOICE_LINE_INV1 =line_sat.hk_rsap_INVOICE_LINE_INV1
	join RVLT_SAP.RSAP_INVOICE_LINE_INV1_INVOICE_OINV_LNK head_lnk
		on head_lnk.HK_RSAP_INVOICE_LINE_INV1 = line_sat.HK_RSAP_INVOICE_LINE_INV1
	join RVLT_SAP.RSAP_INVOICE_OINV_P1_L20_SAT head_sat
		on head_sat.hk_rsap_invoice_oinv = head_lnk.hk_rsap_invoice_oinv
		and head_sat.md_valid_before = lib.dwh_far_future_date()
		and not head_sat.md_is_deleted
	join RVLT_SAP.RSAP_INVOICE_OINV_RGNR_GESELLSCHAFT_LNK gesellschaft_lnk
		on gesellschaft_lnk.hk_rsap_invoice_oinv = head_lnk.hk_rsap_invoice_oinv 
	join RVLT_SAP.RSAP_INVOICE_OINV_BUSINESS_PARTNER_OCRD_SALES_EMPLOYEE_OSLP_LNK bpartner_semployee_lnk
		on bpartner_semployee_lnk.hk_rsap_invoice_oinv = head_lnk.hk_rsap_invoice_oinv 
	join RVLT_SAP.RSAP_INVOICE_OINV_BUSINESS_PARTNER_OCRD_SALES_EMPLOYEE_OSLP_ESAT bpartner_semployee_esat
		on bpartner_semployee_esat.LK_RSAP_INVOICE_OINV_BUSINESS_PARTNER_OCRD_SALES_EMPLOYEE_OSLP =bpartner_semployee_lnk.LK_RSAP_INVOICE_OINV_BUSINESS_PARTNER_OCRD_SALES_EMPLOYEE_OSLP 
		and bpartner_semployee_esat.md_valid_before = lib.dwh_far_future_date()
		and not bpartner_semployee_esat.md_is_deleted
	join rvlt_sap.rsap_invoice_line_inv1_item_oitm_lnk item_lnk
		on item_lnk.hk_rsap_invoice_line_inv1 =line_sat.hk_rsap_invoice_line_inv1 
	join rvlt_sap.rsap_invoice_line_inv1_item_oitm_esat item_esat
		on item_esat.lk_rsap_invoice_line_inv1_item_oitm =item_lnk.lk_rsap_invoice_line_inv1_item_oitm 
		and item_esat.md_valid_before = lib.dwh_far_future_date()
		and not head_sat.md_is_deleted 
	where line_sat.md_valid_before = lib.dwh_far_future_date()
		and not line_sat.md_is_deleted
        and head_sat.doctype ='I' -- Nur Artikelrechnungen
        and gesellschaft_lnk.hk_rgnr_gesellschaft ='lvcIsVRFxu9B6kVpzEDhqF40HJQ=' -- bingold_de'
        and head_sat.docdate between '2024-10-24' and '2024-10-30'
        order by head_sat.docdate;
        
/* exchangerates DEISS_CH */

select max(md_inserted_at)
from RVLT_SAP.RSAP_CURRENCY_ORTT_EXCHANGE_RATE_J1_L10_SAT
        
select
	ratedate ,currency ,rate ,company 
from  BVLT_SAP.BSAP_EXCHANGE_RATE_ORTT_CURRENT_REF r
where company='DEISS_DE'
and r.currency ='CHF'
 
select
	ratedate ,currency ,rate ,company 
from  BVLT_SAP.BSAP_EXCHANGE_RATE_ORTT_CURRENT_REF r
where company='DEISS_CH'
and r.currency ='EUR'
order by ratedate ,company 

/* Voller Auftrag nach Docnum*/
select 
company
,docentry 
,linenum
,s.linetotal 
from DV_D_MAIN_DATABASE.RVLT_SAP.RSAP_INVOICE_LINE_INV1_HUB h
join DV_D_MAIN_DATABASE.RVLT_SAP.RSAP_INVOICE_LINE_INV1_P1_L20_SAT s
 on s.hk_rsap_invoice_line_inv1 = h.hk_rsap_invoice_line_inv1 
 and s.md_valid_before = lib.dwh_far_future_date()
 and not s.md_is_deleted
where company='FIPP_DE'
and docentry = 121580
order by linenum 

/* Anaylse von Inner Join ausschlüssen */

with measures as (
	select
		gesellschaft_lnk.hk_rgnr_gesellschaft 
		,item_lnk.hk_rsap_item_oitm
		,bpartner_semployee_lnk.hk_rsap_business_partner_ocrd 
		,bpartner_semployee_lnk.hk_rsap_sales_employee_oslp 
		,head_sat.docdate
		,head_sat.doctype 
		,line_sat.ocrcode
		,line_sat.ocrcode2
		,line_korr_sat.linetotal_korrigiert 
		,line_sat.currency 
	from   RVLT_SAP.RSAP_INVOICE_LINE_INV1_P1_L20_SAT line_sat
	join bvlt_sap.bsap_INVOICE_LINE_INV1_korrigiert_v1_b10_current_sat  line_korr_sat
		on line_korr_sat.hk_rsap_INVOICE_LINE_INV1 =line_sat.hk_rsap_INVOICE_LINE_INV1
	join RVLT_SAP.RSAP_INVOICE_LINE_INV1_INVOICE_OINV_LNK head_lnk
		on head_lnk.HK_RSAP_INVOICE_LINE_INV1 = line_sat.HK_RSAP_INVOICE_LINE_INV1
	join RVLT_SAP.RSAP_INVOICE_OINV_P1_L20_SAT head_sat
		on head_sat.hk_rsap_invoice_oinv = head_lnk.hk_rsap_invoice_oinv
		and head_sat.md_valid_before = lib.dwh_far_future_date()
		and not head_sat.md_is_deleted
	join RVLT_SAP.RSAP_INVOICE_OINV_RGNR_GESELLSCHAFT_LNK gesellschaft_lnk
		on gesellschaft_lnk.hk_rsap_invoice_oinv = head_lnk.hk_rsap_invoice_oinv 
	join RVLT_SAP.RSAP_INVOICE_OINV_BUSINESS_PARTNER_OCRD_SALES_EMPLOYEE_OSLP_LNK bpartner_semployee_lnk
		on bpartner_semployee_lnk.hk_rsap_invoice_oinv = head_lnk.hk_rsap_invoice_oinv 
	join RVLT_SAP.RSAP_INVOICE_OINV_BUSINESS_PARTNER_OCRD_SALES_EMPLOYEE_OSLP_ESAT bpartner_semployee_esat
		on bpartner_semployee_esat.LK_RSAP_INVOICE_OINV_BUSINESS_PARTNER_OCRD_SALES_EMPLOYEE_OSLP =bpartner_semployee_lnk.LK_RSAP_INVOICE_OINV_BUSINESS_PARTNER_OCRD_SALES_EMPLOYEE_OSLP 
		and bpartner_semployee_esat.md_valid_before = lib.dwh_far_future_date()
		and not bpartner_semployee_esat.md_is_deleted
	join rvlt_sap.rsap_invoice_line_inv1_item_oitm_lnk item_lnk
		on item_lnk.hk_rsap_invoice_line_inv1 =line_sat.hk_rsap_invoice_line_inv1 
	join rvlt_sap.rsap_invoice_line_inv1_item_oitm_esat item_esat
		on item_esat.lk_rsap_invoice_line_inv1_item_oitm =item_lnk.lk_rsap_invoice_line_inv1_item_oitm 
		and item_esat.md_valid_before = lib.dwh_far_future_date()
		and not head_sat.md_is_deleted 
	join RVLT_SAP.RSAP_INVOICE_LINE_INV1_HUB line_hub
		on line_hub.hk_rsap_invoice_line_inv1 =line_sat.hk_rsap_invoice_line_inv1 
	where line_sat.md_valid_before = lib.dwh_far_future_date()
		and not line_sat.md_is_deleted
        and (head_sat.doctype ='I' or line_hub.company ='FIPP_DE') -- Nur Artikelrechnungen (dann wären es 395.409) bei FIPP_DE nehmen wir allesn, dann sind es  395.527 rows
        --
        and gesellschaft_lnk.hk_rgnr_gesellschaft='Q0lYdn72rlg/opQIFRwPSJdXulQ=' -- FIPP_DE
        and docdate ='2024-10-29'	
	--
	union all
	--
	select
		gesellschaft_lnk.hk_rgnr_gesellschaft 
		,item_lnk.hk_rsap_item_oitm
		,bpartner_semployee_lnk.hk_rsap_business_partner_ocrd 
		,bpartner_semployee_lnk.hk_rsap_sales_employee_oslp 
		,head_sat.docdate
		,head_sat.doctype 
		,line_sat.ocrcode
		,line_sat.ocrcode2
		,-line_korr_sat.linetotal_korrigiert 
		,line_sat.currency cy
	from RVLT_SAP.rsap_credit_memo_line_rin1_p1_l20_sat  line_sat
	join bvlt_sap.bsap_credit_memo_line_rin1_korrigiert_v1_b10_current_sat  line_korr_sat
		on line_korr_sat.hk_rsap_credit_memo_line_rin1 =line_sat.hk_rsap_credit_memo_line_rin1 
	join RVLT_SAP.rsap_credit_memo_line_rin1_credit_memo_orin_lnk head_lnk
		on head_lnk.hk_rsap_credit_memo_line_rin1 = line_sat.hk_rsap_credit_memo_line_rin1 
	join RVLT_SAP.rsap_credit_memo_orin_p1_l20_sat head_sat
		on head_sat.hk_rsap_credit_memo_orin = head_lnk.hk_rsap_credit_memo_orin
		and head_sat.md_valid_before = lib.dwh_far_future_date()
		and not head_sat.md_is_deleted
	join RVLT_SAP.rsap_credit_memo_orin_rgnr_gesellschaft_lnk  gesellschaft_lnk
		on gesellschaft_lnk.hk_rsap_credit_memo_orin = head_lnk.hk_rsap_credit_memo_orin 
	join RVLT_SAP.rsap_credit_memo_orin_business_partner_ocrd_SALES_EMPLOYEE_OSLP_lnk bpartner_semployee_lnk
		on bpartner_semployee_lnk.hk_rsap_credit_memo_orin = head_lnk.hk_rsap_credit_memo_orin 
	join RVLT_SAP.rsap_credit_memo_orin_business_partner_ocrd_SALES_EMPLOYEE_OSLP_ESAT bpartner_semployee__esat
		on bpartner_semployee__esat.LK_RSAP_CREDIT_MEMO_ORIN_BUSINESS_PARTNER_OCRD_SALES_EMPLOYEE_OSLP =bpartner_semployee_lnk.LK_RSAP_CREDIT_MEMO_ORIN_BUSINESS_PARTNER_OCRD_SALES_EMPLOYEE_OSLP 
		and bpartner_semployee__esat.md_valid_before = lib.dwh_far_future_date()
		and not bpartner_semployee__esat.md_is_deleted
	join rvlt_sap.rsap_credit_memo_line_rin1_item_oitm_lnk item_lnk
		on item_lnk.hk_rsap_credit_memo_line_rin1 =line_sat.hk_rsap_credit_memo_line_rin1 
	join rvlt_sap.rsap_credit_memo_line_rin1_item_oitm_esat item_esat
		on item_esat.lk_rsap_credit_memo_line_rin1_item_oitm =item_lnk.lk_rsap_credit_memo_line_rin1_item_oitm 
		and item_esat.md_valid_before = lib.dwh_far_future_date()
		and not head_sat.md_is_deleted 
	join RVLT_SAP.rsap_credit_memo_line_rin1_hub line_hub
		on line_hub.hk_rsap_credit_memo_line_rin1 =line_sat.hk_rsap_credit_memo_line_rin1 
	where line_sat.md_valid_before = lib.dwh_far_future_date()
		and not line_sat.md_is_deleted	
        and (head_sat.doctype ='I' or line_hub.company ='FIPP_DE') -- Nur Artikelrechnungen außer bei FIPP, da alle Rechnungen
        --
        and gesellschaft_lnk.hk_rgnr_gesellschaft='Q0lYdn72rlg/opQIFRwPSJdXulQ=' -- FIPP_DE
        and docdate ='2024-10-29'	        
		)
, SAP_businesspartner_current as (
	select  lnk.hk_rsap_business_partner_ocrd,  lnk.hk_bgnr_geschaeftspartner,b10_sat.kundengruppe 
	from bvlt_general.bgnr_geschaeftspartner_rsap_business_partner_lnk lnk
	join bvlt_general.bgnr_geschaeftspartner_rsap_business_partner_esat  esat 
		on esat.lk_bgnr_geschaeftspartner_rsap_business_partner = lnk.lk_bgnr_geschaeftspartner_rsap_business_partner 
		and esat.md_valid_before = lib.dwh_far_future_date()
		and not esat.md_is_deleted
	join bvlt_general.bgnr_geschaeftspartner_p1_b10_sat b10_sat 
		on b10_sat.hk_bgnr_geschaeftspartner = lnk.hk_bgnr_geschaeftspartner 
)
, sap_items_with_itmsgrpnam  as (
	select item_hub.hk_rsap_item_oitm,group_ref.itmsgrpnam ,item_sat.itmgrpcod 
	from rvlt_sap.rsap_item_oitm_hub item_hub
	join rvlt_sap.rsap_item_oitm_p1_l20_sat item_sat
		on item_sat.hk_rsap_item_oitm =item_hub.hk_rsap_item_oitm 
		and item_sat.md_valid_before = lib.dwh_far_future_date()
		and not item_sat.md_is_deleted
	left join bvlt_sap.bsap_item_group_oitb_current_ref group_ref
		on group_ref.company =item_hub.company 
		and group_ref.itmsgrpcod = item_sat.itmgrpcod 
)		
--, fully_joined_and_aggregated_sap_dataset as (
	select measures.hk_rgnr_gesellschaft   			-- 1
		,businesspartner.hk_bgnr_geschaeftspartner  --2
		,businesspartner.kundengruppe				--3
		,DECODE(semp_sat.u_dim1,'10','HGH','20','GH','01','Fabrik','?'||semp_sat.u_dim1||'?') 	handelsplatz						--4
		,measures.docdate							--5
		,measures.doctype
		,measures.ocrcode							--6
		,measures.ocrcode2							--7
		,measures.currency 							--8
		,sap_items.itmsgrpnam						--9
		,sap_items.itmgrpcod 						--10
		,sum(measures.linetotal_korrigiert) linetotal_korrigiert_sum
	from measures
	join sap_items_with_itmsgrpnam sap_items 
		 on sap_items.hk_rsap_item_oitm=measures.hk_rsap_item_oitm
	left join SAP_businesspartner_current businesspartner	
		on businesspartner.hk_rsap_business_partner_ocrd = measures.hk_rsap_business_partner_ocrd
	left join RVLT_SAP.RSAP_SALES_EMPLOYEE_OSLP_P1_L20_SAT	semp_sat
		on semp_sat.hk_rsap_sales_employee_oslp = measures.hk_rsap_sales_employee_oslp
		and semp_sat.md_valid_before = lib.dwh_far_future_date()
		and not semp_sat.md_is_deleted
	group by 1,2,3,4,5,6,7,8,9,10,11
	
	)
-- Final sap dataset with last lookups, in row calculations and target column naming
,final_sap_dataset as(	
select 
		gesellschaft_hub.company 								as Gesellschaft
		,fds.docdate											as tagesdatum
		,'---'													as kanal
		,fds.handelsplatz										as handelsplatz_aka_U_Dim1_von_vertriebsmitarbeiter
		,distrule_ref.ocrname									as verkaufsgebiet			
		,distrule_ref2.ocrname									as verkaeufernummer
		,fds.kundengruppe										as kundengruppe
		,fds.hk_bgnr_geschaeftspartner 							as dk_geschaeftspartner
		,fds.itmsgrpnam										    as artikelgruppe
		,fds.itmgrpcod											as artikelgruppe_code
		,round(fds.linetotal_korrigiert_sum,2)					as umsatz
		,fds.currency											as waehrung
		,case when fds.currency='EUR' 
			  then round(fds.linetotal_korrigiert_sum,2)
			  when fds.currency='CHF' and gesellschaft_hub.company='DEISS_CH'
              then round(fds.linetotal_korrigiert_sum*exch_eur_src.rate,2) -- Eurokurs bei DEISS_CH
              else round(fds.linetotal_korrigiert_sum/exch_de.rate,2) -- Fremdwährungskurs bei DEISS
			end 												as umsatz_eur
		,case when fds.currency='EUR'
			  then 1
			  when fds.currency='CHF' and gesellschaft_hub.company='DEISS_CH'
              then exch_eur_src.rate -- Eurokurs bei DEISS_CH
              else exch_de.rate -- Fremdwährungskurs bei DEISS
			end													as rate
from fully_joined_and_aggregated_sap_dataset fds
join DV_D_MAIN_DATABASE.RVLT_GENERAL.RGNR_GESELLSCHAFT_HUB gesellschaft_hub
    on gesellschaft_hub.hk_rgnr_gesellschaft = fds.hk_rgnr_gesellschaft
left join BVLT_SAP.BSAP_DISTRIBUTION_RULE_OOCR_CURRENT_REF distrule_ref
	on distrule_ref.company  = gesellschaft_hub.company 
	and distrule_ref.ocrcode =fds.ocrcode
left join BVLT_SAP.BSAP_DISTRIBUTION_RULE_OOCR_CURRENT_REF distrule_ref2
	on distrule_ref2.company  = gesellschaft_hub.company 
	and distrule_ref2.ocrcode =fds.ocrcode2
left join BVLT_SAP.BSAP_EXCHANGE_RATE_ORTT_CURRENT_REF exch_de
		on exch_de.ratedate = fds.docdate 
		and exch_de.currency = fds.currency			-- Fremdwährungskurs bei DEISS
		and exch_de.company = 'DEISS_DE'				-- Fremdwährungskurs bei DEISS
left join BVLT_SAP.BSAP_EXCHANGE_RATE_ORTT_CURRENT_REF exch_eur_src
		on exch_eur_src.ratedate = fds.docdate 
		and exch_eur_src.currency = 'EUR' 					-- Eurokurs der Quelle
		and exch_eur_src.company = gesellschaft_hub.company -- Eurokurs der Quelle
)		          


/* statistics of doctypes INVOICE */        
select company ,docdate,doctype,count(1),sum(doctotal)
from RVLT_SAP.rsap_invoice_oinv_hub head_hub
join RVLT_SAP.rsap_invoice_oinv_p1_l20_sat head_sat
	on head_sat.hk_rsap_invoice_oinv = head_hub.hk_rsap_invoice_oinv 
	and head_sat.md_valid_before = lib.dwh_far_future_date()
		and not head_sat.md_is_deleted
where company='FIPP_DE'	 and docdate ='2024-10-29'	
group by 1,2,3	;
  
        
/* statistics of doctypes CREDIT_MEMO */        
select company ,docdate,doctype,count(1)
from RVLT_SAP.RSAP_CREDIT_MEMO_ORIN_HUB head_hub
join RVLT_SAP.rsap_credit_memo_orin_p1_l20_sat head_sat
	on head_sat.hk_rsap_credit_memo_orin = head_hub.hk_rsap_credit_memo_orin 
	and head_sat.md_valid_before = lib.dwh_far_future_date()
		and not head_sat.md_is_deleted
where company='FIPP_DE'	 and docdate ='2024-10-29'	
group by 1,2,3	;
  
/* verify minus operator */

with current_result as (
select 
	gesellschaft,
	tagesdatum ,
	handelsplatz_aka_u_dim1_von_vertriebsmitarbeiter hp,
	kundengruppe ,
	ARTIKELGRUPPE_CODE,
	sum(umsatz_eur) summe_umsatz_eur
from DV_D_MAIN_DATABASE.MART_AUFTRAEGE.F_UMSATZ
where tagesdatum = '2024-10-29'
and gesellschaft ='FIPP_DE'
and kundengruppe ='DM'
group by 1,2,3,4,5
order by 1,2,3,4,5
)
, reference_result as (
select 
	gesellschaft,
	tagesdatum ,
	hp,
	kundengruppe,
	ARTIKELGRUPPE_CODE,
	summe_umsatz_eur
from mart_auftraege_referenz._20241128_0954_F_umsatz_2024_oktober
where tagesdatum = '2024-10-29'
and gesellschaft ='FIPP_DE'
and kundengruppe ='DM'
--group by 1,2,3,4,5,6
order by 1,2,3,4,5,6
)
select *,'<-- current' from current_result
minus
select *,'<-- current' from reference_result
union
select *,'<-- reference' from reference_result
minus
select *,'<-- reference' from current_result

 
        
        
        