Data vault pipeline developer cheat sheet 
rendered from  rsap_ocrg_j1.dvpi

pipeline name:  rsap_ocrg_j1

------------------------------------------------------
record source:  sap.ocrg

Source fields:
       COMPANY    VARCHAR(50)
       GROUPCODE  INTEGER
       JSON_TEXT  VARCHAR


------------------------------------------------------
Table List:
stage_table.rvlt_sap.rsap_ocrg_j1_stage
table.rvlt_sap.rsap_card_group_ocrg_hub
table.rvlt_sap.rsap_card_group_ocrg_j1_l10_sat

------------------------------------------------------
stage table:  stage_rvlt.rsap_ocrg_j1_stage
Field to Stage mapping:
	--business keys,
		COMPANY    >  COMPANY,
		GROUPCODE  >  GROUPCODE,

	--content,
		JSON_TEXT  >  JSON_TEXT

------------------------------------------------------
Hash value composition

HK_RSAP_CARD_GROUP_OCRG (key)
		COMPANY 
		GROUPCODE 

RH_RSAP_CARD_GROUP_OCRG_J1_L10_SAT (diff_hash)
		JSON_TEXT 


------------------------------------------------------
Table load method
(STAGE >  VAULT)

rsap_card_group_ocrg_hub (/) can be loaded by convention
		  key: HK_RSAP_CARD_GROUP_OCRG  >  HK_RSAP_CARD_GROUP_OCRG
		  business_key: COMPANY  >  COMPANY 
		  business_key: GROUPCODE  >  GROUPCODE 

rsap_card_group_ocrg_j1_l10_sat (/) can be loaded by convention
		  parent_key: HK_RSAP_CARD_GROUP_OCRG  >  HK_RSAP_CARD_GROUP_OCRG
		  diff_hash: RH_RSAP_CARD_GROUP_OCRG_J1_L10_SAT  >  RH_RSAP_CARD_GROUP_OCRG_J1_L10_SAT
		  content: JSON_TEXT  >  JSON_TEXT 

