{"model_profile_name": "_default", "table_key_column_type": "CHAR(28)", "table_key_hash_function": "sha-1", "table_key_hash_encoding": "BASE64", "hash_concatenation_seperator": "|", "hash_timestamp_format_sqlstyle": "YYYY-MM-DD HH24:MI:SS.US", "hash_null_value_string": "", "key_for_null_ghost_record": "0000000000000000000000000001", "key_for_missing_ghost_record": "FFFFFFFFFFFFFFFFFFFFFFFFFFFE", "far_future_timestamp": "2299-12-30 00:00:00", "compare_criteria_default": "key+current", "uses_diff_hash_default": "true", "diff_hash_column_type": "CHAR(28)", "diff_hash_function": "sha-1", "diff_hash_encoding": "BASE64", "is_enddated_default": "true", "load_date_column_name": "MD_INSERTED_AT", "load_date_column_type": "TIMESTAMP", "load_enddate_column_name": "MD_VALID_BEFORE", "load_enddate_column_type": "TIMESTAMP", "has_deletion_flag_default": "true", "deletion_flag_column_name": "MD_IS_DELETED", "deletion_flag_column_type": "BOOLEAN", "record_source_column_name": "MD_RECORD_SOURCE", "record_source_column_type": "VARCHAR(255)", "load_process_id_column_name": "MD_RUN_ID", "load_process_id_column_type": "INT", "xenc_encryption_key_column_type": "CHAR(28)", "xenc_encryption_key_index_column_type": "INT", "xenc_content_hash_column_type": "CHAR(28)", "xenc_content_hash_function": "sha-1", "xenc_content_hash_encoding": "BASE64"}