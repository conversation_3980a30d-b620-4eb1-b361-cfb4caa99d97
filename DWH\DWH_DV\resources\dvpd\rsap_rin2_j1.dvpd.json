{"dvpd_version": "0.6.2", "stage_properties": [{"stage_schema": "stage_rvlt", "stage_table_name": "rsap_rin2_j1_stage"}], "pipeline_name": "rsap_rin2_j1", "record_source_name_expression": "sap.rin2", "data_extraction": {"fetch_module_name": "none - ddl and cnode snippet generation only"}, "fields": [{"field_name": "company", "field_type": "<PERSON><PERSON><PERSON><PERSON>(50)", "targets": [{"table_name": "RSAP_CREDIT_MEMO_LINE_RIN1_HUB"}]}, {"field_name": "docentry", "field_type": "INTEGER", "targets": [{"table_name": "RSAP_CREDIT_MEMO_LINE_RIN1_HUB"}]}, {"field_name": "linenum", "field_type": "INTEGER", "targets": [{"table_name": "RSAP_CREDIT_MEMO_LINE_RIN1_HUB"}]}, {"field_name": "groupnum", "field_type": "INTEGER", "targets": [{"table_name": "RSAP_CREDIT_MEMO_LINE_RIN1_RIN2_DLNK"}]}, {"field_name": "json_text", "field_type": "VARCHAR", "targets": [{"table_name": "RSAP_CREDIT_MEMO_LINE_RIN1_RIN2_J1_L10_SAT", "exclude_json_paths_from_change_detection": ["UpdateDate"]}]}], "data_vault_model": [{"schema_name": "rvlt_sap", "tables": [{"table_name": "RSAP_CREDIT_MEMO_LINE_RIN1_HUB", "table_stereotype": "hub", "hub_key_column_name": "HK_RSAP_CREDIT_MEMO_LINE_RIN1"}, {"table_name": "RSAP_CREDIT_MEMO_LINE_RIN1_RIN2_DLNK", "table_stereotype": "lnk", "link_key_column_name": "LK_RSAP_CREDIT_MEMO_LINE_RIN1_RIN2", "link_parent_tables": ["RSAP_CREDIT_MEMO_LINE_RIN1_HUB"]}, {"table_name": "RSAP_CREDIT_MEMO_LINE_RIN1_RIN2_J1_L10_SAT", "table_stereotype": "sat", "satellite_parent_table": "RSAP_CREDIT_MEMO_LINE_RIN1_RIN2_DLNK", "diff_hash_column_name": "RH_RSAP_CREDIT_MEMO_LINE_RIN1_RIN2_J1_L10_SAT"}]}]}