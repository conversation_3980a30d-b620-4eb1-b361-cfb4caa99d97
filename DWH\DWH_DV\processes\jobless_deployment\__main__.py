"""
deploy view
deploy function
"""
import os
import csv
import codecs

from lib.cimtjobinstance import CimtJobInstance, cimtjobinstance_job
from lib.connection_snf import connection_snf_for_dwh_connection_type
from lib.dvf_basics import Dvf_dwh_connection_type

from lib.dvf_ddl_deploymentmanager_snf import Dvf_ddl_deploymentmanager_snf
from lib.configuration import configuration_load_ini
from pathlib import Path

VAR = 1

OBJECTS_DEPLOY_FAILED = []

def deploy_process(file_path, type, schema_name, name, mandantory):
    file_name = os.path.split(file_path)[1]

    try:
        deployment_manager = Dvf_ddl_deploymentmanager_snf(connection_snf_for_dwh_connection_type(Dvf_dwh_connection_type.owner))
        # print("^^^^^^ trying deploy of", file_name, type, schema_name, name)
        if type == 'table':
            deployment_manager.deploy_table(schema_name=schema_name, table_name=name)
        if type == 'view':
            deployment_manager.deploy_view(schema_name=schema_name, view_name=name)
        if type == 'function':
            deployment_manager.deploy_function(schema_name=schema_name, function_name=name)
        if type == 'testdata':
            deployment_manager.execute_testdata_insert(schema_name=schema_name, test_name=name)

    except Exception as err:
        print("^^^^^^ ERROR while deploying: ", type, schema_name, name, " from ", file_name)
        print(err)
        global VAR
        if mandantory == '0':
            VAR = 0
        global OBJECTS_DEPLOY_FAILED
        OBJECTS_DEPLOY_FAILED.append( type, schema_name, name, " from ", file_name)
        print("OBJECTS_DEPLOY_FAILED ================== ", OBJECTS_DEPLOY_FAILED)

        raise


def read_file(file_to_process):
    print(60 * "#", " file to process ", 60 * "#")
    print("*** vvvvvv", file_to_process, "vvvvvv ***")
    try:
        with codecs.open(file_to_process, "rb", "utf-8") as data:
            csvread = csv.reader(data, delimiter=',', quotechar='"')
            for row in csvread:
                if len(row) == 4:
                    mandantory = row[0]
                    type = row[1]
                    schema_name = row[2]
                    object = row[3]
                    if mandantory != '2':
                        deploy_process(file_to_process, type, schema_name, object, mandantory)
                else:
                    if len(row) > 0:
                        raise Exception("Malformed row :", row)

    except Exception as err:
        print('#### Error when reading object list file', err)
        raise


@cimtjobinstance_job
def main(file_not_to_deploy="all", parent_job_instance=None, **kwargs):
    """Check the unprocessed directory and load any present files into stage."""
    my_job_instance = CimtJobInstance(kwargs['instance_job_name'], parent_job_instance)
    my_job_instance.start_instance()

    try:
        if os.getenv('E_CONF_AVAILABLE') == 'YES' or os.getenv('E_CONF_AVAILABLE') == 'docker' or os.getenv(
                'E_CONF_AVAILABLE') == 'argo':
            ddl_root_path = "/datamodel"
        else:
            params = configuration_load_ini('dvf.ini', 'ddl_deployment')
            ddl_root_path = params['ddl_root_path']

        string_base_path = f"{ddl_root_path}/jobless_deployment".replace('$PYTHONPATH', os.getenv('PYTHONPATH'))
        base_path = Path(string_base_path)
        files_succesfully_full_deployed = []
        files_not_deployed = []
        count_files_with_error = 0
        for file in sorted(base_path.iterdir()):
            try:
                if os.stat(file).st_size != 0 and file.stem != 'template' and not file.stem.startswith(file_not_to_deploy):
                    read_file(file)
                    files_succesfully_full_deployed.append(file)
            except Exception:
                count_files_with_error += 1
                files_not_deployed.append(file)
                print(" #### ^^^^ ERROR while installing file: ", file)
                print("")

        print("\n", 20 * "#", "files whose items are successfully deployed: ", 20 * "#")
        for f in files_succesfully_full_deployed:
            print("\t", os.path.split(f)[1])

        if count_files_with_error > 0:
            print("\n", 30 * "-", 'number of files with error: ', count_files_with_error, 30 * "-")
            print(20 * "#", "files which can not be successfully deployed: ", 20 * "#")

            for f in files_not_deployed:
                print("\t\t\t\t\t\t", os.path.split(f)[1])
            print("\n")

            raise NameError('at least one file has error')

    except Exception:
        print(20*"#","some processing failed, check the messages above",20*"#",'\n')
        if VAR == 0:
            my_job_instance.end_instance_with_error(1, 'some processing failed')
            print('\n',10*"=-*","some views/functions which should deployed in production have to be checked ", 10*"=-*",)
            raise

    my_job_instance.end_instance()


if __name__ == '__main__':
    main()

