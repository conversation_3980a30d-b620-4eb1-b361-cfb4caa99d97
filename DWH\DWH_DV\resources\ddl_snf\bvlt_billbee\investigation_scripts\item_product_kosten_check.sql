
/* Orders mit totalcost 0*/
select o_s.ordernumber ,oi_s.totalprice,item_prod_l.hk_rbbe_product 
from RVLT_BILLBEE.RBBE_ORDER_ITEM_ORDER_LNK item_order_l 
join RVLT_BILLBEE.RBBE_ORDER_ITEM_ORDER_P1_L10_SAT oi_s
   on oi_s.hk_rbbe_order_item =item_order_l.hk_rbbe_order_item 
   and oi_s.md_valid_before =lib.dwh_far_future_date()
   and not oi_s.md_is_deleted 
join RVLT_BILLBEE.RBBE_ORDER_ORDER_P1_L10_SAT   o_s
   on o_s.hk_rbbe_order =item_order_l.hk_rbbe_order 
   and o_s.md_valid_before =lib.dwh_far_future_date()
   and not o_s.md_is_deleted 
join RVLT_BILLBEE.RBBE_ORDER_ITEM_PRODUCT_LNK item_prod_l
			on item_prod_l.hk_rbbe_order_item = item_order_l.hk_rbbe_order_item 
join RVLT_BILLBEE.RBBE_ORDER_ITEM_PRODUCT_ESAT item_prod_e
  on item_prod_e.lk_rbbe_order_item_product = item_prod_l.lk_rbbe_order_item_product 
	and item_prod_e.md_valid_before =lib.dwh_far_future_date()
	and not item_prod_e.md_is_deleted	   
where o_s.totalcost=0   
order by ordernumber ;

select item_order_l.hk_rbbe_order, ordernumber, o_s.totalcost ,o_s.shippingcost ,oi_s.totalprice ,oi_s.taxamount ,oi_s.totalprice -oi_s.taxamount netto,
from RVLT_BILLBEE.RBBE_ORDER_ITEM_ORDER_LNK item_order_l 
join RVLT_BILLBEE.RBBE_ORDER_ITEM_ORDER_P1_L10_SAT oi_s
   on oi_s.hk_rbbe_order_item =item_order_l.hk_rbbe_order_item 
   and oi_s.md_valid_before =lib.dwh_far_future_date()
   and not oi_s.md_is_deleted 
join RVLT_BILLBEE.RBBE_ORDER_ORDER_P1_L10_SAT   o_s
   on o_s.hk_rbbe_order =item_order_l.hk_rbbe_order 
   and o_s.md_valid_before =lib.dwh_far_future_date()
   and not o_s.md_is_deleted 
join RVLT_BILLBEE.RBBE_ORDER_ITEM_PRODUCT_LNK item_prod_l
			on item_prod_l.hk_rbbe_order_item = item_order_l.hk_rbbe_order_item 
join RVLT_BILLBEE.RBBE_ORDER_ITEM_PRODUCT_ESAT item_prod_e
  on item_prod_e.lk_rbbe_order_item_product = item_prod_l.lk_rbbe_order_item_product 
	and item_prod_e.md_valid_before =lib.dwh_far_future_date()
	and not item_prod_e.md_is_deleted	
--where item_prod_l.hk_rbbe_product ='0000000000000000000000000000'
where item_order_l.hk_rbbe_order='IaKpLGRbVpca0XZ4faJ8ts0Dcfk='   


/* Rohdatencount der Items */
select count(1) 
from RVLT_BILLBEE.RBBE_ORDER_ITEM_ORDER_P1_L10_SAT oi_s
   where oi_s.md_valid_before =lib.dwh_far_future_date()
   and not oi_s.md_is_deleted 
   and md_record_source <>'SYSTEM'


/* ORder, item basis und ergebnis*/
select  ordernumber
	,o_s.totalcost 								as order_totalcost
	,o_s.shippingcost 							as order_shippingcost
	,oi_s.totalprice 							as item_total_price 
	,oi_s.taxamount  							as item_tax_amount
	,case when item_prod_l.hk_rbbe_product<>'0000000000000000000000000000'
		 then true
		 else false
		 end									as item_mit_product
	,bbbe_product_kosten_korr.totalprice_netto  as b_item_totalprice_netto
	,bbbe_product_kosten_korr.zu_verteilender_wert_netto   as b_zu_verteilender_wert_netto
	,bbbe_product_kosten_korr.product_verteilschluessel  as b_product_verteilschluessel
	,round(bbbe_product_kosten_korr.totalprice_netto_korrigiert,2)  as b_totalprice_netto_korrigiert
	,o_h.billbeeorderid 
	,item_order_l.hk_rbbe_order
	,oi_s.hk_rbbe_order_item 
from RVLT_BILLBEE.RBBE_ORDER_ITEM_ORDER_LNK item_order_l 
join RVLT_BILLBEE.RBBE_ORDER_ITEM_ORDER_esat item_order_e
	on item_order_e.lk_rbbe_order_item_order = item_order_l.lk_rbbe_order_item_order 
   and item_order_e.md_valid_before =lib.dwh_far_future_date()
   and not item_order_e.md_is_deleted	
-- >>>> Business Vault Tabelle die wir testen   
left join  bvlt_billbee.bbbe_order_item_product_korrigiert_v1_b10_current_sat bbbe_product_kosten_korr
	on bbbe_product_kosten_korr.hk_rbbe_order_item = item_order_l.hk_rbbe_order_item 
-- <<<<
join RVLT_BILLBEE.RBBE_ORDER_ITEM_ORDER_P1_L10_SAT oi_s
   on oi_s.hk_rbbe_order_item =item_order_l.hk_rbbe_order_item  
   and oi_s.md_valid_before =lib.dwh_far_future_date()
   and not oi_s.md_is_deleted 
join RVLT_BILLBEE.RBBE_ORDER_ORDER_P1_L10_SAT   o_s
   on o_s.hk_rbbe_order =item_order_l.hk_rbbe_order 
   and o_s.md_valid_before =lib.dwh_far_future_date()
   and not o_s.md_is_deleted 
join RVLT_BILLBEE.rbbe_order_hub o_h   
	on o_h.hk_rbbe_order = item_order_l.hk_rbbe_order 
join RVLT_BILLBEE.RBBE_ORDER_ITEM_PRODUCT_LNK item_prod_l
	on item_prod_l.hk_rbbe_order_item = item_order_l.hk_rbbe_order_item 
join RVLT_BILLBEE.RBBE_ORDER_ITEM_PRODUCT_ESAT item_prod_e
  on item_prod_e.lk_rbbe_order_item_product = item_prod_l.lk_rbbe_order_item_product 
	and item_prod_e.md_valid_before =lib.dwh_far_future_date()
	and not item_prod_e.md_is_deleted   
--where item_order_l.hk_rbbe_order='IaKpLGRbVpca0XZ4faJ8ts0Dcfk='   
order by 1


