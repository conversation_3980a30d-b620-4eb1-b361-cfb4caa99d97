CREATE or replace VIEW bvlt_sap.bsap_credit_memo_line_rin1_korrigiert_v1_b10_current_sat AS

with gesamtnetto_current as (
	select head_s.hk_rsap_credit_memo_orin ,
		coalesce (head_s.doctotal,0)
		- coalesce (head_s.totalexpns,0)
		- coalesce (head_s.vatsum ,0)
		+ coalesce (head_s.discsum ,0)
		+ coalesce (head_s.dpmamnt ,0) as gesam<PERSON><PERSON>o
		,head_s.discsum 
		,case when head_s.canceled ='C'
			then -1
			else 1 end as  richtungsfaktor
	from rvlt_sap.rsap_credit_memo_orin_p1_l20_sat head_s 
	where head_s.md_valid_before =lib.dwh_far_future_date()
	and not  head_s.md_is_deleted 
)
,line_fipp_bonus_current as (
select 
		line_rin2_l.hk_rsap_credit_memo_line_rin1 ,
		sum(line_rin2_s.linetotal) rin2_linetotal_sum
from rvlt_sap.rsap_credit_memo_line_rin1_rin2_p1_l20_sat line_rin2_s
join rvlt_sap.rsap_credit_memo_line_rin1_rin2_dlnk line_rin2_l
    on line_rin2_l.lk_rsap_credit_memo_line_rin1_rin2 = line_rin2_s.lk_rsap_credit_memo_line_rin1_rin2 
join rvlt_sap.rsap_credit_memo_line_rin1_hub line_h
    on line_h.hk_rsap_credit_memo_line_rin1 = line_rin2_l.hk_rsap_credit_memo_line_rin1 
where  line_rin2_s.md_valid_before =lib.dwh_far_future_date()
		and not line_rin2_s.md_is_deleted 
		and line_h.company = 'FIPP_DE'
		and line_rin2_s.expnscode in (3,4)
group by 1		
)
,netto_anteil as (
	select 
		line_s.hk_rsap_credit_memo_line_rin1 ,
		gc.discsum,
		gc.richtungsfaktor,
		line_s.linetotal,
		case when gc.gesamtnetto<>0  
			then (line_s.linetotal+coalesce(lfpc.rin2_linetotal_sum,0))/ gc.gesamtnetto
			else 0 end 							as anteil_am_gesamtnetto,
	from gesamtnetto_current gc
	join rvlt_sap.rsap_credit_memo_line_rin1_credit_memo_orin_lnk head_line_l
	     on head_line_l.hk_rsap_credit_memo_orin =gc.hk_rsap_credit_memo_orin
	join rvlt_sap.rsap_credit_memo_line_rin1_p1_l20_sat line_s
		on line_s.hk_rsap_credit_memo_line_rin1 =head_line_l.hk_rsap_credit_memo_line_rin1 
		and line_s.md_valid_before =lib.dwh_far_future_date()
		and not line_s.md_is_deleted 
		and line_s.linetype ='R' 
	left join line_fipp_bonus_current lfpc
		on lfpc.hk_rsap_credit_memo_line_rin1=line_s.hk_rsap_credit_memo_line_rin1
)
-- finale struktur
select
	hk_rsap_credit_memo_line_rin1,
	anteil_am_gesamtnetto 										as anteil_am_gesamtnetto,
	case when discsum >0
		then linetotal-discsum*anteil_am_gesamtnetto
		else linetotal
	end * richtungsfaktor									as linetotal_korrigiert
from netto_anteil;

/*
 * select * from  bvlt_sap.bsap_credit_memo_line_rin1_korrigiert_v1_b10_current order by 3
 */