-- DROP VIEW rvlt_manual_data.rmud_rgnr_gesellschaft_monatsplanzahl_p1_l20_msat;

CREATE OR REPLACE VIEW rvlt_manual_data.rmud_rgnr_gesellschaft_monatsplanzahl_p1_l20_msat (
	MD_INSERTED_AT,
	MD_RUN_ID,
	MD_RECORD_SOURCE,
    MD_IS_DELETED,
    MD_VALID_BEFORE,
    LK_RMUD_RGNR_GESELLSCHAFT_MONATSPLANZAHL,
    GH_RMUD_RGNR_GESELLSCHAFT_MONATSPLANZAHL_P1_L10_MSAT,
    PLANZAHL,
    KUNDENGRUPPE,
    VERKAUFSGEBIET,
    VERKAEU<PERSON>ERNUMMER
)
AS
SELECT MD_INSERTED_AT,
	MD_RUN_ID,
	MD_RECORD_SOURCE,
    MD_IS_DELETED,
    MD_VALID_BEFORE,
    LK_RMUD_RGNR_GESELLSCHAFT_MONATSPLANZAHL,
    GH_RMUD_RGNR_GESELLSCHAFT_MONATSPLANZAHL_P1_L10_MSAT,
    PLANZAHL,
	COALESCE(JSON_EXTRACT_PATH_TEXT(untergruppe_json, '"kundengruppe:"'),'---') AS KUNDENGRUPPE,
	COALESCE(JSON_EXTRACT_PATH_TEXT(untergruppe_json, '"verkaufsgebiet:"'),'---') AS VERKAUFSGEBIET,
	COALESCE(JSON_EXTRACT_PATH_TEXT(untergruppe_json, '"verk\u00e4ufernummer:"'), '---') AS VERKAEUFERNUMMER
FROM rvlt_manual_data.rmud_rgnr_gesellschaft_monatsplanzahl_p1_l10_msat;