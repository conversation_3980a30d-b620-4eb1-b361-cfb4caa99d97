<mxfile host="Electron" agent="Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) draw.io/24.7.17 Chrome/128.0.6613.36 Electron/32.0.1 Safari/537.36" version="24.7.17">
  <diagram id="R2lEEEUBdFMjLlhIrx00" name="Page-1">
    <mxGraphModel dx="1356" dy="868" grid="1" gridSize="10" guides="1" tooltips="1" connect="1" arrows="1" fold="1" page="1" pageScale="1" pageWidth="850" pageHeight="1100" math="0" shadow="0" extFonts="Permanent Marker^https://fonts.googleapis.com/css?family=Permanent+Marker">
      <root>
        <mxCell id="0" />
        <mxCell id="1" parent="0" />
        <mxCell id="QJ2WdCTA2bv4eHh05B-j-1" value="f_umsatz" style="swimlane;childLayout=stackLayout;horizontal=1;startSize=50;horizontalStack=0;rounded=1;fontSize=14;fontStyle=0;strokeWidth=2;resizeParent=0;resizeLast=1;shadow=0;dashed=0;align=center;arcSize=4;whiteSpace=wrap;html=1;" parent="1" vertex="1">
          <mxGeometry x="290" y="60" width="210" height="270" as="geometry" />
        </mxCell>
        <mxCell id="QJ2WdCTA2bv4eHh05B-j-2" value="&lt;div&gt;gesellschaft&lt;/div&gt;&lt;div&gt;tagesdatum&lt;/div&gt;&lt;div&gt;&lt;br&gt;&lt;/div&gt;&lt;div&gt;&lt;span style=&quot;background-color: initial;&quot;&gt;verkaufsgebiet&lt;/span&gt;&lt;br&gt;&lt;/div&gt;&lt;div&gt;verkaeufernummer&lt;br&gt;&lt;/div&gt;&lt;div&gt;kundengruppe&lt;/div&gt;&lt;div&gt;&lt;br&gt;&lt;/div&gt;dk_kunde&lt;div&gt;&lt;br&gt;&lt;/div&gt;&lt;div&gt;&lt;div&gt;&lt;span style=&quot;background-color: initial;&quot;&gt;artikelgruppe&lt;/span&gt;&lt;br&gt;&lt;/div&gt;&lt;/div&gt;&lt;div&gt;&lt;br&gt;&lt;/div&gt;&lt;div&gt;umsatz&lt;/div&gt;&lt;div&gt;waehrung_iso&lt;/div&gt;&lt;div&gt;umsatz_eur&lt;/div&gt;" style="align=left;strokeColor=none;fillColor=none;spacingLeft=4;fontSize=12;verticalAlign=top;resizable=0;rotatable=0;part=1;html=1;" parent="QJ2WdCTA2bv4eHh05B-j-1" vertex="1">
          <mxGeometry y="50" width="210" height="220" as="geometry" />
        </mxCell>
        <mxCell id="QJ2WdCTA2bv4eHh05B-j-3" value="f_umsatzplanzahl" style="swimlane;childLayout=stackLayout;horizontal=1;startSize=50;horizontalStack=0;rounded=1;fontSize=14;fontStyle=0;strokeWidth=2;resizeParent=0;resizeLast=1;shadow=0;dashed=0;align=center;arcSize=4;whiteSpace=wrap;html=1;" parent="1" vertex="1">
          <mxGeometry x="530" y="60" width="280" height="190" as="geometry" />
        </mxCell>
        <mxCell id="QJ2WdCTA2bv4eHh05B-j-4" value="&lt;div&gt;gesellschaft&lt;/div&gt;&lt;div&gt;tagesdatum&amp;nbsp;&lt;/div&gt;&lt;div&gt;&lt;br&gt;&lt;/div&gt;&lt;div&gt;&lt;span style=&quot;background-color: initial;&quot;&gt;verkaufsgebiet&amp;nbsp;&lt;/span&gt;&lt;span style=&quot;background-color: initial;&quot;&gt;(*)&lt;/span&gt;&lt;br&gt;&lt;/div&gt;&lt;div&gt;verkaeufernummer (*)&lt;span style=&quot;background-color: initial;&quot;&gt;&lt;br&gt;&lt;/span&gt;&lt;/div&gt;&lt;div&gt;&lt;span style=&quot;background-color: initial;&quot;&gt;kundengruppe&amp;nbsp;&lt;/span&gt;&lt;span style=&quot;background-color: initial;&quot;&gt;(*)&lt;/span&gt;&lt;br&gt;&lt;/div&gt;&lt;div&gt;&lt;span style=&quot;background-color: initial;&quot;&gt;&lt;br&gt;&lt;/span&gt;&lt;/div&gt;&lt;div&gt;geplanter_umsatz_eur&lt;/div&gt;&lt;div&gt;&lt;br&gt;&lt;/div&gt;" style="align=left;strokeColor=none;fillColor=none;spacingLeft=4;fontSize=12;verticalAlign=top;resizable=0;rotatable=0;part=1;html=1;" parent="QJ2WdCTA2bv4eHh05B-j-3" vertex="1">
          <mxGeometry y="50" width="280" height="140" as="geometry" />
        </mxCell>
        <mxCell id="QJ2WdCTA2bv4eHh05B-j-5" value="d_geschaeftspartner" style="swimlane;childLayout=stackLayout;horizontal=1;startSize=50;horizontalStack=0;rounded=1;fontSize=14;fontStyle=0;strokeWidth=2;resizeParent=0;resizeLast=1;shadow=0;dashed=0;align=center;arcSize=4;whiteSpace=wrap;html=1;" parent="1" vertex="1">
          <mxGeometry x="50" y="130" width="160" height="210" as="geometry" />
        </mxCell>
        <mxCell id="QJ2WdCTA2bv4eHh05B-j-6" value="dk_kunde&amp;nbsp;&lt;div&gt;gesellschaft&lt;/div&gt;&lt;div&gt;&lt;span style=&quot;background-color: initial;&quot;&gt;name&lt;/span&gt;&lt;/div&gt;&lt;div&gt;&lt;span style=&quot;background-color: initial;&quot;&gt;kundengruppe&lt;/span&gt;&lt;/div&gt;&lt;div&gt;&lt;span style=&quot;background-color: initial;&quot;&gt;verkaufsgebiet&lt;/span&gt;&lt;/div&gt;&lt;div&gt;&lt;span style=&quot;background-color: initial;&quot;&gt;verkäufernummer&lt;/span&gt;&lt;/div&gt;&lt;div&gt;sap_cardcode&lt;/div&gt;&lt;div&gt;&lt;br&gt;&lt;/div&gt;&lt;div&gt;...&lt;/div&gt;" style="align=left;strokeColor=none;fillColor=none;spacingLeft=4;fontSize=12;verticalAlign=top;resizable=0;rotatable=0;part=1;html=1;" parent="QJ2WdCTA2bv4eHh05B-j-5" vertex="1">
          <mxGeometry y="50" width="160" height="160" as="geometry" />
        </mxCell>
        <mxCell id="QJ2WdCTA2bv4eHh05B-j-8" value="" style="edgeStyle=entityRelationEdgeStyle;fontSize=12;html=1;endArrow=ERzeroToMany;endFill=1;rounded=0;exitX=1;exitY=0.25;exitDx=0;exitDy=0;" parent="1" source="QJ2WdCTA2bv4eHh05B-j-6" target="QJ2WdCTA2bv4eHh05B-j-2" edge="1">
          <mxGeometry width="100" height="100" relative="1" as="geometry">
            <mxPoint x="370" y="500" as="sourcePoint" />
            <mxPoint x="470" y="400" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="QJ2WdCTA2bv4eHh05B-j-10" value="&lt;span style=&quot;text-wrap: nowrap;&quot;&gt;Mit (*) gekennzeichnete Attribute&amp;nbsp;&lt;/span&gt;&lt;div&gt;&lt;span style=&quot;text-wrap: nowrap; background-color: initial;&quot;&gt;werden nicht&amp;nbsp;&lt;/span&gt;&lt;span style=&quot;text-wrap: nowrap; background-color: initial;&quot;&gt;bei jeder&lt;/span&gt;&lt;/div&gt;&lt;div&gt;&lt;span style=&quot;text-wrap: nowrap; background-color: initial;&quot;&gt;Gesellschaft für die Gliederung&lt;/span&gt;&lt;/div&gt;&lt;div&gt;&lt;div style=&quot;&quot;&gt;&lt;span style=&quot;text-wrap: nowrap;&quot;&gt;der Planzahlen genutzt.&lt;/span&gt;&lt;/div&gt;&lt;/div&gt;" style="shape=note;size=20;whiteSpace=wrap;html=1;align=left;spacingLeft=9;" parent="1" vertex="1">
          <mxGeometry x="545" y="270" width="250" height="100" as="geometry" />
        </mxCell>
      </root>
    </mxGraphModel>
  </diagram>
</mxfile>
