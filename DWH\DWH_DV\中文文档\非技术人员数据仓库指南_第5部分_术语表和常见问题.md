# 数据仓库项目指南：非技术人员版（第5部分：术语表和常见问题）

## 术语表

为了帮助您更好地理解数据仓库项目中使用的术语，我们编制了以下术语表：

### 一般术语

| 术语 | 解释 |
|------|------|
| 数据仓库 | 一个集中存储和管理企业所有数据的系统，用于分析和报告 |
| ETL | 提取(Extract)、转换(Transform)、加载(Load)的缩写，指将数据从源系统移动到数据仓库的过程 |
| ELT | 提取(Extract)、加载(Load)、转换(Transform)的缩写，与ETL类似，但先加载数据再转换 |
| 源系统 | 提供原始数据的系统，如SAP、Billbee等 |
| 元数据 | 关于数据的数据，如数据来源、加载时间等 |
| 业务键 | 在业务上唯一标识实体的值，如订单号、客户ID等 |

### Data Vault相关术语

| 术语 | 解释 |
|------|------|
| Data Vault | 一种数据建模方法，将数据分解为Hub、Link和Satellite |
| Hub | 代表业务核心实体的表，如客户、产品、订单等 |
| Link | 表示实体之间关系的表 |
| Satellite | 存储与Hub或Link相关描述性信息的表，并跟踪历史变化 |
| 哈希键 | 基于业务键生成的唯一标识符，用于连接不同的表 |
| 差异哈希 | 用于检测数据变化的哈希值 |
| Raw Vault | 存储未经处理的原始数据的层 |
| Business Vault | 应用业务规则和转换的层 |
| Data Mart | 面向特定业务领域的数据视图层 |

### 项目特定术语

| 术语 | 解释 |
|------|------|
| DVPD | Data Vault Pipeline Definition，定义如何将源数据映射到Data Vault模型的配置文件 |
| DVPI | Data Vault Pipeline Implementation，DVPD的实现细节 |
| L10 | Load Layer 10，原始数据加载层 |
| L20 | Load Layer 20，处理后数据层 |
| SAT | Satellite的缩写，表示卫星表 |
| MSAT | Multi-active Satellite的缩写，表示多活动卫星表 |
| ESAT | Effectivity Satellite的缩写，表示有效性卫星表 |
| HUB | Hub的缩写，表示中心表 |
| LNK | Link的缩写，表示链接表 |
| DLNK | Dependent Link的缩写，表示依赖链接表 |

### 模式名称前缀

| 前缀 | 解释 |
|------|------|
| RVLT_ | Raw Vault，原始保管库 |
| BVLT_ | Business Vault，业务保管库 |
| MART_ | Data Mart，数据集市 |
| STAGE_ | Staging，暂存区 |

### 表名前缀

| 前缀 | 解释 |
|------|------|
| RSAP_ | SAP数据的Raw Vault表 |
| RBBE_ | Billbee数据的Raw Vault表 |
| RMUD_ | 手动数据的Raw Vault表 |
| RGNR_ | 通用数据的Raw Vault表 |
| BSAP_ | SAP数据的Business Vault表 |
| BBBE_ | Billbee数据的Business Vault表 |
| BGNR_ | 通用数据的Business Vault表 |
| F_ | 事实表（Fact table） |
| D_ | 维度表（Dimension table） |

## 常见问题

### 1. 什么是Data Vault，为什么我们选择它？

**回答**：Data Vault是一种数据建模方法，它将数据分解为Hub（中心）、Link（链接）和Satellite（卫星）。我们选择Data Vault的原因包括：

- **灵活性**：可以轻松适应业务变化
- **可追溯性**：保留完整的历史数据和数据来源
- **可扩展性**：可以轻松添加新的数据源和业务规则
- **并行开发**：不同团队可以同时处理不同部分

### 2. 数据仓库与源系统（如SAP）有什么区别？

**回答**：源系统（如SAP）主要用于日常业务操作，而数据仓库专为分析和报告设计。主要区别包括：

- **目的**：源系统处理交易，数据仓库支持分析
- **数据组织**：源系统针对快速交易处理优化，数据仓库针对复杂查询优化
- **历史数据**：源系统通常只保留当前数据，数据仓库保留历史数据
- **数据整合**：数据仓库整合来自多个源系统的数据

### 3. Raw Vault、Business Vault和Data Mart有什么区别？

**回答**：这三个层次在数据处理和业务应用方面有不同的角色：

- **Raw Vault**：存储未经处理的原始数据，保持数据的原始状态
- **Business Vault**：应用业务规则和转换，如调整发票行的成本
- **Data Mart**：创建面向特定业务领域的数据视图，如销售报表

### 4. 为什么数据在多个表中，而不是一个大表？

**回答**：将数据分散在多个表中（如Hub、Link和Satellite）有几个优点：

- **减少冗余**：每个实体只存储一次
- **提高灵活性**：可以轻松添加新的关系和属性
- **保留历史**：可以跟踪属性随时间的变化
- **提高性能**：可以只查询需要的数据

### 5. 如何请求新的报表或分析？

**回答**：如果您需要新的报表或分析，请按照以下步骤操作：

1. 明确定义您的业务需求和问题
2. 确定所需的数据元素（如日期、产品、客户、金额等）
3. 指定所需的过滤条件和计算
4. 提交请求给数据团队
5. 与数据团队合作，确保报表满足您的需求

### 6. 数据更新的频率是多少？

**回答**：不同数据源的更新频率可能不同：

- **SAP数据**：通常每天更新一次
- **Billbee数据**：通常每小时更新一次
- **手动数据**（如销售计划）：根据需要手动更新

### 7. 如何确保数据的准确性？

**回答**：我们采取多种措施确保数据的准确性：

- **数据验证**：在加载过程中验证数据格式和值
- **数据审计**：定期检查数据的完整性和一致性
- **源系统对账**：将数据仓库中的数据与源系统进行对比
- **业务规则验证**：应用业务规则检查数据的合理性

### 8. 我可以直接访问数据仓库吗？

**回答**：通常，业务用户不直接访问数据仓库，而是通过报表和分析工具访问数据。这是因为：

- 数据仓库结构复杂，需要技术知识才能正确查询
- 报表和分析工具提供了更友好的界面
- 可以实施适当的安全控制和数据访问权限

如果您需要特定数据，请联系数据团队，他们可以帮助您获取所需信息。

### 9. 什么是DVPD和DVPI，它们有什么用？

**回答**：DVPD（Data Vault Pipeline Definition）和DVPI（Data Vault Pipeline Implementation）是自动化数据加载过程的工具：

- **DVPD**：定义如何将源数据映射到Data Vault模型的配置文件
- **DVPI**：DVPD的实现细节，包含执行映射的具体步骤

这些工具帮助我们标准化和自动化数据加载过程，减少错误并提高效率。

### 10. 如何理解数据模型图中的不同形状和颜色？

**回答**：在数据模型图中，不同的形状和颜色代表不同类型的表：

- **深蓝色六边形**：Hub表，代表业务核心实体
- **浅蓝色六边形**：Link表，表示实体之间的关系
- **黄色矩形**：Satellite表，存储描述性信息和历史变化

这种可视化方式帮助我们理解数据结构和关系。
