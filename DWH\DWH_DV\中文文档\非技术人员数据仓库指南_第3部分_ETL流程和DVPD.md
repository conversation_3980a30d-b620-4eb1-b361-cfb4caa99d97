# 数据仓库项目指南：非技术人员版（第3部分：ETL流程和DVPD）

## ETL流程：数据如何从源系统到达数据仓库

ETL（提取-转换-加载）是将数据从源系统移动到数据仓库的过程。在我们的项目中，这个过程更准确地称为ELT（提取-加载-转换），因为我们首先加载原始数据，然后在数据仓库内部进行转换。

### 数据流程概述

1. **提取（Extract）**：从源系统（如SAP、Billbee）获取数据
2. **加载（Load）**：将原始数据加载到Raw Vault（原始保管库）
3. **转换（Transform）**：在Business Vault（业务保管库）中应用业务规则
4. **集成（Integrate）**：在Data Mart（数据集市）中整合数据供分析使用

### 以SAP发票数据为例

让我们以SAP发票数据为例，说明整个流程：

1. **提取**：
   - 通过Lobster工具从SAP数据库提取发票数据（表OINV、INV1、INV2等）
   - 数据以JSON格式存储在Azure Blob Storage中

2. **加载到Raw Vault**：
   - 数据被加载到Raw Vault的Hub、Link和Satellite表中
   - 例如：`RSAP_INVOICE_LINE_INV1_HUB`、`RSAP_INVOICE_LINE_INV1_J1_L10_SAT`等

3. **转换在Business Vault中进行**：
   - 应用业务规则，如调整发票行的成本
   - 例如：`BSAP_INVOICE_LINE_INV1_KORRIGIERT_V1_B10_CURRENT_SAT`

4. **集成到Data Mart**：
   - 创建面向业务的视图，如销售报表
   - 例如：`MART_AUFTRAEGE.F_UMSATZ`（销售额事实表）

## DVPD和DVPI：自动化数据加载

在我们的项目中，我们使用DVPD（Data Vault Pipeline Definition）和DVPI（Data Vault Pipeline Implementation）来自动化数据加载过程。

### DVPD：Data Vault Pipeline Definition

DVPD是一个JSON格式的配置文件，它定义了如何将源数据映射到Data Vault模型。它包含以下主要部分：

1. **管道名称和记录源**：标识数据来源
2. **数据提取配置**：如何从源系统获取数据
3. **字段映射**：源字段如何映射到目标表
4. **Data Vault模型定义**：目标表的结构和关系

**示例**：以下是`rsap_inv1_j1.dvpd.json`的简化版本：

```json
{
  "dvpd_version": "0.6.2",
  "pipeline_name": "rsap_inv1_j1",
  "record_source_name_expression": "sap.inv1",
  "data_extraction": {
    "fetch_module_name": "lobster",
    "parse_module_name": "json",
    "load_module_name": "python_framework"
  },
  "fields": [
    {
      "field_name": "COMPANY",
      "field_type": "Varchar(50)",
      "targets": [
        {"table_name": "RSAP_INVOICE_LINE_INV1_HUB"},
        {"table_name": "RSAP_ITEM_OITM_HUB"}
      ]
    },
    {
      "field_name": "DOCENTRY",
      "field_type": "INTEGER",
      "targets": [{"table_name": "RSAP_INVOICE_LINE_INV1_HUB"}]
    },
    {
      "field_name": "LINENUM",
      "field_type": "INTEGER",
      "targets": [{"table_name": "RSAP_INVOICE_LINE_INV1_HUB"}]
    },
    {
      "field_name": "ITEMCODE",
      "field_type": "Varchar(50)",
      "targets": [{"table_name": "RSAP_ITEM_OITM_HUB"}]
    },
    {
      "field_name": "JSON_TEXT",
      "field_type": "VARCHAR",
      "targets": [{"table_name": "RSAP_INVOICE_LINE_INV1_J1_L10_SAT"}]
    }
  ],
  "data_vault_model": [
    {
      "schema_name": "rvlt_sap",
      "tables": [
        {
          "table_name": "RSAP_INVOICE_LINE_INV1_HUB",
          "table_stereotype": "hub",
          "hub_key_column_name": "HK_RSAP_INVOICE_LINE_INV1"
        },
        {
          "table_name": "RSAP_INVOICE_LINE_INV1_J1_L10_SAT",
          "table_stereotype": "sat",
          "satellite_parent_table": "RSAP_INVOICE_LINE_INV1_HUB",
          "diff_hash_column_name": "RH_RSAP_INVOICE_LINE_INV1_J1_L10_SAT"
        },
        {
          "table_name": "RSAP_ITEM_OITM_HUB",
          "table_stereotype": "hub",
          "hub_key_column_name": "HK_RSAP_ITEM_OITM"
        }
      ]
    }
  ]
}
```

### DVPI：Data Vault Pipeline Implementation

DVPI是DVPD的实现细节，它包含了如何执行DVPD中定义的映射。在我们的项目中，DVPI文件生成了开发人员备忘单（devsheet.txt），帮助开发人员理解数据流。

**示例**：以下是`rsap_inv1_j1.devsheet.txt`的部分内容：

```
Data vault pipeline developer cheat sheet 
rendered from rsap_inv1_j1.dvpi

pipeline name: rsap_inv1_j1

------------------------------------------------------
record source: sap.inv1

Source fields:
       COMPANY    Varchar(50)
       DOCENTRY   INTEGER
       LINENUM    INTEGER
       ITEMCODE   Varchar(50)
       JSON_TEXT  VARCHAR

------------------------------------------------------
Table List:
stage_table.rvlt_sap.rsap_inv1_j1_stage
table.rvlt_sap.rsap_invoice_line_inv1_hub
table.rvlt_sap.rsap_invoice_line_inv1_j1_l10_sat
table.rvlt_sap.rsap_item_oitm_hub
```

## 以截图中的例子详解

您提供的截图显示了SAP发票数据的一部分Data Vault模型，特别是关于`invoice_line_inv1`（发票行）、`inv2`（发票行扩展）和相关表的关系。

![SAP发票数据模型](../documentation/rvlt_sap.drawio.png)

### 模型解释

在这个模型中：

1. **中心实体（深蓝色六边形）**：
   - `invoice_line_inv1`：代表发票行，由公司代码、文档编号和行号唯一标识

2. **关系（浅蓝色六边形）**：
   - `invoice_line_inv1_inv2`：发票行与其扩展数据的关系
   - `invoice_line_inv1_invoice`：发票行与发票头的关系
   - `invoice_line_inv1_item_oitm`：发票行与物料的关系

3. **属性（黄色矩形）**：
   - `invoice_line_inv1_j1_l10`：发票行的原始JSON数据
   - `invoice_line_inv1_p1_l20`：发票行的处理后数据
   - `invoice_line_inv1_inv2_j1_l10`：发票行扩展的原始JSON数据
   - `invoice_line_inv1_inv2_p1_l20`：发票行扩展的处理后数据

### 数据流程示例

让我们通过一个具体的例子来说明数据如何在这个模型中流动：

1. **源数据**：SAP系统中的一个发票包含两行：
   - 行1：100个产品A，单价10欧元，总价1000欧元
   - 行2：50个产品B，单价20欧元，总价1000欧元
   - 发票有10%的整体折扣，存储在INV2表中

2. **Raw Vault加载**：
   - 发票行数据加载到`RSAP_INVOICE_LINE_INV1_HUB`和`RSAP_INVOICE_LINE_INV1_J1_L10_SAT`
   - 发票行扩展数据（折扣信息）加载到`RSAP_INVOICE_LINE_INV1_INV2_J1_L10_SAT`
   - 产品数据加载到`RSAP_ITEM_OITM_HUB`和相关Satellite
   - 关系信息加载到相应的Link表

3. **Business Vault转换**：
   - 计算每行的折扣分摊：行1分摊100欧元折扣，行2分摊100欧元折扣
   - 计算调整后的行总价：行1为900欧元，行2为900欧元
   - 结果存储在`BSAP_INVOICE_LINE_INV1_KORRIGIERT_V1_B10_CURRENT_SAT`

4. **Data Mart集成**：
   - 创建`F_UMSATZ`（销售额事实表），包含日期、产品、客户、金额等维度
   - 用户可以按不同维度分析销售数据，如按产品、客户、日期等

通过这种方式，业务用户可以获得准确的销售报表，而不必担心底层数据的复杂性。
