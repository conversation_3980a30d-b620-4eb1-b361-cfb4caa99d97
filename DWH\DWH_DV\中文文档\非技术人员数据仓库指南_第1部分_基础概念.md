# 数据仓库项目指南：非技术人员版（第1部分：基础概念）

## 引言

本文档旨在帮助没有IT背景的业务人员理解我们的数据仓库项目。我们将使用简单的语言和实例来解释复杂的概念，让您能够理解数据如何从源系统（如SAP和Billbee）流向报表和分析工具。

## 什么是数据仓库？

数据仓库是一个集中存储和管理企业所有数据的系统。它将来自不同源系统（如SAP、Billbee等）的数据整合在一起，以便于分析和报告。

想象一下，数据仓库就像一个大型图书馆：
- 不同的源系统（如SAP、Billbee）就像不同的书籍供应商
- 数据仓库就是图书馆本身，负责收集、整理和存储所有书籍
- 业务用户就像图书馆的读者，可以查阅需要的信息

## 我们项目的架构

我们的数据仓库项目基于以下技术和架构：

1. **存储平台**：Snowflake（云数据库）和Azure（微软云服务）
2. **建模方法**：Data Vault 2.0（一种灵活的数据建模方法）
3. **数据源**：
   - SAP Business One（企业资源规划系统）
   - Billbee（电子商务订单管理系统）
   - 手动输入的数据（如Excel表格中的销售计划数据）

## Data Vault 2.0：简单解释

Data Vault 2.0是一种数据建模方法，它将数据分解为三种基本组件：

1. **Hub（中心）**：代表业务中的核心实体，如客户、产品、订单等
2. **Link（链接）**：表示实体之间的关系，如客户下了订单、订单包含产品等
3. **Satellite（卫星）**：存储与Hub或Link相关的描述性信息和历史变化

这种方法的优点是：
- 能够轻松适应业务变化
- 保留完整的历史数据
- 可以追踪数据的来源和变化

## 数据流程：从源系统到报表

在我们的项目中，数据按照以下流程处理：

1. **提取（Extract）**：从源系统（如SAP、Billbee）获取原始数据
2. **加载（Load）**：将原始数据加载到Raw Vault（原始保管库）
3. **转换（Transform）**：在Business Vault（业务保管库）中应用业务规则和转换
4. **集成（Integrate）**：在Data Mart（数据集市）中整合数据，使其易于分析和报告

这个过程通常被称为ETL（提取-转换-加载）或ELT（提取-加载-转换）。

## 项目中的层次结构

我们的数据仓库分为几个主要层次：

1. **Raw Vault（原始保管库）**：存储未经处理的原始数据
   - 模式名称前缀：RVLT_（如RVLT_SAP、RVLT_BILLBEE）
   
2. **Business Vault（业务保管库）**：应用业务规则和转换
   - 模式名称前缀：BVLT_（如BVLT_SAP、BVLT_GENERAL）
   
3. **Data Mart（数据集市）**：面向特定业务领域的数据视图
   - 模式名称前缀：MART_（如MART_AUFTRAEGE）

在接下来的部分中，我们将详细解释每个层次，并使用实际例子来说明数据如何在这些层次之间流动。
