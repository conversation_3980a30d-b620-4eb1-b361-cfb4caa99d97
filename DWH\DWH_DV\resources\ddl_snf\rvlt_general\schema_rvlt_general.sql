/*
 * Schema: rvlt_general
 * 
 * 描述：
 * 此模式包含通用数据的原始数据仓库表（Raw Vault Tables）。
 * 它存储跨系统使用的通用数据，如公司信息、日历数据等基础参考数据。
 * 
 * 包含的表类型：
 * - Hub表：存储通用实体的唯一标识符
 * - Link表：存储通用实体之间的关系
 * - Satellite表：存储通用实体的描述性属性和历史变化
 * - 其他支持表和视图
 */

-- DROP SCHEMA rvlt_general;

CREATE SCHEMA if not EXISTS rvlt_general;

COMMENT ON SCHEMA rvlt_general
  IS 'Contains raw vault tables for general data across systems, including company information and calendar data';

