-- generated script for stage_rvlt.rbbe_order_orderitem_p1_stage

-- DROP TABLE stage_rvlt.rbbe_order_orderitem_p1_stage;

CREATE TABLE stage_rvlt.rbbe_order_orderitem_p1_stage (
--metadata,
MD_INSERTED_AT TIMESTAMP NOT NULL,
MD_RUN_ID INT NOT NULL,
MD_RECORD_SOURCE VARCHAR(255) NOT NULL,
MD_IS_DELETED BOOLEAN NOT NULL,
--hash keys,
HK_RBBE_ORDER CHAR(28) NOT NULL,
HK_RBBE_ORDER_ITEM CHAR(28) NOT NULL,
HK_RBBE_PRODUCT CHAR(28) NOT NULL,
LK_RBBE_ORDER_ITEM_ORDER CHAR(28) NOT NULL,
LK_RBBE_ORDER_ITEM_PRODUCT CHAR(28) NOT NULL,
RH_RBBE_ORDER_ITEM_ORDER_P1_L10_SAT CHAR(28) NOT NULL,
--business keys,
BILLBEEID NUMBER(36,0) NULL,
B<PERSON>L<PERSON>EORDERID NUMBER(36,0) NULL,
PRODUCT_BILLBEEID NUMBER(36,0) NULL,
--content untracked,
LASTMODIFIEDAT TIMESTAMP NULL,
--content,
DISCOUNT NUMBER(20,2) NULL,
INVOICESKU VARCHAR(200) NULL,
PRODUCT_COUNTRYOFORIGIN VARCHAR(200) NULL,
PRODUCT_EAN VARCHAR(200) NULL,
PRODUCT_SKU VARCHAR(200) NULL,
PRODUCT_TARICCODE VARCHAR(200) NULL,
PRODUCT_TITLE VARCHAR(1000) NULL,
QUANTITY NUMBER(20,2) NULL,
TAXAMOUNT NUMBER(20,2) NULL,
TAXINDEX NUMBER(20,2) NULL,
TOTALPRICE NUMBER(20,2) NULL,
UNREBATEDTOTALPRICE NUMBER(20,2) NULL
);
-- end of script --