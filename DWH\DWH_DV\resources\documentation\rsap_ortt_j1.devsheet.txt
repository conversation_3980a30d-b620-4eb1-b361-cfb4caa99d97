Data vault pipeline developer cheat sheet 
rendered from  rsap_ortt_j1.dvpi

pipeline name:  rsap_ortt_j1

------------------------------------------------------
record source:  sap.ortt

Source fields:
       COMPANY    VARCHAR(50)
       CURRENCY   VARCHAR(3)
       RATEDATE   DATE
       JSON_TEXT  VARCHAR


------------------------------------------------------
Table List:
stage_table.rvlt_sap.rsap_ortt_j1_stage
table.rvlt_sap.rsap_currency_ortt_hub
table.rvlt_sap.rsap_currency_ortt_exchange_rate_dlnk
table.rvlt_sap.rsap_currency_ortt_exchange_rate_j1_l10_sat

------------------------------------------------------
stage table:  stage_rvlt.rsap_ortt_j1_stage
Field to Stage mapping:
	--business keys,
		COMPANY    >  COMPANY,
		CURRENCY   >  CURRENCY,
		RATEDATE   >  RATEDATE,

	--content,
		JSON_TEXT  >  JSON_TEXT

------------------------------------------------------
Hash value composition

HK_RSAP_CURRENCY_ORTT (key)
		COMPANY 
		CURRENCY 

LK_RSAP_ORTT_EXCHANGE_RATE_DLNK (key)
		RATEDATE 
		COMPANY 
		CURRENCY 

RH_RSAP_CURRENCY_ORTT_EXCHANGE_RATE_J1_L10_SAT (diff_hash)
		JSON_TEXT 


------------------------------------------------------
Table load method
(STAGE >  VAULT)

rsap_currency_ortt_hub (/) can be loaded by convention
		  key: HK_RSAP_CURRENCY_ORTT  >  HK_RSAP_CURRENCY_ORTT
		  business_key: COMPANY  >  COMPANY 
		  business_key: CURRENCY  >  CURRENCY 

rsap_currency_ortt_exchange_rate_dlnk (/) can be loaded by convention
		  parent_key_1: HK_RSAP_CURRENCY_ORTT  >  HK_RSAP_CURRENCY_ORTT
		  key: LK_RSAP_ORTT_EXCHANGE_RATE_DLNK  >  LK_RSAP_ORTT_EXCHANGE_RATE_DLNK
		  dependent_child_key: RATEDATE  >  RATEDATE 

rsap_currency_ortt_exchange_rate_j1_l10_sat (/) can be loaded by convention
		  parent_key: LK_RSAP_ORTT_EXCHANGE_RATE_DLNK  >  LK_RSAP_ORTT_EXCHANGE_RATE_DLNK
		  diff_hash: RH_RSAP_CURRENCY_ORTT_EXCHANGE_RATE_J1_L10_SAT  >  RH_RSAP_CURRENCY_ORTT_EXCHANGE_RATE_J1_L10_SAT
		  content: JSON_TEXT  >  JSON_TEXT 

