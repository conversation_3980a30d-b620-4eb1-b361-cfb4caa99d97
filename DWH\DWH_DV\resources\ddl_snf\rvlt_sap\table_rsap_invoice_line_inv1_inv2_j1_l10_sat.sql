/*
 * 表名: rvlt_sap.rsap_invoice_line_inv1_inv2_j1_l10_sat
 * 
 * 描述：
 * 此表是SAP发票行项目依赖Link的卫星表，存储依赖关系的描述性属性。
 * 作为Data Vault模型中的卫星表，它保存依赖关系的属性和历史变化。
 * 每条记录代表发票行与其他相关数据之间依赖关系在特定时间点的属性状态。
 * 
 * 字段说明：
 * - MD_INSERTED_AT: 记录插入时间戳，表示数据何时被加载到卫星表中
 * - MD_RUN_ID: 加载过程的运行ID，用于跟踪ETL过程
 * - MD_RECORD_SOURCE: 数据来源系统，标识数据的来源
 * - MD_IS_DELETED: 标识记录是否被删除的标志
 * - MD_VALID_BEFORE: 记录有效期结束时间戳，用于历史追踪
 * - LK_RSAP_INVOICE_LINE_INV1_INV2: 关联到依赖Link表的哈希键
 * - RH_RSAP_INVOICE_LINE_INV1_INV2_J1_L10_SAT: 卫星记录的哈希键，用于唯一标识属性集
 * - JSON_TEXT: 存储依赖关系的详细属性，以JSON格式保存
 * 
 * 相关表：
 * - rsap_invoice_line_inv1_inv2_dlnk: 发票行的依赖Link表
 * - rsap_invoice_line_inv1_hub: 发票行的Hub表
 * 
 * 在Data Vault模型中的作用：
 * 此卫星表存储发票行依赖关系的所有描述性属性，并通过时间戳跟踪这些属性的历史变化。
 * 它与依赖Link表一起，提供了发票行与其他业务概念关联的完整视图，支持复杂的业务分析。
 */

-- DROP TABLE rvlt_sap.rsap_invoice_line_inv1_inv2_j1_l10_sat;

CREATE TABLE rvlt_sap.rsap_invoice_line_inv1_inv2_j1_l10_sat (
MD_INSERTED_AT TIMESTAMP NOT NULL,
MD_RUN_ID INT NOT NULL,
MD_RECORD_SOURCE VARCHAR(255) NOT NULL,
MD_IS_DELETED BOOLEAN NOT NULL,
MD_VALID_BEFORE TIMESTAMP NOT NULL,
LK_RSAP_INVOICE_LINE_INV1_INV2 CHAR(28) NOT NULL,
RH_RSAP_INVOICE_LINE_INV1_INV2_J1_L10_SAT CHAR(28) NOT NULL,
JSON_TEXT VARCHAR NULL
);

--COMMENT STATEMENTS

-- end of script --