{"dvdp_compiler": "dvpdc reference compiler,  release 0.6.2", "dvpi_version": "0.6.2", "compile_timestamp": "2024-11-14 16:08:15", "dvpd_version": "0.6.2", "pipeline_name": "rbbe_ORDER_P1", "dvpd_filename": "rbbe_order_p1.dvpd.json", "tables": [{"table_name": "rbbe_order_hub", "table_stereotype": "hub", "schema_name": "rvlt_billbee", "storage_component": "", "columns": [{"column_name": "MD_INSERTED_AT", "is_nullable": false, "column_class": "meta_load_date", "column_type": "TIMESTAMP"}, {"column_name": "MD_RUN_ID", "is_nullable": false, "column_class": "meta_load_process_id", "column_type": "INT"}, {"column_name": "MD_RECORD_SOURCE", "is_nullable": false, "column_class": "meta_record_source", "column_type": "VARCHAR(255)"}, {"column_name": "HK_RBBE_ORDER", "is_nullable": false, "column_class": "key", "column_type": "CHAR(28)"}, {"column_name": "BILLBEEORDERID", "is_nullable": true, "column_class": "business_key", "column_type": "NUMBER(36,0)", "prio_for_column_position": 50000}]}, {"table_name": "rbbe_order_order_p1_l10_sat", "table_stereotype": "sat", "schema_name": "rvlt_billbee", "storage_component": "", "has_deletion_flag": true, "is_effectivity_sat": false, "is_enddated": true, "is_multiactive": false, "compare_criteria": "key+current", "uses_diff_hash": true, "columns": [{"column_name": "MD_INSERTED_AT", "is_nullable": false, "column_class": "meta_load_date", "column_type": "TIMESTAMP"}, {"column_name": "MD_RUN_ID", "is_nullable": false, "column_class": "meta_load_process_id", "column_type": "INT"}, {"column_name": "MD_RECORD_SOURCE", "is_nullable": false, "column_class": "meta_record_source", "column_type": "VARCHAR(255)"}, {"column_name": "MD_IS_DELETED", "is_nullable": false, "column_class": "meta_deletion_flag", "column_type": "BOOLEAN"}, {"column_name": "MD_VALID_BEFORE", "is_nullable": false, "column_class": "meta_load_enddate", "column_type": "TIMESTAMP"}, {"column_name": "HK_RBBE_ORDER", "is_nullable": false, "column_class": "parent_key", "column_type": "CHAR(28)", "parent_key_column_name": "HK_RBBE_ORDER", "parent_table_name": "rbbe_order_hub"}, {"column_name": "RH_RBBE_ORDER_ORDER_P1_L10_SAT", "is_nullable": false, "column_class": "diff_hash", "column_type": "CHAR(28)"}, {"column_name": "ORDERNUMBER", "is_nullable": true, "column_class": "content", "column_type": "VARCHAR(200)", "exclude_from_change_detection": false, "prio_for_column_position": 50000}, {"column_name": "TOTALCOST", "is_nullable": true, "column_class": "content", "column_type": "NUMBER(20,2)", "exclude_from_change_detection": false, "prio_for_column_position": 50000}, {"column_name": "ADJUSTMENTCOST", "is_nullable": true, "column_class": "content", "column_type": "NUMBER(20,2)", "exclude_from_change_detection": false, "prio_for_column_position": 50000}, {"column_name": "CURRENCY", "is_nullable": true, "column_class": "content", "column_type": "VARCHAR(200)", "exclude_from_change_detection": false, "prio_for_column_position": 50000}, {"column_name": "SHIPPINGCOST", "is_nullable": true, "column_class": "content", "column_type": "NUMBER(20,2)", "exclude_from_change_detection": false, "prio_for_column_position": 50000}, {"column_name": "INVOICEADDRESS_COUNTRYISO2", "is_nullable": true, "column_class": "content", "column_type": "VARCHAR(20)", "exclude_from_change_detection": false, "prio_for_column_position": 50000}, {"column_name": "UPDATEDAT", "is_nullable": true, "column_class": "content_untracked", "column_type": "TIMESTAMP", "exclude_from_change_detection": "true", "prio_for_column_position": 50000}, {"column_name": "LASTMODIFIEDAT", "is_nullable": true, "column_class": "content_untracked", "column_type": "TIMESTAMP", "exclude_from_change_detection": "true", "prio_for_column_position": 50000}]}, {"table_name": "rbbe_order_order_p2_l10_sat", "table_stereotype": "sat", "schema_name": "rvlt_billbee", "storage_component": "", "has_deletion_flag": true, "is_effectivity_sat": false, "is_enddated": true, "is_multiactive": false, "compare_criteria": "key+current", "uses_diff_hash": true, "columns": [{"column_name": "MD_INSERTED_AT", "is_nullable": false, "column_class": "meta_load_date", "column_type": "TIMESTAMP"}, {"column_name": "MD_RUN_ID", "is_nullable": false, "column_class": "meta_load_process_id", "column_type": "INT"}, {"column_name": "MD_RECORD_SOURCE", "is_nullable": false, "column_class": "meta_record_source", "column_type": "VARCHAR(255)"}, {"column_name": "MD_IS_DELETED", "is_nullable": false, "column_class": "meta_deletion_flag", "column_type": "BOOLEAN"}, {"column_name": "MD_VALID_BEFORE", "is_nullable": false, "column_class": "meta_load_enddate", "column_type": "TIMESTAMP"}, {"column_name": "HK_RBBE_ORDER", "is_nullable": false, "column_class": "parent_key", "column_type": "CHAR(28)", "parent_key_column_name": "HK_RBBE_ORDER", "parent_table_name": "rbbe_order_hub"}, {"column_name": "RH_RBBE_ORDER_ORDER_P2_L10_SAT", "is_nullable": false, "column_class": "diff_hash", "column_type": "CHAR(28)"}, {"column_name": "STATE", "is_nullable": true, "column_class": "content", "column_type": "NUMBER(36,0)", "exclude_from_change_detection": false, "prio_for_column_position": 50000}, {"column_name": "CREATEDAT", "is_nullable": true, "column_class": "content", "column_type": "TIMESTAMP", "exclude_from_change_detection": false, "prio_for_column_position": 50000}, {"column_name": "SHIPPEDAT", "is_nullable": true, "column_class": "content", "column_type": "TIMESTAMP", "exclude_from_change_detection": false, "prio_for_column_position": 50000}, {"column_name": "CONFIRMEDAT", "is_nullable": true, "column_class": "content", "column_type": "TIMESTAMP", "exclude_from_change_detection": false, "prio_for_column_position": 50000}, {"column_name": "INVOICEDATE", "is_nullable": true, "column_class": "content", "column_type": "TIMESTAMP", "exclude_from_change_detection": false, "prio_for_column_position": 50000}, {"column_name": "PAYEDAT", "is_nullable": true, "column_class": "content", "column_type": "TIMESTAMP", "exclude_from_change_detection": false, "prio_for_column_position": 50000}, {"column_name": "UPDATEDAT", "is_nullable": true, "column_class": "content_untracked", "column_type": "TIMESTAMP", "exclude_from_change_detection": "true", "prio_for_column_position": 50000}, {"column_name": "LASTMODIFIEDAT", "is_nullable": true, "column_class": "content_untracked", "column_type": "TIMESTAMP", "exclude_from_change_detection": "true", "prio_for_column_position": 50000}]}, {"table_name": "rbbe_customer_hub", "table_stereotype": "hub", "schema_name": "rvlt_billbee", "storage_component": "", "columns": [{"column_name": "MD_INSERTED_AT", "is_nullable": false, "column_class": "meta_load_date", "column_type": "TIMESTAMP"}, {"column_name": "MD_RUN_ID", "is_nullable": false, "column_class": "meta_load_process_id", "column_type": "INT"}, {"column_name": "MD_RECORD_SOURCE", "is_nullable": false, "column_class": "meta_record_source", "column_type": "VARCHAR(255)"}, {"column_name": "HK_RBBE_CUSTOMER", "is_nullable": false, "column_class": "key", "column_type": "CHAR(28)"}, {"column_name": "ID", "is_nullable": true, "column_class": "business_key", "column_type": "NUMBER(36,0)", "prio_for_column_position": 50000}]}, {"table_name": "rbbe_order_customer_lnk", "table_stereotype": "lnk", "schema_name": "rvlt_billbee", "storage_component": "", "columns": [{"column_name": "MD_INSERTED_AT", "is_nullable": false, "column_class": "meta_load_date", "column_type": "TIMESTAMP"}, {"column_name": "MD_RUN_ID", "is_nullable": false, "column_class": "meta_load_process_id", "column_type": "INT"}, {"column_name": "MD_RECORD_SOURCE", "is_nullable": false, "column_class": "meta_record_source", "column_type": "VARCHAR(255)"}, {"column_name": "HK_RBBE_ORDER", "is_nullable": false, "column_class": "parent_key", "column_type": "CHAR(28)", "parent_key_column_name": "HK_RBBE_ORDER", "parent_table_name": "rbbe_order_hub"}, {"column_name": "HK_RBBE_CUSTOMER", "is_nullable": false, "column_class": "parent_key", "column_type": "CHAR(28)", "parent_key_column_name": "HK_RBBE_CUSTOMER", "parent_table_name": "rbbe_customer_hub"}, {"column_name": "LK_RBBE_ORDER_CUSTOMER", "is_nullable": false, "column_class": "key", "column_type": "CHAR(28)"}]}, {"table_name": "rbbe_order_customer_esat", "table_stereotype": "sat", "schema_name": "rvlt_billbee", "storage_component": "", "has_deletion_flag": true, "is_effectivity_sat": true, "is_enddated": true, "is_multiactive": false, "compare_criteria": "key+current", "uses_diff_hash": false, "driving_keys": ["HK_RBBE_ORDER"], "columns": [{"column_name": "MD_INSERTED_AT", "is_nullable": false, "column_class": "meta_load_date", "column_type": "TIMESTAMP"}, {"column_name": "MD_RUN_ID", "is_nullable": false, "column_class": "meta_load_process_id", "column_type": "INT"}, {"column_name": "MD_RECORD_SOURCE", "is_nullable": false, "column_class": "meta_record_source", "column_type": "VARCHAR(255)"}, {"column_name": "MD_IS_DELETED", "is_nullable": false, "column_class": "meta_deletion_flag", "column_type": "BOOLEAN"}, {"column_name": "MD_VALID_BEFORE", "is_nullable": false, "column_class": "meta_load_enddate", "column_type": "TIMESTAMP"}, {"column_name": "LK_RBBE_ORDER_CUSTOMER", "is_nullable": false, "column_class": "parent_key", "column_type": "CHAR(28)", "parent_key_column_name": "LK_RBBE_ORDER_CUSTOMER", "parent_table_name": "rbbe_order_customer_lnk"}]}, {"table_name": "rbbe_shop_hub", "table_stereotype": "hub", "schema_name": "rvlt_billbee", "storage_component": "", "columns": [{"column_name": "MD_INSERTED_AT", "is_nullable": false, "column_class": "meta_load_date", "column_type": "TIMESTAMP"}, {"column_name": "MD_RUN_ID", "is_nullable": false, "column_class": "meta_load_process_id", "column_type": "INT"}, {"column_name": "MD_RECORD_SOURCE", "is_nullable": false, "column_class": "meta_record_source", "column_type": "VARCHAR(255)"}, {"column_name": "HK_RBBE_SHOP", "is_nullable": false, "column_class": "key", "column_type": "CHAR(28)"}, {"column_name": "BILLBEESHOPID", "is_nullable": true, "column_class": "business_key", "column_type": "NUMBER(36,0)", "prio_for_column_position": 50000}]}, {"table_name": "rbbe_order_shop_lnk", "table_stereotype": "lnk", "schema_name": "rvlt_billbee", "storage_component": "", "columns": [{"column_name": "MD_INSERTED_AT", "is_nullable": false, "column_class": "meta_load_date", "column_type": "TIMESTAMP"}, {"column_name": "MD_RUN_ID", "is_nullable": false, "column_class": "meta_load_process_id", "column_type": "INT"}, {"column_name": "MD_RECORD_SOURCE", "is_nullable": false, "column_class": "meta_record_source", "column_type": "VARCHAR(255)"}, {"column_name": "HK_RBBE_ORDER", "is_nullable": false, "column_class": "parent_key", "column_type": "CHAR(28)", "parent_key_column_name": "HK_RBBE_ORDER", "parent_table_name": "rbbe_order_hub"}, {"column_name": "HK_RBBE_SHOP", "is_nullable": false, "column_class": "parent_key", "column_type": "CHAR(28)", "parent_key_column_name": "HK_RBBE_SHOP", "parent_table_name": "rbbe_shop_hub"}, {"column_name": "LK_RBBE_ORDER_SHOP", "is_nullable": false, "column_class": "key", "column_type": "CHAR(28)"}]}, {"table_name": "rbbe_order_shop_p1_l10_sat", "table_stereotype": "sat", "schema_name": "rvlt_billbee", "storage_component": "", "has_deletion_flag": true, "is_effectivity_sat": false, "is_enddated": true, "is_multiactive": false, "compare_criteria": "key+current", "uses_diff_hash": true, "driving_keys": ["HK_RBBE_ORDER"], "columns": [{"column_name": "MD_INSERTED_AT", "is_nullable": false, "column_class": "meta_load_date", "column_type": "TIMESTAMP"}, {"column_name": "MD_RUN_ID", "is_nullable": false, "column_class": "meta_load_process_id", "column_type": "INT"}, {"column_name": "MD_RECORD_SOURCE", "is_nullable": false, "column_class": "meta_record_source", "column_type": "VARCHAR(255)"}, {"column_name": "MD_IS_DELETED", "is_nullable": false, "column_class": "meta_deletion_flag", "column_type": "BOOLEAN"}, {"column_name": "MD_VALID_BEFORE", "is_nullable": false, "column_class": "meta_load_enddate", "column_type": "TIMESTAMP"}, {"column_name": "LK_RBBE_ORDER_SHOP", "is_nullable": false, "column_class": "parent_key", "column_type": "CHAR(28)", "parent_key_column_name": "LK_RBBE_ORDER_SHOP", "parent_table_name": "rbbe_order_shop_lnk"}, {"column_name": "RH_RBBE_ORDER_SHOP_P1_L10_SAT", "is_nullable": false, "column_class": "diff_hash", "column_type": "CHAR(28)"}, {"column_name": "SELLER_BILLBEESHOPNAME", "is_nullable": true, "column_class": "content", "column_type": "VARCHAR(200)", "exclude_from_change_detection": false, "prio_for_column_position": 50000}]}, {"table_name": "rbbe_order_parent_order_lnk", "table_stereotype": "lnk", "schema_name": "rvlt_billbee", "storage_component": "", "columns": [{"column_name": "MD_INSERTED_AT", "is_nullable": false, "column_class": "meta_load_date", "column_type": "TIMESTAMP"}, {"column_name": "MD_RUN_ID", "is_nullable": false, "column_class": "meta_load_process_id", "column_type": "INT"}, {"column_name": "MD_RECORD_SOURCE", "is_nullable": false, "column_class": "meta_record_source", "column_type": "VARCHAR(255)"}, {"column_name": "HK_RBBE_ORDER", "is_nullable": false, "column_class": "parent_key", "column_type": "CHAR(28)", "parent_key_column_name": "HK_RBBE_ORDER", "parent_table_name": "rbbe_order_hub"}, {"column_name": "HK_RBBE_ORDER_PARENT", "is_nullable": false, "column_class": "parent_key", "column_type": "CHAR(28)", "parent_key_column_name": "HK_RBBE_ORDER", "parent_table_name": "rbbe_order_hub"}, {"column_name": "LK_RBBE_ORDER_PARENT_ORDER", "is_nullable": false, "column_class": "key", "column_type": "CHAR(28)"}]}, {"table_name": "rbbe_order_parent_order_esat", "table_stereotype": "sat", "schema_name": "rvlt_billbee", "storage_component": "", "has_deletion_flag": true, "is_effectivity_sat": true, "is_enddated": true, "is_multiactive": false, "compare_criteria": "key+current", "uses_diff_hash": false, "driving_keys": ["HK_RBBE_ORDER"], "columns": [{"column_name": "MD_INSERTED_AT", "is_nullable": false, "column_class": "meta_load_date", "column_type": "TIMESTAMP"}, {"column_name": "MD_RUN_ID", "is_nullable": false, "column_class": "meta_load_process_id", "column_type": "INT"}, {"column_name": "MD_RECORD_SOURCE", "is_nullable": false, "column_class": "meta_record_source", "column_type": "VARCHAR(255)"}, {"column_name": "MD_IS_DELETED", "is_nullable": false, "column_class": "meta_deletion_flag", "column_type": "BOOLEAN"}, {"column_name": "MD_VALID_BEFORE", "is_nullable": false, "column_class": "meta_load_enddate", "column_type": "TIMESTAMP"}, {"column_name": "LK_RBBE_ORDER_PARENT_ORDER", "is_nullable": false, "column_class": "parent_key", "column_type": "CHAR(28)", "parent_key_column_name": "LK_RBBE_ORDER_PARENT_ORDER", "parent_table_name": "rbbe_order_parent_order_lnk"}]}], "data_extraction": {"fetch_module_name": "billbee api", "parse_module_name": "json_array", "load_module_name": "python_framework", "json_array_path": "$.Data"}, "parse_sets": [{"stage_properties": [{"stage_schema": "stage_rvlt", "stage_table_name": "rbbe_ORDER_P1_stage", "storage_component": ""}], "record_source_name_expression": "billbee.order", "fields": [{"json_path": "<PERSON><PERSON><PERSON><PERSON><PERSON>rId", "field_type": "NUMBER(36,0)", "field_position": 1, "needs_encryption": false, "field_name": "BILLBEEORDERID"}, {"json_path": "Bill<PERSON>eeParentOrderId", "field_type": "NUMBER(36,0)", "field_position": 2, "needs_encryption": false, "field_name": "BILLBEEPARENTORDERID"}, {"json_path": "Customer.Id", "field_type": "NUMBER(36,0)", "field_position": 3, "needs_encryption": false, "field_name": "CUSTOMER_ID"}, {"json_path": "<PERSON><PERSON><PERSON>ShopId", "field_type": "NUMBER(36,0)", "field_position": 4, "needs_encryption": false, "field_name": "BILLBEESHOPID"}, {"json_path": "OrderNumber", "field_type": "VARCHAR(200)", "field_position": 5, "needs_encryption": false, "field_name": "ORDERNUMBER"}, {"json_path": "TotalCost", "field_type": "NUMBER(20,2)", "field_position": 6, "needs_encryption": false, "field_name": "TOTALCOST"}, {"json_path": "AdjustmentCost", "field_type": "NUMBER(20,2)", "field_position": 7, "needs_encryption": false, "field_name": "ADJUSTMENTCOST"}, {"json_path": "<PERSON><PERSON><PERSON><PERSON>", "field_type": "VARCHAR(200)", "field_position": 8, "needs_encryption": false, "field_name": "CURRENCY"}, {"json_path": "ShippingCost", "field_type": "NUMBER(20,2)", "field_position": 9, "needs_encryption": false, "field_name": "SHIPPINGCOST"}, {"json_path": "InvoiceAddress.CountryISO2", "field_type": "VARCHAR(20)", "field_position": 10, "needs_encryption": false, "field_name": "INVOICEADDRESS_COUNTRYISO2"}, {"json_path": "State", "field_type": "NUMBER(36,0)", "field_position": 11, "needs_encryption": false, "field_name": "STATE"}, {"json_path": "CreatedAt", "field_type": "TIMESTAMP", "field_position": 12, "needs_encryption": false, "field_name": "CREATEDAT"}, {"json_path": "ShippedAt", "field_type": "TIMESTAMP", "field_position": 13, "needs_encryption": false, "field_name": "SHIPPEDAT"}, {"json_path": "ConfirmedAt", "field_type": "TIMESTAMP", "field_position": 14, "needs_encryption": false, "field_name": "CONFIRMEDAT"}, {"json_path": "InvoiceDate", "field_type": "TIMESTAMP", "field_position": 15, "needs_encryption": false, "field_name": "INVOICEDATE"}, {"json_path": "PayedAt", "field_type": "TIMESTAMP", "field_position": 16, "needs_encryption": false, "field_name": "PAYEDAT"}, {"json_path": "UpdatedAt", "field_type": "TIMESTAMP", "field_position": 17, "needs_encryption": false, "field_name": "UPDATEDAT"}, {"json_path": "LastModifiedAt", "field_type": "TIMESTAMP", "field_position": 18, "needs_encryption": false, "field_name": "LASTMODIFIEDAT"}, {"json_path": "<PERSON><PERSON><PERSON>", "field_type": "VARCHAR(200)", "field_position": 19, "needs_encryption": false, "field_name": "SELLER_BILLBEESHOPNAME"}], "hashes": [{"stage_column_name": "HK_RBBE_ORDER", "hash_origin_table": "rbbe_order_hub", "column_class": "key", "hash_fields": [{"field_name": "BILLBEEORDERID", "prio_in_key_hash": 0, "field_target_table": "rbbe_order_hub", "field_target_column": "BILLBEEORDERID"}], "column_type": "CHAR(28)", "hash_encoding": "BASE64", "hash_function": "sha-1", "hash_concatenation_seperator": "|", "hash_timestamp_format_sqlstyle": "YYYY-MM-DD HH24:MI:SS.US", "hash_null_value_string": "", "model_profile_name": "_default", "hash_name": "KEY_OF_RBBE_ORDER_HUB"}, {"stage_column_name": "HK_RBBE_ORDER_PARENT", "hash_origin_table": "rbbe_order_hub", "column_class": "key", "hash_fields": [{"field_name": "BILLBEEPARENTORDERID", "prio_in_key_hash": 0, "field_target_table": "rbbe_order_hub", "field_target_column": "BILLBEEORDERID"}], "column_type": "CHAR(28)", "hash_encoding": "BASE64", "hash_function": "sha-1", "hash_concatenation_seperator": "|", "hash_timestamp_format_sqlstyle": "YYYY-MM-DD HH24:MI:SS.US", "hash_null_value_string": "", "model_profile_name": "_default", "hash_name": "KEY_OF_RBBE_ORDER_HUB__FOR__PARENT"}, {"stage_column_name": "HK_RBBE_CUSTOMER", "hash_origin_table": "rbbe_customer_hub", "column_class": "key", "hash_fields": [{"field_name": "CUSTOMER_ID", "prio_in_key_hash": 0, "field_target_table": "rbbe_customer_hub", "field_target_column": "ID"}], "column_type": "CHAR(28)", "hash_encoding": "BASE64", "hash_function": "sha-1", "hash_concatenation_seperator": "|", "hash_timestamp_format_sqlstyle": "YYYY-MM-DD HH24:MI:SS.US", "hash_null_value_string": "", "model_profile_name": "_default", "hash_name": "KEY_OF_RBBE_CUSTOMER_HUB"}, {"stage_column_name": "HK_RBBE_SHOP", "hash_origin_table": "rbbe_shop_hub", "column_class": "key", "hash_fields": [{"field_name": "BILLBEESHOPID", "prio_in_key_hash": 0, "field_target_table": "rbbe_shop_hub", "field_target_column": "BILLBEESHOPID"}], "column_type": "CHAR(28)", "hash_encoding": "BASE64", "hash_function": "sha-1", "hash_concatenation_seperator": "|", "hash_timestamp_format_sqlstyle": "YYYY-MM-DD HH24:MI:SS.US", "hash_null_value_string": "", "model_profile_name": "_default", "hash_name": "KEY_OF_RBBE_SHOP_HUB"}, {"stage_column_name": "LK_RBBE_ORDER_CUSTOMER", "hash_origin_table": "rbbe_order_customer_lnk", "column_class": "key", "hash_fields": [{"field_name": "BILLBEEORDERID", "prio_in_key_hash": 0, "field_target_table": "rbbe_order_hub", "field_target_column": "BILLBEEORDERID", "parent_declaration_position": 1}, {"field_name": "CUSTOMER_ID", "prio_in_key_hash": 0, "field_target_table": "rbbe_customer_hub", "field_target_column": "ID", "parent_declaration_position": 2}], "column_type": "CHAR(28)", "hash_encoding": "BASE64", "hash_function": "sha-1", "hash_concatenation_seperator": "|", "hash_timestamp_format_sqlstyle": "YYYY-MM-DD HH24:MI:SS.US", "hash_null_value_string": "", "model_profile_name": "_default", "hash_name": "KEY_<PERSON>_RBBE_ORDER_CUSTOMER_LNK"}, {"stage_column_name": "LK_RBBE_ORDER_SHOP", "hash_origin_table": "rbbe_order_shop_lnk", "column_class": "key", "hash_fields": [{"field_name": "BILLBEEORDERID", "prio_in_key_hash": 0, "field_target_table": "rbbe_order_hub", "field_target_column": "BILLBEEORDERID", "parent_declaration_position": 1}, {"field_name": "BILLBEESHOPID", "prio_in_key_hash": 0, "field_target_table": "rbbe_shop_hub", "field_target_column": "BILLBEESHOPID", "parent_declaration_position": 2}], "column_type": "CHAR(28)", "hash_encoding": "BASE64", "hash_function": "sha-1", "hash_concatenation_seperator": "|", "hash_timestamp_format_sqlstyle": "YYYY-MM-DD HH24:MI:SS.US", "hash_null_value_string": "", "model_profile_name": "_default", "hash_name": "KEY_OF_RBBE_ORDER_SHOP_LNK"}, {"stage_column_name": "LK_RBBE_ORDER_PARENT_ORDER", "hash_origin_table": "rbbe_order_parent_order_lnk", "column_class": "key", "hash_fields": [{"field_name": "BILLBEEORDERID", "prio_in_key_hash": 0, "field_target_table": "rbbe_order_hub", "field_target_column": "BILLBEEORDERID", "parent_declaration_position": 1}, {"field_name": "BILLBEEPARENTORDERID", "prio_in_key_hash": 0, "field_target_table": "rbbe_order_hub", "field_target_column": "BILLBEEORDERID", "parent_declaration_position": 2}], "column_type": "CHAR(28)", "hash_encoding": "BASE64", "hash_function": "sha-1", "hash_concatenation_seperator": "|", "hash_timestamp_format_sqlstyle": "YYYY-MM-DD HH24:MI:SS.US", "hash_null_value_string": "", "model_profile_name": "_default", "hash_name": "KEY_OF_RBBE_ORDER_PARENT_ORDER_LNK"}, {"stage_column_name": "RH_RBBE_ORDER_ORDER_P1_L10_SAT", "hash_origin_table": "rbbe_order_order_p1_l10_sat", "multi_row_content": false, "column_class": "diff_hash", "hash_fields": [{"field_name": "ORDERNUMBER", "prio_in_diff_hash": 0, "prio_for_row_order": 50000, "field_target_table": "rbbe_order_order_p1_l10_sat", "field_target_column": "ORDERNUMBER"}, {"field_name": "TOTALCOST", "prio_in_diff_hash": 0, "prio_for_row_order": 50000, "field_target_table": "rbbe_order_order_p1_l10_sat", "field_target_column": "TOTALCOST"}, {"field_name": "ADJUSTMENTCOST", "prio_in_diff_hash": 0, "prio_for_row_order": 50000, "field_target_table": "rbbe_order_order_p1_l10_sat", "field_target_column": "ADJUSTMENTCOST"}, {"field_name": "CURRENCY", "prio_in_diff_hash": 0, "prio_for_row_order": 50000, "field_target_table": "rbbe_order_order_p1_l10_sat", "field_target_column": "CURRENCY"}, {"field_name": "SHIPPINGCOST", "prio_in_diff_hash": 0, "prio_for_row_order": 50000, "field_target_table": "rbbe_order_order_p1_l10_sat", "field_target_column": "SHIPPINGCOST"}, {"field_name": "INVOICEADDRESS_COUNTRYISO2", "prio_in_diff_hash": 0, "prio_for_row_order": 50000, "field_target_table": "rbbe_order_order_p1_l10_sat", "field_target_column": "INVOICEADDRESS_COUNTRYISO2"}], "column_type": "CHAR(28)", "hash_encoding": "BASE64", "hash_function": "sha-1", "hash_concatenation_seperator": "|", "hash_timestamp_format_sqlstyle": "YYYY-MM-DD HH24:MI:SS.US", "hash_null_value_string": "", "model_profile_name": "_default", "related_key_hash": "KEY_OF_RBBE_ORDER_HUB", "hash_name": "DIFF_OF_RBBE_ORDER_ORDER_P1_L10_SAT"}, {"stage_column_name": "RH_RBBE_ORDER_ORDER_P2_L10_SAT", "hash_origin_table": "rbbe_order_order_p2_l10_sat", "multi_row_content": false, "column_class": "diff_hash", "hash_fields": [{"field_name": "STATE", "prio_in_diff_hash": 0, "prio_for_row_order": 50000, "field_target_table": "rbbe_order_order_p2_l10_sat", "field_target_column": "STATE"}, {"field_name": "CREATEDAT", "prio_in_diff_hash": 0, "prio_for_row_order": 50000, "field_target_table": "rbbe_order_order_p2_l10_sat", "field_target_column": "CREATEDAT"}, {"field_name": "SHIPPEDAT", "prio_in_diff_hash": 0, "prio_for_row_order": 50000, "field_target_table": "rbbe_order_order_p2_l10_sat", "field_target_column": "SHIPPEDAT"}, {"field_name": "CONFIRMEDAT", "prio_in_diff_hash": 0, "prio_for_row_order": 50000, "field_target_table": "rbbe_order_order_p2_l10_sat", "field_target_column": "CONFIRMEDAT"}, {"field_name": "INVOICEDATE", "prio_in_diff_hash": 0, "prio_for_row_order": 50000, "field_target_table": "rbbe_order_order_p2_l10_sat", "field_target_column": "INVOICEDATE"}, {"field_name": "PAYEDAT", "prio_in_diff_hash": 0, "prio_for_row_order": 50000, "field_target_table": "rbbe_order_order_p2_l10_sat", "field_target_column": "PAYEDAT"}], "column_type": "CHAR(28)", "hash_encoding": "BASE64", "hash_function": "sha-1", "hash_concatenation_seperator": "|", "hash_timestamp_format_sqlstyle": "YYYY-MM-DD HH24:MI:SS.US", "hash_null_value_string": "", "model_profile_name": "_default", "related_key_hash": "KEY_OF_RBBE_ORDER_HUB", "hash_name": "DIFF_OF_RBBE_ORDER_ORDER_P2_L10_SAT"}, {"stage_column_name": "RH_RBBE_ORDER_SHOP_P1_L10_SAT", "hash_origin_table": "rbbe_order_shop_p1_l10_sat", "multi_row_content": false, "column_class": "diff_hash", "hash_fields": [{"field_name": "SELLER_BILLBEESHOPNAME", "prio_in_diff_hash": 0, "prio_for_row_order": 50000, "field_target_table": "rbbe_order_shop_p1_l10_sat", "field_target_column": "SELLER_BILLBEESHOPNAME"}], "column_type": "CHAR(28)", "hash_encoding": "BASE64", "hash_function": "sha-1", "hash_concatenation_seperator": "|", "hash_timestamp_format_sqlstyle": "YYYY-MM-DD HH24:MI:SS.US", "hash_null_value_string": "", "model_profile_name": "_default", "related_key_hash": "KEY_OF_RBBE_ORDER_SHOP_LNK", "hash_name": "DIFF_OF_RBBE_ORDER_SHOP_P1_L10_SAT"}], "load_operations": [{"table_name": "rbbe_order_hub", "relation_name": "/", "operation_origin": "field mapping relation", "hash_mappings": [{"hash_class": "key", "column_name": "HK_RBBE_ORDER", "is_nullable": false, "hash_name": "KEY_OF_RBBE_ORDER_HUB", "stage_column_name": "HK_RBBE_ORDER"}], "data_mapping": [{"column_name": "BILLBEEORDERID", "field_name": "BILLBEEORDERID", "column_class": "business_key", "is_nullable": true, "stage_column_name": "BILLBEEORDERID"}]}, {"table_name": "rbbe_order_hub", "relation_name": "PARENT", "operation_origin": "field mapping relation", "hash_mappings": [{"hash_class": "key", "column_name": "HK_RBBE_ORDER", "is_nullable": false, "hash_name": "KEY_OF_RBBE_ORDER_HUB__FOR__PARENT", "stage_column_name": "HK_RBBE_ORDER_PARENT"}], "data_mapping": [{"column_name": "BILLBEEORDERID", "field_name": "BILLBEEPARENTORDERID", "column_class": "business_key", "is_nullable": true, "stage_column_name": "BILLBEEPARENTORDERID"}]}, {"table_name": "rbbe_order_order_p1_l10_sat", "relation_name": "/", "operation_origin": "field mapping relation", "hash_mappings": [{"hash_class": "parent_key", "column_name": "HK_RBBE_ORDER", "is_nullable": false, "hash_name": "KEY_OF_RBBE_ORDER_HUB", "stage_column_name": "HK_RBBE_ORDER"}, {"hash_class": "diff_hash", "column_name": "RH_RBBE_ORDER_ORDER_P1_L10_SAT", "is_nullable": false, "hash_name": "DIFF_OF_RBBE_ORDER_ORDER_P1_L10_SAT", "stage_column_name": "RH_RBBE_ORDER_ORDER_P1_L10_SAT"}], "data_mapping": [{"column_name": "ORDERNUMBER", "field_name": "ORDERNUMBER", "column_class": "content", "is_nullable": true, "stage_column_name": "ORDERNUMBER"}, {"column_name": "TOTALCOST", "field_name": "TOTALCOST", "column_class": "content", "is_nullable": true, "stage_column_name": "TOTALCOST"}, {"column_name": "ADJUSTMENTCOST", "field_name": "ADJUSTMENTCOST", "column_class": "content", "is_nullable": true, "stage_column_name": "ADJUSTMENTCOST"}, {"column_name": "CURRENCY", "field_name": "CURRENCY", "column_class": "content", "is_nullable": true, "stage_column_name": "CURRENCY"}, {"column_name": "SHIPPINGCOST", "field_name": "SHIPPINGCOST", "column_class": "content", "is_nullable": true, "stage_column_name": "SHIPPINGCOST"}, {"column_name": "INVOICEADDRESS_COUNTRYISO2", "field_name": "INVOICEADDRESS_COUNTRYISO2", "column_class": "content", "is_nullable": true, "stage_column_name": "INVOICEADDRESS_COUNTRYISO2"}, {"column_name": "UPDATEDAT", "field_name": "UPDATEDAT", "column_class": "content_untracked", "is_nullable": true, "stage_column_name": "UPDATEDAT"}, {"column_name": "LASTMODIFIEDAT", "field_name": "LASTMODIFIEDAT", "column_class": "content_untracked", "is_nullable": true, "stage_column_name": "LASTMODIFIEDAT"}]}, {"table_name": "rbbe_order_order_p2_l10_sat", "relation_name": "/", "operation_origin": "field mapping relation", "hash_mappings": [{"hash_class": "parent_key", "column_name": "HK_RBBE_ORDER", "is_nullable": false, "hash_name": "KEY_OF_RBBE_ORDER_HUB", "stage_column_name": "HK_RBBE_ORDER"}, {"hash_class": "diff_hash", "column_name": "RH_RBBE_ORDER_ORDER_P2_L10_SAT", "is_nullable": false, "hash_name": "DIFF_OF_RBBE_ORDER_ORDER_P2_L10_SAT", "stage_column_name": "RH_RBBE_ORDER_ORDER_P2_L10_SAT"}], "data_mapping": [{"column_name": "STATE", "field_name": "STATE", "column_class": "content", "is_nullable": true, "stage_column_name": "STATE"}, {"column_name": "CREATEDAT", "field_name": "CREATEDAT", "column_class": "content", "is_nullable": true, "stage_column_name": "CREATEDAT"}, {"column_name": "SHIPPEDAT", "field_name": "SHIPPEDAT", "column_class": "content", "is_nullable": true, "stage_column_name": "SHIPPEDAT"}, {"column_name": "CONFIRMEDAT", "field_name": "CONFIRMEDAT", "column_class": "content", "is_nullable": true, "stage_column_name": "CONFIRMEDAT"}, {"column_name": "INVOICEDATE", "field_name": "INVOICEDATE", "column_class": "content", "is_nullable": true, "stage_column_name": "INVOICEDATE"}, {"column_name": "PAYEDAT", "field_name": "PAYEDAT", "column_class": "content", "is_nullable": true, "stage_column_name": "PAYEDAT"}, {"column_name": "UPDATEDAT", "field_name": "UPDATEDAT", "column_class": "content_untracked", "is_nullable": true, "stage_column_name": "UPDATEDAT"}, {"column_name": "LASTMODIFIEDAT", "field_name": "LASTMODIFIEDAT", "column_class": "content_untracked", "is_nullable": true, "stage_column_name": "LASTMODIFIEDAT"}]}, {"table_name": "rbbe_customer_hub", "relation_name": "/", "operation_origin": "field mapping relation", "hash_mappings": [{"hash_class": "key", "column_name": "HK_RBBE_CUSTOMER", "is_nullable": false, "hash_name": "KEY_OF_RBBE_CUSTOMER_HUB", "stage_column_name": "HK_RBBE_CUSTOMER"}], "data_mapping": [{"column_name": "ID", "field_name": "CUSTOMER_ID", "column_class": "business_key", "is_nullable": true, "stage_column_name": "CUSTOMER_ID"}]}, {"table_name": "rbbe_order_customer_lnk", "relation_name": "/", "operation_origin": "induced from satellite rbbe_order_customer_esat", "hash_mappings": [{"hash_class": "parent_key_1", "column_name": "HK_RBBE_ORDER", "is_nullable": false, "hash_name": "KEY_OF_RBBE_ORDER_HUB", "stage_column_name": "HK_RBBE_ORDER"}, {"hash_class": "parent_key_2", "column_name": "HK_RBBE_CUSTOMER", "is_nullable": false, "hash_name": "KEY_OF_RBBE_CUSTOMER_HUB", "stage_column_name": "HK_RBBE_CUSTOMER"}, {"hash_class": "key", "column_name": "LK_RBBE_ORDER_CUSTOMER", "is_nullable": false, "hash_name": "KEY_<PERSON>_RBBE_ORDER_CUSTOMER_LNK", "stage_column_name": "LK_RBBE_ORDER_CUSTOMER"}]}, {"table_name": "rbbe_order_customer_esat", "relation_name": "/", "operation_origin": "explicitly tracked relation", "hash_mappings": [{"hash_class": "parent_key", "column_name": "LK_RBBE_ORDER_CUSTOMER", "is_nullable": false, "hash_name": "KEY_<PERSON>_RBBE_ORDER_CUSTOMER_LNK", "stage_column_name": "LK_RBBE_ORDER_CUSTOMER"}]}, {"table_name": "rbbe_shop_hub", "relation_name": "/", "operation_origin": "field mapping relation", "hash_mappings": [{"hash_class": "key", "column_name": "HK_RBBE_SHOP", "is_nullable": false, "hash_name": "KEY_OF_RBBE_SHOP_HUB", "stage_column_name": "HK_RBBE_SHOP"}], "data_mapping": [{"column_name": "BILLBEESHOPID", "field_name": "BILLBEESHOPID", "column_class": "business_key", "is_nullable": true, "stage_column_name": "BILLBEESHOPID"}]}, {"table_name": "rbbe_order_shop_lnk", "relation_name": "/", "operation_origin": "induced from satellite rbbe_order_shop_p1_l10_sat", "hash_mappings": [{"hash_class": "parent_key_1", "column_name": "HK_RBBE_ORDER", "is_nullable": false, "hash_name": "KEY_OF_RBBE_ORDER_HUB", "stage_column_name": "HK_RBBE_ORDER"}, {"hash_class": "parent_key_2", "column_name": "HK_RBBE_SHOP", "is_nullable": false, "hash_name": "KEY_OF_RBBE_SHOP_HUB", "stage_column_name": "HK_RBBE_SHOP"}, {"hash_class": "key", "column_name": "LK_RBBE_ORDER_SHOP", "is_nullable": false, "hash_name": "KEY_OF_RBBE_ORDER_SHOP_LNK", "stage_column_name": "LK_RBBE_ORDER_SHOP"}]}, {"table_name": "rbbe_order_shop_p1_l10_sat", "relation_name": "/", "operation_origin": "field mapping relation", "hash_mappings": [{"hash_class": "parent_key", "column_name": "LK_RBBE_ORDER_SHOP", "is_nullable": false, "hash_name": "KEY_OF_RBBE_ORDER_SHOP_LNK", "stage_column_name": "LK_RBBE_ORDER_SHOP"}, {"hash_class": "diff_hash", "column_name": "RH_RBBE_ORDER_SHOP_P1_L10_SAT", "is_nullable": false, "hash_name": "DIFF_OF_RBBE_ORDER_SHOP_P1_L10_SAT", "stage_column_name": "RH_RBBE_ORDER_SHOP_P1_L10_SAT"}], "data_mapping": [{"column_name": "SELLER_BILLBEESHOPNAME", "field_name": "SELLER_BILLBEESHOPNAME", "column_class": "content", "is_nullable": true, "stage_column_name": "SELLER_BILLBEESHOPNAME"}]}, {"table_name": "rbbe_order_parent_order_lnk", "relation_name": "/", "operation_origin": "fixed '/' operation due to explicit parent relation declaration", "hash_mappings": [{"hash_class": "parent_key_1", "column_name": "HK_RBBE_ORDER", "is_nullable": false, "hash_name": "KEY_OF_RBBE_ORDER_HUB", "stage_column_name": "HK_RBBE_ORDER"}, {"hash_class": "parent_key_2", "column_name": "HK_RBBE_ORDER_PARENT", "is_nullable": false, "hash_name": "KEY_OF_RBBE_ORDER_HUB__FOR__PARENT", "stage_column_name": "HK_RBBE_ORDER_PARENT"}, {"hash_class": "key", "column_name": "LK_RBBE_ORDER_PARENT_ORDER", "is_nullable": false, "hash_name": "KEY_OF_RBBE_ORDER_PARENT_ORDER_LNK", "stage_column_name": "LK_RBBE_ORDER_PARENT_ORDER"}]}, {"table_name": "rbbe_order_parent_order_esat", "relation_name": "/", "operation_origin": "following parent operation list", "hash_mappings": [{"hash_class": "parent_key", "column_name": "LK_RBBE_ORDER_PARENT_ORDER", "is_nullable": false, "hash_name": "KEY_OF_RBBE_ORDER_PARENT_ORDER_LNK", "stage_column_name": "LK_RBBE_ORDER_PARENT_ORDER"}]}], "stage_columns": [{"stage_column_name": "MD_INSERTED_AT", "is_nullable": false, "stage_column_class": "meta_load_date", "column_type": "TIMESTAMP"}, {"stage_column_name": "MD_RUN_ID", "is_nullable": false, "stage_column_class": "meta_load_process_id", "column_type": "INT"}, {"stage_column_name": "MD_RECORD_SOURCE", "is_nullable": false, "stage_column_class": "meta_record_source", "column_type": "VARCHAR(255)"}, {"stage_column_name": "MD_IS_DELETED", "is_nullable": false, "stage_column_class": "meta_deletion_flag", "column_type": "BOOLEAN"}, {"stage_column_name": "HK_RBBE_ORDER", "stage_column_class": "hash", "is_nullable": false, "hash_name": "KEY_OF_RBBE_ORDER_HUB", "column_type": "CHAR(28)"}, {"stage_column_name": "HK_RBBE_ORDER_PARENT", "stage_column_class": "hash", "is_nullable": false, "hash_name": "KEY_OF_RBBE_ORDER_HUB__FOR__PARENT", "column_type": "CHAR(28)"}, {"stage_column_name": "HK_RBBE_CUSTOMER", "stage_column_class": "hash", "is_nullable": false, "hash_name": "KEY_OF_RBBE_CUSTOMER_HUB", "column_type": "CHAR(28)"}, {"stage_column_name": "HK_RBBE_SHOP", "stage_column_class": "hash", "is_nullable": false, "hash_name": "KEY_OF_RBBE_SHOP_HUB", "column_type": "CHAR(28)"}, {"stage_column_name": "LK_RBBE_ORDER_CUSTOMER", "stage_column_class": "hash", "is_nullable": false, "hash_name": "KEY_<PERSON>_RBBE_ORDER_CUSTOMER_LNK", "column_type": "CHAR(28)"}, {"stage_column_name": "LK_RBBE_ORDER_SHOP", "stage_column_class": "hash", "is_nullable": false, "hash_name": "KEY_OF_RBBE_ORDER_SHOP_LNK", "column_type": "CHAR(28)"}, {"stage_column_name": "LK_RBBE_ORDER_PARENT_ORDER", "stage_column_class": "hash", "is_nullable": false, "hash_name": "KEY_OF_RBBE_ORDER_PARENT_ORDER_LNK", "column_type": "CHAR(28)"}, {"stage_column_name": "RH_RBBE_ORDER_ORDER_P1_L10_SAT", "stage_column_class": "hash", "is_nullable": false, "hash_name": "DIFF_OF_RBBE_ORDER_ORDER_P1_L10_SAT", "column_type": "CHAR(28)"}, {"stage_column_name": "RH_RBBE_ORDER_ORDER_P2_L10_SAT", "stage_column_class": "hash", "is_nullable": false, "hash_name": "DIFF_OF_RBBE_ORDER_ORDER_P2_L10_SAT", "column_type": "CHAR(28)"}, {"stage_column_name": "RH_RBBE_ORDER_SHOP_P1_L10_SAT", "stage_column_class": "hash", "is_nullable": false, "hash_name": "DIFF_OF_RBBE_ORDER_SHOP_P1_L10_SAT", "column_type": "CHAR(28)"}, {"stage_column_name": "BILLBEEORDERID", "stage_column_class": "data", "field_name": "BILLBEEORDERID", "is_nullable": true, "column_type": "NUMBER(36,0)", "column_classes": ["business_key"]}, {"stage_column_name": "BILLBEEPARENTORDERID", "stage_column_class": "data", "field_name": "BILLBEEPARENTORDERID", "is_nullable": true, "column_type": "NUMBER(36,0)", "column_classes": ["business_key"]}, {"stage_column_name": "CUSTOMER_ID", "stage_column_class": "data", "field_name": "CUSTOMER_ID", "is_nullable": true, "column_type": "NUMBER(36,0)", "column_classes": ["business_key"]}, {"stage_column_name": "BILLBEESHOPID", "stage_column_class": "data", "field_name": "BILLBEESHOPID", "is_nullable": true, "column_type": "NUMBER(36,0)", "column_classes": ["business_key"]}, {"stage_column_name": "ORDERNUMBER", "stage_column_class": "data", "field_name": "ORDERNUMBER", "is_nullable": true, "column_type": "VARCHAR(200)", "column_classes": ["content"]}, {"stage_column_name": "TOTALCOST", "stage_column_class": "data", "field_name": "TOTALCOST", "is_nullable": true, "column_type": "NUMBER(20,2)", "column_classes": ["content"]}, {"stage_column_name": "ADJUSTMENTCOST", "stage_column_class": "data", "field_name": "ADJUSTMENTCOST", "is_nullable": true, "column_type": "NUMBER(20,2)", "column_classes": ["content"]}, {"stage_column_name": "CURRENCY", "stage_column_class": "data", "field_name": "CURRENCY", "is_nullable": true, "column_type": "VARCHAR(200)", "column_classes": ["content"]}, {"stage_column_name": "SHIPPINGCOST", "stage_column_class": "data", "field_name": "SHIPPINGCOST", "is_nullable": true, "column_type": "NUMBER(20,2)", "column_classes": ["content"]}, {"stage_column_name": "INVOICEADDRESS_COUNTRYISO2", "stage_column_class": "data", "field_name": "INVOICEADDRESS_COUNTRYISO2", "is_nullable": true, "column_type": "VARCHAR(20)", "column_classes": ["content"]}, {"stage_column_name": "STATE", "stage_column_class": "data", "field_name": "STATE", "is_nullable": true, "column_type": "NUMBER(36,0)", "column_classes": ["content"]}, {"stage_column_name": "CREATEDAT", "stage_column_class": "data", "field_name": "CREATEDAT", "is_nullable": true, "column_type": "TIMESTAMP", "column_classes": ["content"]}, {"stage_column_name": "SHIPPEDAT", "stage_column_class": "data", "field_name": "SHIPPEDAT", "is_nullable": true, "column_type": "TIMESTAMP", "column_classes": ["content"]}, {"stage_column_name": "CONFIRMEDAT", "stage_column_class": "data", "field_name": "CONFIRMEDAT", "is_nullable": true, "column_type": "TIMESTAMP", "column_classes": ["content"]}, {"stage_column_name": "INVOICEDATE", "stage_column_class": "data", "field_name": "INVOICEDATE", "is_nullable": true, "column_type": "TIMESTAMP", "column_classes": ["content"]}, {"stage_column_name": "PAYEDAT", "stage_column_class": "data", "field_name": "PAYEDAT", "is_nullable": true, "column_type": "TIMESTAMP", "column_classes": ["content"]}, {"stage_column_name": "UPDATEDAT", "stage_column_class": "data", "field_name": "UPDATEDAT", "is_nullable": true, "column_type": "TIMESTAMP", "column_classes": ["content_untracked"]}, {"stage_column_name": "LASTMODIFIEDAT", "stage_column_class": "data", "field_name": "LASTMODIFIEDAT", "is_nullable": true, "column_type": "TIMESTAMP", "column_classes": ["content_untracked"]}, {"stage_column_name": "SELLER_BILLBEESHOPNAME", "stage_column_class": "data", "field_name": "SELLER_BILLBEESHOPNAME", "is_nullable": true, "column_type": "VARCHAR(200)", "column_classes": ["content"]}]}]}