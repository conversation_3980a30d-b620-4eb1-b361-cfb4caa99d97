/*
 * Schema: stage_rvlt
 * 
 * 描述：
 * 此模式包含原始数据的暂存区表（Staging Tables）。
 * 它是数据从源系统到数据仓库的第一个落地点，存储未经处理的原始数据。
 * 这些表通常是临时性的，用于数据加载过程中的中间存储。
 * 
 * 包含的表类型：
 * - 暂存表：按源系统和数据类型组织，存储原始数据
 * - 每个暂存表通常对应一个数据提取过程
 * - 表名通常以源系统前缀和数据类型命名
 */

-- DROP SCHEMA stage_rvlt;

CREATE SCHEMA if not EXISTS stage_rvlt;

COMMENT ON SCHEMA stage_rvlt
  IS 'Contains staging tables for raw data before loading into vault structures';

GRANT USAGE ON SCHEMA stage_rvlt to DV_D_ACCESS_WRITE;
