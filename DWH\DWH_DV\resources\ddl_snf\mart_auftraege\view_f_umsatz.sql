
/* select * from  mart_auftraege.f_umsatz order by kundengruppe, gesellschaft, artikelgruppe, tagesdatum,verkaufsgebiet;
   select  tagesdatum,gesellschaft, round(sum(umsatz_eur),0) from mart_auftraege.f_umsatz group by 1,2 order by 1,2;
 */

CREATE OR replace VIEW mart_auftraege.f_umsatz AS 

with measures as (

	select
		gesellschaft_lnk.hk_rgnr_gesellschaft 
		,item_lnk.hk_rsap_item_oitm
		,bpartner_semployee_lnk.hk_rsap_business_partner_ocrd 
		,bpartner_semployee_lnk.hk_rsap_sales_employee_oslp 
		,head_sat.docdate
		,line_sat.ocrcode
		,line_sat.ocrcode2
		,line_korr_sat.linetotal_korrigiert 
		,line_sat.currency 
	from   RVLT_SAP.RSAP_INVOICE_LINE_INV1_P1_L20_SAT line_sat
	join bvlt_sap.bsap_INVOICE_LINE_INV1_korrigiert_v1_b10_current_sat  line_korr_sat
		on line_korr_sat.hk_rsap_INVOICE_LINE_INV1 =line_sat.hk_rsap_INVOICE_LINE_INV1
	join RVLT_SAP.RSAP_INVOICE_LINE_INV1_INVOICE_OINV_LNK head_lnk
		on head_lnk.HK_RSAP_INVOICE_LINE_INV1 = line_sat.HK_RSAP_INVOICE_LINE_INV1
	join RVLT_SAP.RSAP_INVOICE_OINV_P1_L20_SAT head_sat
		on head_sat.hk_rsap_invoice_oinv = head_lnk.hk_rsap_invoice_oinv
		and head_sat.md_valid_before = lib.dwh_far_future_date()
		and not head_sat.md_is_deleted
	join RVLT_SAP.RSAP_INVOICE_OINV_RGNR_GESELLSCHAFT_LNK gesellschaft_lnk
		on gesellschaft_lnk.hk_rsap_invoice_oinv = head_lnk.hk_rsap_invoice_oinv 
	join RVLT_SAP.RSAP_INVOICE_OINV_BUSINESS_PARTNER_OCRD_SALES_EMPLOYEE_OSLP_LNK bpartner_semployee_lnk
		on bpartner_semployee_lnk.hk_rsap_invoice_oinv = head_lnk.hk_rsap_invoice_oinv 
	join RVLT_SAP.RSAP_INVOICE_OINV_BUSINESS_PARTNER_OCRD_SALES_EMPLOYEE_OSLP_ESAT bpartner_semployee_esat
		on bpartner_semployee_esat.LK_RSAP_INVOICE_OINV_BUSINESS_PARTNER_OCRD_SALES_EMPLOYEE_OSLP =bpartner_semployee_lnk.LK_RSAP_INVOICE_OINV_BUSINESS_PARTNER_OCRD_SALES_EMPLOYEE_OSLP 
		and bpartner_semployee_esat.md_valid_before = lib.dwh_far_future_date()
		and not bpartner_semployee_esat.md_is_deleted
	join rvlt_sap.rsap_invoice_line_inv1_item_oitm_lnk item_lnk
		on item_lnk.hk_rsap_invoice_line_inv1 =line_sat.hk_rsap_invoice_line_inv1 
	join rvlt_sap.rsap_invoice_line_inv1_item_oitm_esat item_esat
		on item_esat.lk_rsap_invoice_line_inv1_item_oitm =item_lnk.lk_rsap_invoice_line_inv1_item_oitm 
		and item_esat.md_valid_before = lib.dwh_far_future_date()
		and not head_sat.md_is_deleted 
	join RVLT_SAP.RSAP_INVOICE_LINE_INV1_HUB line_hub
		on line_hub.hk_rsap_invoice_line_inv1 =line_sat.hk_rsap_invoice_line_inv1 
	where line_sat.md_valid_before = lib.dwh_far_future_date()
		and not line_sat.md_is_deleted
        and (head_sat.doctype ='I' or line_hub.company ='FIPP_DE') -- Nur Artikelrechnungen (dann wären es 395.409) bei FIPP_DE nehmen wir allesn, dann sind es  395.527 rows
        
	--
	union all
	--
	
	select
		gesellschaft_lnk.hk_rgnr_gesellschaft 
		,item_lnk.hk_rsap_item_oitm
		,bpartner_semployee_lnk.hk_rsap_business_partner_ocrd 
		,bpartner_semployee_lnk.hk_rsap_sales_employee_oslp 
		,head_sat.docdate
		,line_sat.ocrcode
		,line_sat.ocrcode2
		,-line_korr_sat.linetotal_korrigiert 
		,line_sat.currency cy
	from RVLT_SAP.rsap_credit_memo_line_rin1_p1_l20_sat  line_sat
	join bvlt_sap.bsap_credit_memo_line_rin1_korrigiert_v1_b10_current_sat  line_korr_sat
		on line_korr_sat.hk_rsap_credit_memo_line_rin1 =line_sat.hk_rsap_credit_memo_line_rin1 
	join RVLT_SAP.rsap_credit_memo_line_rin1_credit_memo_orin_lnk head_lnk
		on head_lnk.hk_rsap_credit_memo_line_rin1 = line_sat.hk_rsap_credit_memo_line_rin1 
	join RVLT_SAP.rsap_credit_memo_orin_p1_l20_sat head_sat
		on head_sat.hk_rsap_credit_memo_orin = head_lnk.hk_rsap_credit_memo_orin
		and head_sat.md_valid_before = lib.dwh_far_future_date()
		and not head_sat.md_is_deleted
	join RVLT_SAP.rsap_credit_memo_orin_rgnr_gesellschaft_lnk  gesellschaft_lnk
		on gesellschaft_lnk.hk_rsap_credit_memo_orin = head_lnk.hk_rsap_credit_memo_orin 
	join RVLT_SAP.rsap_credit_memo_orin_business_partner_ocrd_SALES_EMPLOYEE_OSLP_lnk bpartner_semployee_lnk
		on bpartner_semployee_lnk.hk_rsap_credit_memo_orin = head_lnk.hk_rsap_credit_memo_orin 
	join RVLT_SAP.rsap_credit_memo_orin_business_partner_ocrd_SALES_EMPLOYEE_OSLP_ESAT bpartner_semployee__esat
		on bpartner_semployee__esat.LK_RSAP_CREDIT_MEMO_ORIN_BUSINESS_PARTNER_OCRD_SALES_EMPLOYEE_OSLP =bpartner_semployee_lnk.LK_RSAP_CREDIT_MEMO_ORIN_BUSINESS_PARTNER_OCRD_SALES_EMPLOYEE_OSLP 
		and bpartner_semployee__esat.md_valid_before = lib.dwh_far_future_date()
		and not bpartner_semployee__esat.md_is_deleted
	join rvlt_sap.rsap_credit_memo_line_rin1_item_oitm_lnk item_lnk
		on item_lnk.hk_rsap_credit_memo_line_rin1 =line_sat.hk_rsap_credit_memo_line_rin1 
	join rvlt_sap.rsap_credit_memo_line_rin1_item_oitm_esat item_esat
		on item_esat.lk_rsap_credit_memo_line_rin1_item_oitm =item_lnk.lk_rsap_credit_memo_line_rin1_item_oitm 
		and item_esat.md_valid_before = lib.dwh_far_future_date()
		and not head_sat.md_is_deleted 
	join RVLT_SAP.rsap_credit_memo_line_rin1_hub line_hub
		on line_hub.hk_rsap_credit_memo_line_rin1 =line_sat.hk_rsap_credit_memo_line_rin1 
	where line_sat.md_valid_before = lib.dwh_far_future_date()
		and not line_sat.md_is_deleted	
        and (head_sat.doctype ='I' or line_hub.company ='FIPP_DE') -- Nur Artikelrechnungen außer bei FIPP, da alle Rechnungen
        
		)
, SAP_businesspartner_current as (
	select  lnk.hk_rsap_business_partner_ocrd,  lnk.hk_bgnr_geschaeftspartner,b10_sat.kundengruppe 
	from bvlt_general.bgnr_geschaeftspartner_rsap_business_partner_lnk lnk
	join bvlt_general.bgnr_geschaeftspartner_rsap_business_partner_esat  esat 
		on esat.lk_bgnr_geschaeftspartner_rsap_business_partner = lnk.lk_bgnr_geschaeftspartner_rsap_business_partner 
		and esat.md_valid_before = lib.dwh_far_future_date()
		and not esat.md_is_deleted
	join bvlt_general.bgnr_geschaeftspartner_p1_b10_sat b10_sat 
		on b10_sat.hk_bgnr_geschaeftspartner = lnk.hk_bgnr_geschaeftspartner 
)
, sap_items_with_itmsgrpnam  as (
	select item_hub.hk_rsap_item_oitm,group_ref.itmsgrpnam ,item_sat.itmgrpcod 
	from rvlt_sap.rsap_item_oitm_hub item_hub
	join rvlt_sap.rsap_item_oitm_p1_l20_sat item_sat
		on item_sat.hk_rsap_item_oitm =item_hub.hk_rsap_item_oitm 
		and item_sat.md_valid_before = lib.dwh_far_future_date()
		and not item_sat.md_is_deleted
	left join bvlt_sap.bsap_item_group_oitb_current_ref group_ref
		on group_ref.company =item_hub.company 
		and group_ref.itmsgrpcod = item_sat.itmgrpcod 
)		
, fully_joined_and_aggregated_sap_dataset as (
	select measures.hk_rgnr_gesellschaft   			-- 1
		,businesspartner.hk_bgnr_geschaeftspartner  --2
		,businesspartner.kundengruppe				--3
		,DECODE(semp_sat.u_dim1,'10','HGH','20','GH','01','Fabrik','?'||semp_sat.u_dim1||'?') 	handelsplatz						--4
		,measures.docdate							--5
		,measures.ocrcode							--6
		,measures.ocrcode2							--7
		,measures.currency 							--8
		,sap_items.itmsgrpnam						--9
		,sap_items.itmgrpcod 						--10
		,sum(measures.linetotal_korrigiert) linetotal_korrigiert_sum
	from measures
	join sap_items_with_itmsgrpnam sap_items 
		 on sap_items.hk_rsap_item_oitm=measures.hk_rsap_item_oitm
	left join SAP_businesspartner_current businesspartner	
		on businesspartner.hk_rsap_business_partner_ocrd = measures.hk_rsap_business_partner_ocrd
	left join RVLT_SAP.RSAP_SALES_EMPLOYEE_OSLP_P1_L20_SAT	semp_sat
		on semp_sat.hk_rsap_sales_employee_oslp = measures.hk_rsap_sales_employee_oslp
		and semp_sat.md_valid_before = lib.dwh_far_future_date()
		and not semp_sat.md_is_deleted
	group by 1,2,3,4,5,6,7,8,9,10
	)
-- Final sap dataset with last lookups, in row calculations and target column naming
,final_sap_dataset as(	
select 
		gesellschaft_hub.company 								as Gesellschaft
		,fds.docdate											as tagesdatum
		,'---'													as kanal
		,fds.handelsplatz										as handelsplatz_aka_U_Dim1_von_vertriebsmitarbeiter
		,distrule_ref.ocrname									as verkaufsgebiet			
		,distrule_ref2.ocrname									as verkaeufernummer
		,fds.kundengruppe										as kundengruppe
		,fds.hk_bgnr_geschaeftspartner 							as dk_geschaeftspartner
		,fds.itmsgrpnam										    as artikelgruppe
		,fds.itmgrpcod											as artikelgruppe_code
		,round(fds.linetotal_korrigiert_sum,2)					as umsatz
		,fds.currency											as waehrung
		,case when fds.currency='EUR' 
			  then round(fds.linetotal_korrigiert_sum,2)
			  when fds.currency='CHF' and gesellschaft_hub.company='DEISS_CH'
              then round(fds.linetotal_korrigiert_sum*exch_eur_src.rate,2) -- Eurokurs bei DEISS_CH
              else round(fds.linetotal_korrigiert_sum/exch_de.rate,2) -- Fremdwährungskurs bei DEISS
			end 												as umsatz_eur
		,case when fds.currency='EUR'
			  then 1
			  when fds.currency='CHF' and gesellschaft_hub.company='DEISS_CH'
              then exch_eur_src.rate -- Eurokurs bei DEISS_CH
              else exch_de.rate -- Fremdwährungskurs bei DEISS
			end													as rate
from fully_joined_and_aggregated_sap_dataset fds
join DV_D_MAIN_DATABASE.RVLT_GENERAL.RGNR_GESELLSCHAFT_HUB gesellschaft_hub
    on gesellschaft_hub.hk_rgnr_gesellschaft = fds.hk_rgnr_gesellschaft
left join BVLT_SAP.BSAP_DISTRIBUTION_RULE_OOCR_CURRENT_REF distrule_ref
	on distrule_ref.company  = gesellschaft_hub.company 
	and distrule_ref.ocrcode =fds.ocrcode
left join BVLT_SAP.BSAP_DISTRIBUTION_RULE_OOCR_CURRENT_REF distrule_ref2
	on distrule_ref2.company  = gesellschaft_hub.company 
	and distrule_ref2.ocrcode =fds.ocrcode2
left join BVLT_SAP.BSAP_EXCHANGE_RATE_ORTT_CURRENT_REF exch_de
		on exch_de.ratedate = fds.docdate 
		and exch_de.currency = fds.currency			-- Fremdwährungskurs bei DEISS
		and exch_de.company = 'DEISS_DE'				-- Fremdwährungskurs bei DEISS
left join BVLT_SAP.BSAP_EXCHANGE_RATE_ORTT_CURRENT_REF exch_eur_src
		on exch_eur_src.ratedate = fds.docdate 
		and exch_eur_src.currency = 'EUR' 					-- Eurokurs der Quelle
		and exch_eur_src.company = gesellschaft_hub.company -- Eurokurs der Quelle
)		
---
, billbee_businesspartner_current as (
	select  lnk.hk_rbbe_shop 
		  , lnk.hk_bgnr_geschaeftspartner
		  ,b10_sat.kundengruppe 
		  ,b10_sat."NAME" 			geschaeftspartner_name
	from bvlt_general.bgnr_geschaeftspartner_rbbe_shop_lnk lnk
	join bvlt_general.bgnr_geschaeftspartner_rbbe_shop_esat  esat 
		on esat.lk_bgnr_geschaeftspartner_rbbe_shop = lnk.lk_bgnr_geschaeftspartner_rbbe_shop 
		and esat.md_valid_before = lib.dwh_far_future_date()
		and not esat.md_is_deleted
	join bvlt_general.bgnr_geschaeftspartner_p1_b10_sat b10_sat 
		on b10_sat.hk_bgnr_geschaeftspartner = lnk.hk_bgnr_geschaeftspartner 
	where lnk.hk_rbbe_shop <>'ffffffffffffffffffffffffffff' 
		)		
---		
, fully_joined_and_aggregated_billbee_dataset as (
select 
	bp_cur.hk_bgnr_geschaeftspartner
	,bp_cur.geschaeftspartner_name
	,date(coalesce(order_p2_s.invoiceDate,order_p2_s.payedat))						as invoice_date_as_date
	,'##wartet auf pim##' 								as artikelgruppe_source
	,'##wartet auf pim##'								as artikelgruppe_code_source
	,order_p1_s.currency 				
	,sum(item_korr_s.totalprice_netto_korrigiert) 		as total_price_netto_korrigiert_sum
from BVLT_BILLBEE.BBBE_ORDER_ITEM_PRODUCT_KORRIGIERT_V1_B10_CURRENT_SAT item_korr_s
join RVLT_BILLBEE.RBBE_ORDER_ITEM_ORDER_LNK item_order_l 
	on item_order_l.hk_rbbe_order_item =item_korr_s.hk_rbbe_order_item 
join RVLT_BILLBEE.RBBE_ORDER_ITEM_ORDER_esat item_order_e
	on item_order_e.lk_rbbe_order_item_order = item_order_l.lk_rbbe_order_item_order 
   and item_order_e.md_valid_before =lib.dwh_far_future_date()
   and not item_order_e.md_is_deleted
join RVLT_BILLBEE.rbbe_order_order_p1_l10_sat order_p1_s
	on order_p1_s.hk_rbbe_order = item_order_l.hk_rbbe_order 
   and order_p1_s.md_valid_before =lib.dwh_far_future_date()
   and not order_p1_s.md_is_deleted  
join RVLT_BILLBEE.rbbe_order_order_p2_l10_sat order_p2_s
	on order_p2_s.hk_rbbe_order = item_order_l.hk_rbbe_order 
   and order_p2_s.md_valid_before =lib.dwh_far_future_date()
   and not order_p2_s.md_is_deleted  
 join rvlt_billbee.rbbe_order_shop_lnk order_shop_l
 	on order_shop_l.hk_rbbe_order = item_order_l.hk_rbbe_order 
 join rvlt_billbee.rbbe_order_shop_p1_l10_sat order_shop_s
 	on order_shop_s.lk_rbbe_order_shop = order_shop_l.lk_rbbe_order_shop 
   and order_shop_s.md_valid_before =lib.dwh_far_future_date()
   and not order_shop_s.md_is_deleted   
 join billbee_businesspartner_current bp_cur 
    on bp_cur.hk_rbbe_shop=order_shop_l.hk_rbbe_shop 
 where geschaeftspartner_name<>'Testshop manuell' 
   and (order_p2_s.invoiceDate is not null or order_p2_s.payedat is not null)
 group by 1,2,3,4,5,6	
)
-- Final sap dataset with last lookups, in row calculations and target column naming
,final_billbee_dataset as(
select 
		'SUND_DIGITAL'			 								as Gesellschaft -- #### Achtung ist hier hard coded
		,fds.invoice_date_as_date								as tagesdatum
		,fds.geschaeftspartner_name								as kanal
		,'---'													as handelsplatz_aka_U_Dim1_von_vertriebsmitarbeiter
		,'---'									as verkaufsgebiet			
		,'---'									as verkaeufernummer
		,'---'										as kundengruppe
		,fds.hk_bgnr_geschaeftspartner 							as dk_geschaeftspartner
		,fds.artikelgruppe_source							    as artikelgruppe
		,fds.artikelgruppe_code_source							as artikelgruppe_code
		,round(fds.total_price_netto_korrigiert_sum,2)					as umsatz
		,fds.currency											as waehrung
		,case when (fds.currency<>'EUR') 
			then round(fds.total_price_netto_korrigiert_sum/exch.rate,2)		
			else round(fds.total_price_netto_korrigiert_sum,2)	
			end 												as umsatz_eur
		,exch.rate
from fully_joined_and_aggregated_billbee_dataset fds
left join BVLT_SAP.BSAP_EXCHANGE_RATE_ORTT_CURRENT_REF exch
		on exch.ratedate = fds.invoice_date_as_date 
		and exch.currency = fds.currency
		and exch.company = 'DEISS_DE'
)	
select * from final_sap_dataset
union all
select * from final_billbee_dataset
		;

	
Grant select on VIEW mart_auftraege.f_umsatz to role DV_D_ACCESS_MART_READ;