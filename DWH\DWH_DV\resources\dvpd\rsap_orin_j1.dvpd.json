{
	/* ============================================================
	 * DVPD配置文件：SAP贷项通知单头数据处理管道
	 * 文件名：rsap_orin_j1.dvpd.json
	 * 描述：此配置文件定义了从SAP系统提取贷项通知单头数据并加载到Data Vault模型的ETL流程
	 * 相关文件：
	 *   - rsap_rin1_j1.dvpd.json (贷项通知单行项目处理)
	 *   - rsap_oinv_j1.dvpd.json (发票处理)
	 *   - processes/rsap_orin_j1/__main__.py (处理主程序)
	 *   - processes/rsap_orin_j1/load_vault_1.py (数据加载模块)
	 * ============================================================ */

	/* DVPD版本号，用于确保配置文件与处理框架的兼容性 */
	"dvpd_version": "0.6.2",

	/* 定义数据加载的暂存区属性，包括模式名和表名 */
	/* 暂存表作为SAP源系统与Data Vault模型之间的缓冲区，便于数据验证和转换 */
	"stage_properties" : [{"stage_schema":"stage_rvlt","stage_table_name":"rsap_orin_j1_stage"}],

	/* 定义数据管道名称，通常与源系统表名相关 */
	/* 命名约定：rsap前缀表示SAP系统，orin表示贷项通知单头表，j1表示第一个作业 */
	"pipeline_name": "rsap_orin_j1",

	/* 定义数据来源标识，用于记录数据的来源系统 */
	/* 此标识符将被记录在所有目标表的RECORD_SOURCE列中，用于数据溯源 */
	"record_source_name_expression": "sap.orin",

	/* 数据提取配置，这里仅用于DDL生成，不执行实际提取 */
	/* 实际的数据提取逻辑在相应的Python模块中实现 */
	"data_extraction": {
		"fetch_module_name":"none - ddl generation only"
	},

	"fields": [
		      {"field_name": "company", 	"field_type": "Varchar(50)",	"targets": [{"table_name": "RSAP_CREDIT_MEMO_ORIN_HUB"},
																						{"table_name": "RSAP_BUSINESS_PARTNER_OCRD_HUB"},
																						{"table_name": "RSAP_SALES_EMPLOYEE_OSLP_HUB"}]},
		 	  {"field_name": "docentry",	"field_type": "INTEGER",	"targets": [{"table_name": "RSAP_CREDIT_MEMO_ORIN_HUB"}]},
			  {"field_name": "docnum",	"field_type": "INTEGER",	"targets": [{"table_name": "RSAP_CREDIT_MEMO_ORIN_HUB"}]},
			  {"field_name": "cardcode",	"field_type": "VARCHAR(20)",	"targets": [{"table_name": "RSAP_BUSINESS_PARTNER_OCRD_HUB"}]},
			  {"field_name": "slpcode",		"field_type": "VARCHAR(20)",	"targets": [{"table_name": "RSAP_SALES_EMPLOYEE_OSLP_HUB"}]},
		 	  {"field_name": "json_text",	"field_type": "VARCHAR",	"targets": [{"table_name": "RSAP_CREDIT_MEMO_ORIN_J1_L10_SAT",
								"exclude_json_paths_from_change_detection":["UpdateDate"]}]}
			 ],
\t/* ============================================================
	 * Data Vault模型定义部分
	 * 此部分定义了目标数据仓库中的表结构，包括Hub、Link和Satellite表
	 * 遵循Data Vault 2.0建模方法论，实现高度可扩展和灵活的数据模型
	 * ============================================================ */
	"data_vault_model": [
		{"schema_name": "rvlt_sap", /* SAP数据在Data Vault中的模式名 */
		 "tables": [
\t			/* ========== Hub表定义 ========== */
				/* 贷项通知单Hub表：存储贷项通知单业务键及其哈希值 */
				/* 作为贷项通知单实体的核心表，所有相关卫星表和链接表都引用此表 */
				{"table_name": "RSAP_CREDIT_MEMO_ORIN_HUB",
				"table_stereotype": "hub",
				"hub_key_column_name": "HK_RSAP_CREDIT_MEMO_ORIN"},

\t			/* ========== 贷项通知单相关卫星表 ========== */
				/* 贷项通知单详细信息卫星表：存储贷项通知单的完整JSON数据 */
				/* J1表示第一个作业，L10表示第10级别的加载优先级 */
				{"table_name": "RSAP_CREDIT_MEMO_ORIN_J1_L10_SAT",	
				"table_stereotype": "sat",
				"satellite_parent_table": "RSAP_CREDIT_MEMO_ORIN_HUB",
				"diff_hash_column_name": "RH_RSAP_CREDIT_MEMO_ORIN_J1_L10_SAT"},

				{"table_name": "RSAP_BUSINESS_PARTNER_OCRD_HUB",
				"table_stereotype": "hub",
				"hub_key_column_name": "HK_RSAP_BUSINESS_PARTNER_OCRD"},

				{"table_name": "RSAP_SALES_EMPLOYEE_OSLP_HUB",
				"table_stereotype": "hub",
				"hub_key_column_name": "HK_RSAP_SALES_EMPLOYEE_oslp"},

\t		/* ========== 关系链接表 ========== */
				/* 贷项通知单-业务伙伴-销售员链接表：存储三者之间的关系 */
				/* 这是一个多实体链接表，连接贷项通知单与相关的业务伙伴和销售员 */
				/* 结构上与发票模型中的链接表相似，但针对贷项通知单业务场景 */
				{"table_name": "RSAP_CREDIT_MEMO_ORIN_BUSINESS_PARTNER_OCRD_SALES_EMPLOYEE_OSLP_LNK",		
				"table_stereotype": "lnk",
				"link_key_column_name": "LK_RSAP_CREDIT_MEMO_ORIN_BUSINESS_PARTNER_OCRD_SALES_EMPLOYEE_OSLP",
				"link_parent_tables": ["RSAP_CREDIT_MEMO_ORIN_HUB",
									   "RSAP_BUSINESS_PARTNER_OCRD_HUB",
									   "RSAP_SALES_EMPLOYEE_OSLP_HUB"]},

				/* 贷项通知单-业务伙伴-销售员关系事件卫星表：跟踪三者关系的生命周期 */
				/* ESAT表示事件卫星表，用于记录关系的有效性 */
				/* driving_keys指定贷项通知单作为驱动键，表示贷项通知单变更会触发关系更新 */
				{"table_name": "RSAP_CREDIT_MEMO_ORIN_BUSINESS_PARTNER_OCRD_SALES_EMPLOYEE_OSLP_esat",
				"table_stereotype": "sat",
				"satellite_parent_table": "RSAP_CREDIT_MEMO_ORIN_BUSINESS_PARTNER_OCRD_SALES_EMPLOYEE_OSLP_LNK",
				"driving_keys": ["HK_RSAP_CREDIT_MEMO_ORIN"]}
				
				/* 注：此模型与rsap_rin1_j1.dvpd.json中的贷项通知单行模型相关联，共同构成完整的贷项通知单数据模型 */
				/* 同时，与rsap_oinv_j1.dvpd.json中的发票模型形成业务上的对应关系，两者共享业务伙伴和销售员Hub表 */

				]
		}
	]
}