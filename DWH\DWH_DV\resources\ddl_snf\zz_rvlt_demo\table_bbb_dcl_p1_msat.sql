-- generated script for zz_rvlt_demo.bbb_dcl_p1_msat

-- DROP TABLE zz_rvlt_demo.bbb_dcl_p1_msat;

CREATE TABLE zz_rvlt_demo.bbb_dcl_p1_msat (
MD_INSERTED_AT TIMESTAMP NOT NULL,
MD_JOB_INSTANCE_ID INT NOT NULL,
MD_RECORD_SOURCE VARCHAR(255) NOT NULL,
MD_IS_DELETED BOOLEAN NOT NULL,
MD_VALID_BEFORE TIMESTAMP NOT NULL,
LK_BBB_DCL CHAR(28) NOT NULL,
GH_BBB_BBB_P1_MSAT CHAR(28) NOT NULL,
BBB_DCL_P1_C1 VARCHAR(20)  NULL,
BBB_DCL_P1_C2F VARCHAR(100)  NULL
);

--COMMENT STATEMENTS

-- end of script --