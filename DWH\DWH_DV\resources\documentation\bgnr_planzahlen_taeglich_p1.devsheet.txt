Data vault pipeline developer cheat sheet 
rendered from  bgnr_planzahlen_taeglich_p1.dvpi

pipeline name:  bgnr_planzahlen_taeglich_p1

------------------------------------------------------
record source:  bgnr_planzahlen_taeglich_p1

Source fields:
       COMPANY           VARCHAR(50)
       TAGESDATUM        DATE
       GEPLANTER_UMSATZ  NUMBER(20,2)
       VERKAUFSGEBIET    VARCHAR(100)
       VERKAEUFERNUMMER  VARCHAR(100)
       KUNDENGRUPPE      VARCHAR(100)


------------------------------------------------------
Table List:
stage_table.bvlt_general.bgnr_planzahlen_taeglich_p1_stage
table.rvlt_general.rgnr_gesellschaft_kalender_dlnk
table.bvlt_general.bgnr_gesellschaft_tagesplanzahl_v1_b10_msat

------------------------------------------------------
stage table:  stage_bvlt.bgnr_planzahlen_taeglich_p1_stage
Field to Stage mapping:
	--business keys,
		COMPANY           >  COMPANY,
		TAGESDATUM        >  TAGESDATUM,

	--content,
		GEPLANTER_UMSATZ  >  GEPLANTER_UMSATZ,
		KUNDENGRUPPE      >  KUNDENGRUPPE,
		VERKAEUFERNUMMER  >  VERKAEUFERNUMMER,
		VERKAUFSGEBIET    >  VERKAUFSGEBIET

------------------------------------------------------
Hash value composition

HK_RGNR_GESELLSCHAFT (key)
		COMPANY 

LK_RGNR_GESELLSCHAFT_KALENDER (key)
		TAGESDATUM 
		COMPANY 

GH_BGNR_GESELLSCHAFT_TAGESPLANZAHL_V1_B10_MSAT (diff_hash)
		GEPLANTER_UMSATZ 
		KUNDENGRUPPE 
		VERKAEUFERNUMMER 
		VERKAUFSGEBIET 


------------------------------------------------------
Table load method
(STAGE >  VAULT)

rgnr_gesellschaft_kalender_dlnk (/) can be loaded by convention
		  parent_key_1: HK_RGNR_GESELLSCHAFT  >  HK_RGNR_GESELLSCHAFT
		  key: LK_RGNR_GESELLSCHAFT_KALENDER  >  LK_RGNR_GESELLSCHAFT_KALENDER
		  dependent_child_key: TAGESDATUM  >  TAGESDATUM 

bgnr_gesellschaft_tagesplanzahl_v1_b10_msat (/) can be loaded by convention
		  parent_key: LK_RGNR_GESELLSCHAFT_KALENDER  >  LK_RGNR_GESELLSCHAFT_KALENDER
		  diff_hash: GH_BGNR_GESELLSCHAFT_TAGESPLANZAHL_V1_B10_MSAT  >  GH_BGNR_GESELLSCHAFT_TAGESPLANZAHL_V1_B10_MSAT
		  content: GEPLANTER_UMSATZ  >  GEPLANTER_UMSATZ 
		  content: VERKAUFSGEBIET  >  VERKAUFSGEBIET 
		  content: VERKAEUFERNUMMER  >  VERKAEUFERNUMMER 
		  content: KUNDENGRUPPE  >  KUNDENGRUPPE 

