# 数据仓库项目指南：非技术人员版

## 文档目录

本指南分为五个部分，旨在帮助没有IT背景的业务人员理解我们的数据仓库项目。每个部分都侧重于不同的主题，从基础概念到具体应用。

### [第1部分：基础概念](非技术人员数据仓库指南_第1部分_基础概念.md)

- 引言
- 什么是数据仓库？
- 我们项目的架构
- Data Vault 2.0简单解释
- 数据流程：从源系统到报表
- 项目中的层次结构

### [第2部分：Data Vault建模](非技术人员数据仓库指南_第2部分_Data_Vault建模.md)

- Data Vault建模详解
  - Hub（中心）：业务核心实体
  - Link（链接）：实体之间的关系
  - Satellite（卫星）：描述性信息和历史变化
- 以发票数据为例解释Data Vault模型
  - 模型解释
  - 虚拟数据示例

### [第3部分：ETL流程和DVPD](非技术人员数据仓库指南_第3部分_ETL流程和DVPD.md)

- ETL流程：数据如何从源系统到达数据仓库
  - 数据流程概述
  - 以SAP发票数据为例
- DVPD和DVPI：自动化数据加载
  - DVPD：Data Vault Pipeline Definition
  - DVPI：Data Vault Pipeline Implementation
- 以截图中的例子详解
  - 模型解释
  - 数据流程示例

### [第4部分：业务应用和案例研究](非技术人员数据仓库指南_第4部分_业务应用和案例研究.md)

- 业务应用：数据仓库如何支持业务决策
  - 数据仓库的业务价值
  - 我们项目中的主要业务用例
- 案例研究：销售报告流程
  - 业务需求
  - 数据源
  - 数据流程详解
  - 虚拟数据示例
  - 最终报告示例

### [第5部分：术语表和常见问题](非技术人员数据仓库指南_第5部分_术语表和常见问题.md)

- 术语表
  - 一般术语
  - Data Vault相关术语
  - 项目特定术语
  - 模式名称前缀
  - 表名前缀
- 常见问题
  - 什么是Data Vault，为什么我们选择它？
  - 数据仓库与源系统（如SAP）有什么区别？
  - Raw Vault、Business Vault和Data Mart有什么区别？
  - 为什么数据在多个表中，而不是一个大表？
  - 如何请求新的报表或分析？
  - 数据更新的频率是多少？
  - 如何确保数据的准确性？
  - 我可以直接访问数据仓库吗？
  - 什么是DVPD和DVPI，它们有什么用？
  - 如何理解数据模型图中的不同形状和颜色？

## 如何使用本指南

1. 如果您是数据仓库的新用户，建议从第1部分开始，按顺序阅读
2. 如果您对特定主题感兴趣，可以直接跳到相关部分
3. 如果您遇到不熟悉的术语，请参考第5部分的术语表
4. 如果您有特定问题，可以查看第5部分的常见问题

## 联系信息

如果您有任何问题或需要进一步的解释，请联系数据团队：

- 电子邮件：<EMAIL>
- 内部聊天：#data-warehouse-support
