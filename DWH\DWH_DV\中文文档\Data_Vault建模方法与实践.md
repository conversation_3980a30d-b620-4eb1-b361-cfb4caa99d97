﻿# Data Vault建模方法与实践

## 1. 项目概述

本项目采用Data Vault 2.0架构实现企业数据仓库，分为三层结构：
- Raw Vault (RVLT_)：存储原始数据，保持历史记录
- Business Vault (BVLT_)：应用业务规则转换数据
- Data Mart (MART_)：面向业务用户的数据集市

## 2. Data Vault核心组件详解

### 2.1 Hub表
Hub表存储业务实体的唯一标识符，例如：
- RSAP_CREDIT_MEMO_LINE_RIN1_HUB：贷项通知单行Hub
- RSAP_CREDIT_MEMO_ORIN_HUB：贷项通知单头Hub

### 2.2 Link表
Link表存储业务实体之间的关系，例如：
- RSAP_CREDIT_MEMO_LINE_TO_HEADER_LNK：贷项通知单行与头的关系

### 2.3 Satellite表
Satellite表存储业务实体的描述性属性，例如：
- RSAP_CREDIT_MEMO_LINE_RIN1_P1_L20_SAT：贷项通知单行属性
- RSAP_CREDIT_MEMO_ORIN_P1_L20_SAT：贷项通知单头属性

## 3. 项目目录结构解析

### 3.1 resources/ddl_snf目录
- rvlt_sap：Raw Vault层SAP相关表定义
- bvlt_sap：Business Vault层SAP相关视图和表
- mart_auftraege：数据集市层订单报表相关视图

### 3.2 processes目录
包含各种ETL处理脚本，如：
- rsap_rin1_j1：处理SAP贷项通知单行数据
- rsap_orin_j1：处理SAP贷项通知单头数据

## 4. 命名规范体系

### 4.1 模式前缀
- RVLT_：Raw Vault层
- BVLT_：Business Vault层
- MART_：Data Mart层

### 4.2 业务领域标识
- RSAP_：SAP系统数据
- RBBE_：Billbee系统数据

### 4.3 表类型后缀
- _HUB：Hub表
- _LNK：Link表
- _SAT：Satellite表

### 4.4 Satellite特殊标记
- _j1_l10：原始数据层级
- _v1_b10：业务转换数据层级

## 5. 贷项通知单行校正视图案例分析

视图sap_credit_memo_line_rin1_korrigiert_v1_b10_current_sat实现了贷项通知单行数据的业务校正逻辑，包含三个核心CTE：

### 5.1 gesamtnetto_current CTE
计算文档净总额和方向因子，处理正负值转换。

### 5.2 line_fipp_bonus_current CTE
专门处理FIPP_DE公司的奖金行汇总逻辑。

### 5.3 netto_anteil CTE
计算各行净额占比并应用折扣分配算法。

## 6. 项目总结

Data Vault模型在本项目中的主要优势：
- 保持完整历史数据
- 灵活适应业务变化
- 支持多源系统集成
- 提供统一业务视图

通过三层架构和标准化命名规范，项目实现了从原始数据到业务报表的完整数据流，满足了企业分析需求。