Data vault pipeline developer cheat sheet 
rendered from  rsap_oitb_j1.dvpi

pipeline name:  rsap_oitb_j1

------------------------------------------------------
record source:  sap.oitb

Source fields:
       COMPANY     VARCHAR(50)
       ITMSGRPCOD  VARCHAR(20)
       JSON_TEXT   VARCHAR


------------------------------------------------------
Table List:
stage_table.rvlt_sap.rsap_oitb_j1_stage
table.rvlt_sap.rsap_item_group_oitb_hub
table.rvlt_sap.rsap_item_group_oitb_j1_l10_sat

------------------------------------------------------
stage table:  stage_rvlt.rsap_oitb_j1_stage
Field to Stage mapping:
	--business keys,
		COMPANY     >  COMPANY,
		ITMSGRPCOD  >  ITMSGRPCOD,

	--content,
		JSON_TEXT   >  JSON_TEXT

------------------------------------------------------
Hash value composition

HK_RSAP_ITEM_GROUP_OITB (key)
		COMPANY 
		ITMSGRPCOD 

RH_RSAP_ITEM_GROUP_OITB_J1_L10_SAT (diff_hash)
		JSON_TEXT 


------------------------------------------------------
Table load method
(STAGE >  VAULT)

rsap_item_group_oitb_hub (/) can be loaded by convention
		  key: HK_RSAP_ITEM_GROUP_OITB  >  HK_RSAP_ITEM_GROUP_OITB
		  business_key: COMPANY  >  COMPANY 
		  business_key: ITMSGRPCOD  >  ITMSGRPCOD 

rsap_item_group_oitb_j1_l10_sat (/) can be loaded by convention
		  parent_key: HK_RSAP_ITEM_GROUP_OITB  >  HK_RSAP_ITEM_GROUP_OITB
		  diff_hash: RH_RSAP_ITEM_GROUP_OITB_J1_L10_SAT  >  RH_RSAP_ITEM_GROUP_OITB_J1_L10_SAT
		  content: JSON_TEXT  >  JSON_TEXT 

