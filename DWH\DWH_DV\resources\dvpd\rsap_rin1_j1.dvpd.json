{
	/* ============================================================
	 * DVPD配置文件：SAP贷项通知单行数据处理管道
	 * 文件名：rsap_rin1_j1.dvpd.json
	 * 描述：此配置文件定义了从SAP系统提取贷项通知单行数据并加载到Data Vault模型的ETL流程
	 * 相关文件：
	 *   - rsap_orin_j1.dvpd.json (贷项通知单头数据处理)
	 *   - rsap_inv1_j1.dvpd.json (发票行数据处理)
	 *   - processes/rsap_rin1_j1/__main__.py (处理主程序)
	 *   - processes/rsap_rin1_j1/load_vault_1.py (数据加载模块)
	 * ============================================================ */

	/* DVPD版本号，用于确保配置文件与处理框架的兼容性 */
	"dvpd_version": "0.6.2",

	/* 定义数据加载的暂存区属性，包括模式名和表名 */
	/* 暂存表作为SAP源系统与Data Vault模型之间的缓冲区，便于数据验证和转换 */
	"stage_properties" : [{"stage_schema":"stage_rvlt","stage_table_name":"rsap_rin1_j1_stage"}],

	/* 定义数据管道名称，通常与源系统表名相关 */
	/* 命名约定：rsap前缀表示SAP系统，rin1表示贷项通知单行表，j1表示第一个作业 */
	"pipeline_name": "rsap_rin1_j1",

	/* 定义数据来源标识，用于记录数据的来源系统 */
	/* 此标识符将被记录在所有目标表的RECORD_SOURCE列中，用于数据溯源 */
	"record_source_name_expression": "sap.rin1",

	/* 数据提取配置，这里仅用于DDL生成，不执行实际提取 */
	/* 实际的数据提取逻辑在相应的Python模块中实现 */
	"data_extraction": {
		"fetch_module_name":"none - ddl generation only"
	},

	/* ============================================================
	 * 字段映射部分：定义源系统字段如何映射到Data Vault模型中的表
	 * 每个字段定义包含：字段名称、数据类型和目标表映射
	 * ============================================================ */
	"fields": [
		      /* company字段映射到多个Hub表，表示这是一个跨实体的业务键组成部分 */
		      /* 在SAP环境中，公司代码是区分不同法律实体的关键标识符 */
		      {"field_name": "company", 	"field_type": "Varchar(50)",	"targets": [{"table_name": "RSAP_CREDIT_MEMO_LINE_RIN1_HUB"},{"table_name": "rsap_item_oitm_hub"}]},
		 	  /* docentry是贷项通知单行Hub的主要业务键之一 */
		 	  /* 在SAP中，docentry是文档的唯一内部标识符 */
		 	  {"field_name": "docentry",	"field_type": "INTEGER",	"targets": [{"table_name": "RSAP_CREDIT_MEMO_LINE_RIN1_HUB"}]},
			  /* linenum是贷项通知单行Hub的主要业务键之一，标识行项目编号 */
			  /* 与docentry一起构成贷项通知单行的唯一标识 */
			  {"field_name": "linenum",	"field_type": "INTEGER",	"targets": [{"table_name": "RSAP_CREDIT_MEMO_LINE_RIN1_HUB"}]},
			  /* itemcode映射到物料Hub，用于建立贷项通知单行与物料的关联 */
			  /* 物料代码标识产品或服务项目 */
			  {"field_name": "itemcode",	"field_type": "Varchar(50)",	"targets": [{"table_name": "rsap_item_oitm_hub"}]},
		 	  /* json_text字段存储完整的贷项通知单行JSON数据，用于保留所有原始信息 */
		 	  /* exclude_json_paths_from_change_detection属性指定某些路径不参与变更检测 */
		 	  /* 例如，UpdateDate字段的变化不会触发新版本的创建，避免无意义的历史记录 */
		 	  {"field_name": "json_text",	"field_type": "VARCHAR",	"targets": [{"table_name": "RSAP_CREDIT_MEMO_LINE_RIN1_J1_L10_SAT",
								"exclude_json_paths_from_change_detection":["UpdateDate"]}]}
			 ],
	/* ============================================================
	 * Data Vault模型定义部分
	 * 此部分定义了目标数据仓库中的表结构，包括Hub、Link和Satellite表
	 * 遵循Data Vault 2.0建模方法论，实现高度可扩展和灵活的数据模型
	 * ============================================================ */
	"data_vault_model": [
		/* 定义模式名称，通常按照业务领域或源系统分组 */
		{"schema_name": "rvlt_sap", 
		 /* 定义该模式下的所有表 */
		 "tables": [
				/* 贷项通知单行Hub表定义：存储贷项通知单行的业务键和哈希键 */
				{"table_name": "RSAP_CREDIT_MEMO_LINE_RIN1_HUB",
				"table_stereotype": "hub", /* 表类型为hub，表示业务实体 */
				"hub_key_column_name": "HK_RSAP_CREDIT_MEMO_LINE_RIN1"}, /* 哈希键列名，用于唯一标识贷项通知单行 */

				/* 贷项通知单行卫星表定义：存储贷项通知单行的详细属性和历史变化 */
				{"table_name": "RSAP_CREDIT_MEMO_LINE_RIN1_J1_L10_SAT",	
				"table_stereotype": "sat", /* 表类型为sat(卫星表)，存储描述性数据 */
				"satellite_parent_table": "RSAP_CREDIT_MEMO_LINE_RIN1_HUB", /* 关联的父Hub表 */
				"diff_hash_column_name": "RH_RSAP_CREDIT_MEMO_LINE_RIN1_J1_L10_SAT"}, /* 差异哈希值列名，用于检测数据变化 */

				/* 物料Hub表定义：存储物料的业务键和哈希键 */
			 	{"table_name": "rsap_item_oitm_hub",
				 "table_stereotype": "hub", /* 表类型为hub，表示业务实体 */
				 "hub_key_column_name": "hk_rsap_item_oitm"}, /* 哈希键列名，用于唯一标识物料 */

				/* 贷项通知单行-物料链接表：存储两者之间的关系 */
			 	{"table_name": "RSAP_CREDIT_MEMO_LINE_RIN1_ITEM_OITM_LNK",
				"table_stereotype": "lnk", /* 表类型为lnk(链接表)，表示实体间关系 */
				"link_key_column_name": "LK_RSAP_CREDIT_MEMO_LINE_RIN1_ITEM_OITM", /* 链接键列名 */
				/* 定义链接表关联的父Hub表，表示这两个实体之间的关系 */
				"link_parent_tables": ["RSAP_CREDIT_MEMO_LINE_RIN1_HUB","rsap_item_oitm_hub"]},

				/* 贷项通知单行-物料关系事件卫星表：跟踪两者关系的生命周期 */
				/* ESAT表示事件卫星表，用于记录关系的有效性 */
				/* driving_keys指定贷项通知单行作为驱动键，表示贷项通知单行变更会触发关系更新 */
				{"table_name": "RSAP_CREDIT_MEMO_LINE_RIN1_ITEM_OITM_ESAT",
				"table_stereotype": "sat", /* 表类型为sat(卫星表)，但特殊用途为事件卫星表 */
				"satellite_parent_table": "RSAP_CREDIT_MEMO_LINE_RIN1_ITEM_OITM_LNK", /* 关联的父链接表 */
				"driving_keys": ["HK_RSAP_CREDIT_MEMO_LINE_RIN1"]} /* 驱动键，表示以贷项通知单行为主导实体 */
				
				/* 注：此模型与rsap_orin_j1.dvpd.json中的贷项通知单头模型相关联，共同构成完整的贷项通知单数据模型 */
				/* 同时，与rsap_inv1_j1.dvpd.json中的发票行模型形成业务上的对应关系 */

				]
		}
	]
}