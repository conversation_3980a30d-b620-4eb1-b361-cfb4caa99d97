{"dvdp_compiler": "dvpdc reference compiler,  release 0.6.2", "dvpi_version": "0.6.2", "compile_timestamp": "2024-11-12 18:54:51", "dvpd_version": "0.6.2", "pipeline_name": "bgnr_kalender_p1", "dvpd_filename": "bgnr_kalender_p1.dvpd.json", "tables": [{"table_name": "rgnr_gesellschaft_hub", "table_stereotype": "hub", "schema_name": "rvlt_general", "storage_component": "", "columns": [{"column_name": "MD_INSERTED_AT", "is_nullable": false, "column_class": "meta_load_date", "column_type": "TIMESTAMP"}, {"column_name": "MD_RUN_ID", "is_nullable": false, "column_class": "meta_load_process_id", "column_type": "INT"}, {"column_name": "MD_RECORD_SOURCE", "is_nullable": false, "column_class": "meta_record_source", "column_type": "VARCHAR(255)"}, {"column_name": "HK_RGNR_GESELLSCHAFT", "is_nullable": false, "column_class": "key", "column_type": "CHAR(28)"}, {"column_name": "COMPANY", "is_nullable": true, "column_class": "business_key", "column_type": "VARCHAR(50)", "prio_for_column_position": 50000}]}, {"table_name": "rgnr_gesellschaft_kalender_dlnk", "table_stereotype": "lnk", "schema_name": "rvlt_general", "storage_component": "", "columns": [{"column_name": "MD_INSERTED_AT", "is_nullable": false, "column_class": "meta_load_date", "column_type": "TIMESTAMP"}, {"column_name": "MD_RUN_ID", "is_nullable": false, "column_class": "meta_load_process_id", "column_type": "INT"}, {"column_name": "MD_RECORD_SOURCE", "is_nullable": false, "column_class": "meta_record_source", "column_type": "VARCHAR(255)"}, {"column_name": "HK_RGNR_GESELLSCHAFT", "is_nullable": false, "column_class": "parent_key", "column_type": "CHAR(28)", "parent_key_column_name": "HK_RGNR_GESELLSCHAFT", "parent_table_name": "rgnr_gesellschaft_hub"}, {"column_name": "LK_RGNR_GESELLSCHAFT_KALENDER", "is_nullable": false, "column_class": "key", "column_type": "CHAR(28)"}, {"column_name": "TAGESDATUM", "is_nullable": true, "column_class": "dependent_child_key", "column_type": "DATE", "prio_for_column_position": 50000}]}, {"table_name": "bgnr_gesellschaft_kalender_p1_b10_sat", "table_stereotype": "sat", "schema_name": "bvlt_general", "storage_component": "", "has_deletion_flag": true, "is_effectivity_sat": false, "is_enddated": true, "is_multiactive": false, "compare_criteria": "key+current", "uses_diff_hash": true, "columns": [{"column_name": "MD_INSERTED_AT", "is_nullable": false, "column_class": "meta_load_date", "column_type": "TIMESTAMP"}, {"column_name": "MD_RUN_ID", "is_nullable": false, "column_class": "meta_load_process_id", "column_type": "INT"}, {"column_name": "MD_RECORD_SOURCE", "is_nullable": false, "column_class": "meta_record_source", "column_type": "VARCHAR(255)"}, {"column_name": "MD_IS_DELETED", "is_nullable": false, "column_class": "meta_deletion_flag", "column_type": "BOOLEAN"}, {"column_name": "MD_VALID_BEFORE", "is_nullable": false, "column_class": "meta_load_enddate", "column_type": "TIMESTAMP"}, {"column_name": "LK_RGNR_GESELLSCHAFT_KALENDER", "is_nullable": false, "column_class": "parent_key", "column_type": "CHAR(28)", "parent_key_column_name": "LK_RGNR_GESELLSCHAFT_KALENDER", "parent_table_name": "rgnr_gesellschaft_kalender_dlnk"}, {"column_name": "RH_BGNR_GESELLSCHAFT_KALENDER_P1_B10_SAT", "is_nullable": false, "column_class": "diff_hash", "column_type": "CHAR(28)"}, {"column_name": "IST_WERKTAG", "is_nullable": true, "column_class": "content", "column_type": "BOOLEAN", "exclude_from_change_detection": false, "prio_for_column_position": 50000}, {"column_name": "BEMERKUNG", "is_nullable": true, "column_class": "content", "column_type": "VARCHAR(2000)", "exclude_from_change_detection": false, "prio_for_column_position": 50000}]}], "data_extraction": {"fetch_module_name": "none - ddl generation only"}, "parse_sets": [{"stage_properties": [{"stage_schema": "stage_bvlt", "stage_table_name": "bgnr_kalender_p1_stage", "storage_component": ""}], "record_source_name_expression": "bgnr_kalender_p1", "fields": [{"field_type": "<PERSON><PERSON><PERSON><PERSON>(50)", "field_position": 1, "needs_encryption": false, "field_name": "COMPANY"}, {"field_type": "DATE", "field_position": 2, "needs_encryption": false, "field_name": "TAGESDATUM"}, {"field_type": "BOOLEAN", "field_position": 3, "needs_encryption": false, "field_name": "IST_WERKTAG"}, {"field_type": "<PERSON><PERSON><PERSON>(2000)", "field_position": 4, "needs_encryption": false, "field_name": "RMRKS"}], "hashes": [{"stage_column_name": "HK_RGNR_GESELLSCHAFT", "hash_origin_table": "rgnr_gesellschaft_hub", "column_class": "key", "hash_fields": [{"field_name": "COMPANY", "prio_in_key_hash": 0, "field_target_table": "rgnr_gesellschaft_hub", "field_target_column": "COMPANY"}], "column_type": "CHAR(28)", "hash_encoding": "BASE64", "hash_function": "sha-1", "hash_concatenation_seperator": "|", "hash_timestamp_format_sqlstyle": "YYYY-MM-DD HH24:MI:SS.US", "hash_null_value_string": "", "model_profile_name": "_default", "hash_name": "KEY_OF_RGNR_GESELLSCHAFT_HUB"}, {"stage_column_name": "LK_RGNR_GESELLSCHAFT_KALENDER", "hash_origin_table": "rgnr_gesellschaft_kalender_dlnk", "column_class": "key", "hash_fields": [{"field_name": "COMPANY", "prio_in_key_hash": 0, "field_target_table": "rgnr_gesellschaft_hub", "field_target_column": "COMPANY", "parent_declaration_position": 1}, {"field_name": "TAGESDATUM", "prio_in_key_hash": 0, "field_target_table": "rgnr_gesellschaft_kalender_dlnk", "field_target_column": "TAGESDATUM"}], "column_type": "CHAR(28)", "hash_encoding": "BASE64", "hash_function": "sha-1", "hash_concatenation_seperator": "|", "hash_timestamp_format_sqlstyle": "YYYY-MM-DD HH24:MI:SS.US", "hash_null_value_string": "", "model_profile_name": "_default", "hash_name": "KEY_OF_RGNR_GESELLSCHAFT_KALENDER_DLNK"}, {"stage_column_name": "RH_BGNR_GESELLSCHAFT_KALENDER_P1_B10_SAT", "hash_origin_table": "bgnr_gesellschaft_kalender_p1_b10_sat", "multi_row_content": false, "column_class": "diff_hash", "hash_fields": [{"field_name": "IST_WERKTAG", "prio_in_diff_hash": 0, "prio_for_row_order": 50000, "field_target_table": "bgnr_gesellschaft_kalender_p1_b10_sat", "field_target_column": "IST_WERKTAG"}, {"field_name": "RMRKS", "prio_in_diff_hash": 0, "prio_for_row_order": 50000, "field_target_table": "bgnr_gesellschaft_kalender_p1_b10_sat", "field_target_column": "BEMERKUNG"}], "column_type": "CHAR(28)", "hash_encoding": "BASE64", "hash_function": "sha-1", "hash_concatenation_seperator": "|", "hash_timestamp_format_sqlstyle": "YYYY-MM-DD HH24:MI:SS.US", "hash_null_value_string": "", "model_profile_name": "_default", "related_key_hash": "KEY_OF_RGNR_GESELLSCHAFT_KALENDER_DLNK", "hash_name": "DIFF_OF_BGNR_GESELLSCHAFT_KALENDER_P1_B10_SAT"}], "load_operations": [{"table_name": "rgnr_gesellschaft_hub", "relation_name": "/", "operation_origin": "field mapping relation", "hash_mappings": [{"hash_class": "key", "column_name": "HK_RGNR_GESELLSCHAFT", "is_nullable": false, "hash_name": "KEY_OF_RGNR_GESELLSCHAFT_HUB", "stage_column_name": "HK_RGNR_GESELLSCHAFT"}], "data_mapping": [{"column_name": "COMPANY", "field_name": "COMPANY", "column_class": "business_key", "is_nullable": true, "stage_column_name": "COMPANY"}]}, {"table_name": "rgnr_gesellschaft_kalender_dlnk", "relation_name": "/", "operation_origin": "field mapping relation", "hash_mappings": [{"hash_class": "parent_key_1", "column_name": "HK_RGNR_GESELLSCHAFT", "is_nullable": false, "hash_name": "KEY_OF_RGNR_GESELLSCHAFT_HUB", "stage_column_name": "HK_RGNR_GESELLSCHAFT"}, {"hash_class": "key", "column_name": "LK_RGNR_GESELLSCHAFT_KALENDER", "is_nullable": false, "hash_name": "KEY_OF_RGNR_GESELLSCHAFT_KALENDER_DLNK", "stage_column_name": "LK_RGNR_GESELLSCHAFT_KALENDER"}], "data_mapping": [{"column_name": "TAGESDATUM", "field_name": "TAGESDATUM", "column_class": "dependent_child_key", "is_nullable": true, "stage_column_name": "TAGESDATUM"}]}, {"table_name": "bgnr_gesellschaft_kalender_p1_b10_sat", "relation_name": "/", "operation_origin": "field mapping relation", "hash_mappings": [{"hash_class": "parent_key", "column_name": "LK_RGNR_GESELLSCHAFT_KALENDER", "is_nullable": false, "hash_name": "KEY_OF_RGNR_GESELLSCHAFT_KALENDER_DLNK", "stage_column_name": "LK_RGNR_GESELLSCHAFT_KALENDER"}, {"hash_class": "diff_hash", "column_name": "RH_BGNR_GESELLSCHAFT_KALENDER_P1_B10_SAT", "is_nullable": false, "hash_name": "DIFF_OF_BGNR_GESELLSCHAFT_KALENDER_P1_B10_SAT", "stage_column_name": "RH_BGNR_GESELLSCHAFT_KALENDER_P1_B10_SAT"}], "data_mapping": [{"column_name": "IST_WERKTAG", "field_name": "IST_WERKTAG", "column_class": "content", "is_nullable": true, "stage_column_name": "IST_WERKTAG"}, {"column_name": "BEMERKUNG", "field_name": "RMRKS", "column_class": "content", "is_nullable": true, "stage_column_name": "RMRKS"}]}], "stage_columns": [{"stage_column_name": "MD_INSERTED_AT", "is_nullable": false, "stage_column_class": "meta_load_date", "column_type": "TIMESTAMP"}, {"stage_column_name": "MD_RUN_ID", "is_nullable": false, "stage_column_class": "meta_load_process_id", "column_type": "INT"}, {"stage_column_name": "MD_RECORD_SOURCE", "is_nullable": false, "stage_column_class": "meta_record_source", "column_type": "VARCHAR(255)"}, {"stage_column_name": "MD_IS_DELETED", "is_nullable": false, "stage_column_class": "meta_deletion_flag", "column_type": "BOOLEAN"}, {"stage_column_name": "HK_RGNR_GESELLSCHAFT", "stage_column_class": "hash", "is_nullable": false, "hash_name": "KEY_OF_RGNR_GESELLSCHAFT_HUB", "column_type": "CHAR(28)"}, {"stage_column_name": "LK_RGNR_GESELLSCHAFT_KALENDER", "stage_column_class": "hash", "is_nullable": false, "hash_name": "KEY_OF_RGNR_GESELLSCHAFT_KALENDER_DLNK", "column_type": "CHAR(28)"}, {"stage_column_name": "RH_BGNR_GESELLSCHAFT_KALENDER_P1_B10_SAT", "stage_column_class": "hash", "is_nullable": false, "hash_name": "DIFF_OF_BGNR_GESELLSCHAFT_KALENDER_P1_B10_SAT", "column_type": "CHAR(28)"}, {"stage_column_name": "COMPANY", "stage_column_class": "data", "field_name": "COMPANY", "is_nullable": true, "column_type": "<PERSON><PERSON><PERSON><PERSON>(50)", "column_classes": ["business_key"]}, {"stage_column_name": "TAGESDATUM", "stage_column_class": "data", "field_name": "TAGESDATUM", "is_nullable": true, "column_type": "DATE", "column_classes": ["dependent_child_key"]}, {"stage_column_name": "IST_WERKTAG", "stage_column_class": "data", "field_name": "IST_WERKTAG", "is_nullable": true, "column_type": "BOOLEAN", "column_classes": ["content"]}, {"stage_column_name": "RMRKS", "stage_column_class": "data", "field_name": "RMRKS", "is_nullable": true, "column_type": "<PERSON><PERSON><PERSON>(2000)", "column_classes": ["content"]}]}]}