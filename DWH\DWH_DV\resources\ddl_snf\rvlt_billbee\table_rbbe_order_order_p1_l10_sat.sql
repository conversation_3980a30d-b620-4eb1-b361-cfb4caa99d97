-- generated script for rvlt_billbee.rbbe_order_order_p1_l10_sat

-- DROP TABLE rvlt_billbee.rbbe_order_order_p1_l10_sat;

CREATE TABLE rvlt_billbee.rbbe_order_order_p1_l10_sat (
MD_INSERTED_AT TIMESTAMP NOT NULL,
MD_<PERSON>UN_ID INT NOT NULL,
MD_RECORD_SOURCE VARCHAR(255) NOT NULL,
MD_IS_DELETED BOOLEAN NOT NULL,
MD_VALID_BEFORE TIMESTAMP NOT NULL,
HK_RBBE_ORDER CHAR(28) NOT NULL,
RH_RBBE_ORDER_ORDER_P1_L10_SAT CHAR(28) NOT NULL,
OR<PERSON><PERSON>NUMBER VARCHAR(200) NULL,
TOTALCOST NUMBER(20,2) NULL,
ADJUSTMENTCOST NUMBER(20,2) NULL,
CURRENCY VARCHAR(200) NULL,
SHIPPINGCOST NUMBER(20,2) NULL,
INVOICEADDRESS_COUNTRYISO2 VARCHAR(20) NULL,
UPDATEDAT TIMESTAMP NULL,
LASTMODIFIEDAT TIMESTAMP NULL
);

--COMMENT STATEMENTS

-- end of script --