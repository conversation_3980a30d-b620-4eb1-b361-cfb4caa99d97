# Data Vault模型详解：数据流程和业务场景

## 引言

本文档将详细解释Data Vault模型中的数据流程和业务场景，帮助非技术人员理解数据如何在模型中流动，以及这些数据如何支持业务决策。我们将以提供的完整图示为例，通过具体的业务场景来说明整个过程。

## 数据流程概述

在Data Vault模型中，数据按照以下流程处理：

1. **数据提取**：从源系统（如SAP）获取原始数据
2. **加载到Raw Vault**：将原始数据加载到Hub、Link和L10层Satellite表
3. **数据处理**：处理原始数据，生成结构化数据，存储在L20层Satellite表
4. **业务转换**：在Business Vault中应用业务规则，生成业务视图
5. **数据集成**：在Data Mart中整合数据，供业务分析使用

## 详细数据流程说明

### 1. 数据提取

数据从源系统提取的过程：

- **SAP数据**：通过Lobster工具从SAP数据库提取数据
- **数据格式**：转换为JSON格式，便于处理和存储
- **数据源表**：如OINV（发票头）、INV1（发票行）、INV2（发票行扩展）等

### 2. 加载到Raw Vault

原始数据加载到Raw Vault的过程：

#### 2.1 Hub表加载

- **发票行Hub**：`invoice_line_inv1`
  - 加载业务键：公司代码、文档编号、行号
  - 生成哈希键：HK_RSAP_INVOICE_LINE_INV1
  - 记录元数据：数据来源、加载时间

- **发票头Hub**：`invoice_oinv`
  - 加载业务键：公司代码、文档编号
  - 生成哈希键：HK_RSAP_INVOICE_OINV
  - 记录元数据：数据来源、加载时间

- **物料Hub**：`item_oitm`
  - 加载业务键：公司代码、物料代码
  - 生成哈希键：HK_RSAP_ITEM_OITM
  - 记录元数据：数据来源、加载时间

#### 2.2 Link表加载

- **发票行-发票头Link**：`invoice_line_inv1_invoice`
  - 引用发票行Hub的哈希键
  - 引用发票头Hub的哈希键
  - 生成链接哈希键：LK_RSAP_INVOICE_LINE_INV1_INVOICE
  - 记录元数据：数据来源、加载时间

- **发票行-物料Link**：`invoice_line_inv1_item_oitm`
  - 引用发票行Hub的哈希键
  - 引用物料Hub的哈希键
  - 生成链接哈希键：LK_RSAP_INVOICE_LINE_INV1_ITEM_OITM
  - 记录元数据：数据来源、加载时间

#### 2.3 Satellite表加载

- **发票行原始数据Satellite**：`invoice_line_inv1_j1_l10`
  - 引用发票行Hub的哈希键
  - 存储完整的JSON数据
  - 生成差异哈希：RH_RSAP_INVOICE_LINE_INV1_J1_L10_SAT
  - 记录元数据：数据来源、加载时间、有效期

- **发票头原始数据Satellite**：`invoice_oinv_j1_l10`
  - 引用发票头Hub的哈希键
  - 存储完整的JSON数据
  - 生成差异哈希：RH_RSAP_INVOICE_OINV_J1_L10_SAT
  - 记录元数据：数据来源、加载时间、有效期

### 3. 数据处理

原始JSON数据处理为结构化数据的过程：

#### 3.1 JSON解析

- 从JSON数据中提取特定字段
- 转换数据类型（如字符串转数字）
- 标准化日期和时间格式

#### 3.2 L20层Satellite加载

- **发票行处理数据Satellite**：`invoice_line_inv1_p1_l20`
  - 引用发票行Hub的哈希键
  - 存储结构化字段：行总价、货币、数量等
  - 生成差异哈希：RH_RSAP_INVOICE_LINE_INV1_P1_L20_SAT
  - 记录元数据：数据来源、加载时间、有效期

- **发票头处理数据Satellite**：`invoice_oinv_p1_l20`
  - 引用发票头Hub的哈希键
  - 存储结构化字段：文档总价、折扣总额、货币等
  - 生成差异哈希：RH_RSAP_INVOICE_OINV_P1_L20_SAT
  - 记录元数据：数据来源、加载时间、有效期

### 4. 业务转换

在Business Vault中应用业务规则的过程：

#### 4.1 发票行调整

- 计算每行的折扣分摊
- 调整行总价（考虑整体折扣）
- 存储在Business Vault的Satellite表中：`BSAP_INVOICE_LINE_INV1_KORRIGIERT_V1_B10_SAT`

#### 4.2 汇率转换

- 将不同货币的金额转换为标准货币（如欧元）
- 使用适当的汇率（基于发票日期）
- 存储在Business Vault的Satellite表中

#### 4.3 业务分类

- 根据业务规则对交易进行分类
- 例如：区分内部和外部销售
- 存储在Business Vault的Satellite表中

### 5. 数据集成

在Data Mart中整合数据的过程：

#### 5.1 事实表创建

- 创建销售额事实表：`F_UMSATZ`
- 包含度量值：销售额、成本、利润等
- 包含维度引用：日期、产品、客户等

#### 5.2 维度表创建

- 创建日期维度：`D_DATUM`
- 创建产品维度：`D_ARTIKEL`
- 创建客户维度：`D_GESCHAEFTSPARTNER`

#### 5.3 报表视图创建

- 创建业务友好的视图
- 隐藏技术细节，突出业务概念
- 支持各种分析和报告需求

## 业务场景示例

为了更好地理解数据流程，让我们通过几个具体的业务场景来说明：

### 场景1：处理新的SAP发票

#### 步骤1：数据提取和加载

假设SAP系统中创建了一个新发票：
- 发票号：1001，日期：2024-05-20
- 客户：CUSTOMER_A，公司：DEISS_DE
- 行1：产品P001，数量10，单价100欧元，总价1000欧元
- 行2：产品P002，数量5，单价200欧元，总价1000欧元
- 整体折扣：200欧元（10%）

数据被提取并加载到Raw Vault：

1. **Hub表**：
   - `invoice_oinv`：添加发票头记录
   - `invoice_line_inv1`：添加两条发票行记录
   - `item_oitm`：确保产品P001和P002存在
   - `business_partner_ocrd`：确保客户CUSTOMER_A存在

2. **Link表**：
   - `invoice_line_inv1_invoice`：连接发票行与发票头
   - `invoice_line_inv1_item_oitm`：连接发票行与产品
   - `invoice_oinv_business_partner_ocrd`：连接发票头与客户

3. **Satellite表**：
   - `invoice_oinv_j1_l10`：存储发票头的JSON数据
   - `invoice_line_inv1_j1_l10`：存储发票行的JSON数据

#### 步骤2：数据处理

原始JSON数据被处理为结构化数据：

1. **发票头处理**：
   - 从JSON中提取字段：文档总价、折扣总额、货币等
   - 存储在`invoice_oinv_p1_l20`

2. **发票行处理**：
   - 从JSON中提取字段：行总价、数量、单价等
   - 存储在`invoice_line_inv1_p1_l20`

#### 步骤3：业务转换

应用业务规则进行转换：

1. **折扣分摊**：
   - 计算每行的折扣分摊：行1分摊100欧元，行2分摊100欧元
   - 计算调整后的行总价：行1为900欧元，行2为900欧元
   - 存储在Business Vault的Satellite表中

2. **汇率转换**：
   - 如果发票使用非欧元货币，将金额转换为欧元
   - 使用发票日期的汇率

#### 步骤4：数据集成

整合数据供业务分析使用：

1. **更新事实表**：
   - 在`F_UMSATZ`中添加两条记录，对应两个发票行
   - 包含维度引用：日期、产品、客户等
   - 包含度量值：销售额、成本、利润等

2. **更新报表视图**：
   - 更新销售报表视图，反映新的销售数据
   - 业务用户可以查看最新的销售情况

### 场景2：分析特定客户的销售趋势

业务用户想要分析CUSTOMER_A的销售趋势：

1. **数据来源**：
   - 发票数据来自`invoice_oinv`和`invoice_line_inv1`
   - 客户数据来自`business_partner_ocrd`
   - 产品数据来自`item_oitm`
   - 业务转换（如折扣分摊）来自Business Vault

2. **数据集成**：
   - Data Mart中的`F_UMSATZ`事实表整合了所有必要的数据
   - `D_GESCHAEFTSPARTNER`维度表提供客户详细信息
   - `D_DATUM`维度表支持时间分析

3. **业务分析**：
   - 按月查看CUSTOMER_A的销售额趋势
   - 分析CUSTOMER_A购买的产品组合
   - 比较CUSTOMER_A的实际销售与计划目标

### 场景3：产品盈利能力分析

业务用户想要分析产品的盈利能力：

1. **数据来源**：
   - 销售数据来自发票行（`invoice_line_inv1`）
   - 产品数据来自`item_oitm`
   - 成本数据可能来自其他源系统

2. **业务转换**：
   - 计算每个产品的毛利率
   - 考虑折扣、退货等因素
   - 存储在Business Vault中

3. **数据集成**：
   - Data Mart中的事实表和维度表整合了所有必要的数据
   - 创建产品盈利能力报表视图

4. **业务分析**：
   - 识别高利润和低利润产品
   - 分析产品盈利能力的趋势
   - 支持产品组合优化决策

## 总结

Data Vault模型通过结构化的方式组织数据，支持从原始数据到业务分析的完整流程。虽然模型看起来复杂，但它提供了极大的灵活性和可扩展性，能够适应业务变化并支持各种分析需求。

通过理解数据在模型中的流动方式，业务用户可以更好地理解数据仓库如何支持业务决策，从而更有效地利用数据进行分析和报告。
