#  =====================================================================
#  Part of the Cimt Data Vault Python Framework
#
#  Copyright 2023 cimt ag
#
#  Licensed under the Apache License, Version 2.0 (the "License");
#  you may not use this file except in compliance with the License.
#  You may obtain a copy of the License at
#
#      http://www.apache.org/licenses/LICENSE-2.0
#
#  Unless required by applicable law or agreed to in writing, software
#  distributed under the License is distributed on an "AS IS" BASIS,
#  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
#  See the License for the specific language governing permissions and
# limitations under the License.
# =====================================================================

from configparser import ConfigParser
from pathlib import Path,PurePath
import os
from os.path import join, dirname
import sys
import datetime
from enum import Enum
import json
from lib.connection_azrkeyvault import connection_keyvault

from  azure.core.exceptions import ResourceNotFoundError


def error_print(*args, **kwargs):
    """Print a message to stderr"""
    print(*args, file=sys.stderr, **kwargs)


def configuration_load_ini(filename=None, section=None, mandatory_elements=None):
    """Reads the section in the ini file, that has to be located in the
        config directory and returns a dictionary with all found key values"""
    # todo move responsibility for full path to caller

    if filename is None:
        raise Exception("System not set")
    if object is None:
        raise Exception("Object not set")
    current_directory=os.getcwd().replace('\\','/')  # convert windows to unix slash
    end_of_root_directory_string=current_directory.find('/processes')
    if end_of_root_directory_string<0:
        end_of_root_directory_string = current_directory.find('/lib')
    if end_of_root_directory_string<0:
        raise  Exception(f"Could not determine script root directory. Did not find '/processes' or '/lib'")

    if '.ini' not in filename:
        filename += '.ini'

    file_path = Path(current_directory[:end_of_root_directory_string]+'/config').joinpath(f'{filename.lower()}')
    # print(file_path)  # for debug only
    if  os.path.isdir(file_path):
        raise Exception(f"{file_path} is not a file, but was declared to be an ini file")

    if not os.path.exists(file_path):
        raise Exception(f'could not find configuration file: {file_path}')
    parser = ConfigParser()
    parser.read(file_path)

    # get section, default to postgresql
    key_value_list = {}
    if parser.has_section(section):
        params = parser.items(section)
        for param in params:
            key_value_list[param[0]] = param[1]
            # print(param[0], param[1])
    else:
        raise Exception('Object "{0}" not found in ini file "{1}" '.format(section, filename))
    return key_value_list



def configuration_read(system, object, mandatory_elements=None):
    configurations = configuration_load_azrkeyvault(system.replace("_", "-").upper(), object) #first try keyvault
    if configurations is None:
        configurations = configuration_load_env(system, object) #second try envs
        if len(configurations) == 0:
            configurations = configuration_load_ini(system, object, mandatory_elements) #third try ini
    if mandatory_elements is not None:
        for keyword in mandatory_elements:
            if keyword not in configurations:
                raise Exception('Mandtatory parameter "{0}" not found for "{1} system."'.format(keyword, system))
    return configurations


def configuration_load_env(system, object):
    import re
    pattern = re.compile(rf'PE\.{system.upper()}\.{object.upper()}')
    matching_env_vars = {key: value for key, value in os.environ.items() if pattern.search(key)}
    key_value_list = {}
    for key, value in matching_env_vars.items():
        new_key = key.split('.')[-1]
        key_value_list[new_key.lower()] = value
    return key_value_list

def configuration_load_azrkeyvault(system, object):
    # retrieves configuration for any system except KeyVault, as configuration for KeyVault cannot be retrieved from itself.
    # config for KeyVault ist keyvault url
    if system != 'DWH-AZRKEYVAULT': # uppercase and hyphen is a key vault notation
        try:
            secret = connection_keyvault(system)
            return json.loads(secret).get(object)
        except ResourceNotFoundError as e:
            error_print(e)
        except Exception:
            raise



# #### functions for standardized process specific file directories
class ProcessDirectoryType(Enum):
    unprocessed = 'unprocessed'
    archived = 'archived'
    rejected = 'rejected'



def prepare_file_standard_directories(process_name):
    """Adds all data directories in the file system for the file specific operations
        returns the directory pathes as dict"""

    directory_dict = determine_file_standard_directories(process_name)

    print("Adding standard file directories")

    for path_type in directory_dict:
        current_path = Path(directory_dict[path_type])
        if not current_path.exists():
            current_path.mkdir(parents=True)
            print(path_type.value,':',current_path.as_posix()," <-created")
        else:
            print(path_type.value,':',current_path.as_posix())


    return directory_dict


def determine_file_standard_directories(process_name):
    """Returns a dictionary of pathes, based on the general configuration and the given element name
        Dictionary keys are defined in Class ConfigurationDirectoryType"""

    params = configuration_load_ini('dvf.ini', 'file_processing_location',['file_processing_base_directory'])
    base_directory = Path(params['file_processing_base_directory'])

    directory_dict = {
        ProcessDirectoryType.unprocessed: os.path.join(base_directory, process_name, 'unprocessed'),
        ProcessDirectoryType.archived: os.path.join(base_directory, process_name, 'archived',
                                                    datetime.date.today().isoformat()),
        ProcessDirectoryType.rejected: os.path.join(base_directory, process_name, 'rejected',
                                                    datetime.date.today().isoformat())}
    return directory_dict


if __name__ == '__main__':
    '''print('small test of', __name__)
    my_list = configuration_load_ini('dvf.ini', 'ddl_deployment')
    for key in my_list:
        print(key, '->', my_list[key])

    prepare_file_standard_directories('configuration_main_demo')

    print("***************************")'''
    ce = configuration_read('dvf', 'ddl_deployment')
    print(ce)



