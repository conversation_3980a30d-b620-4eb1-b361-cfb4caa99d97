/* This script implements necessary Grants to schemas and their default setting for new objects
 * for all users and roles of the data vault.
 * Must be executed as "DV_D_OWNER_DATABASE"
 * It will be needed...
     * after adding a new role or technical user
     * after adding a new schema, must be executed before adding objects to the schema
     * to repair access problems
 * In case of existing objects in schemas this will only provide the initial access to the schema.
 * Access to existing objects will be granted with the "grant_existing_objects.sql" script
 * Keep in mind, that the final statement generates the necessary grant statements, which then have
 * to be executed.
 */
--
-- process_role
--
-- Usage to rvlt and bvlt schema
SELECT 'GRANT USAGE ON SCHEMA '
 	|| schema_name
 	|| ' TO DV_D_ACCESS_WRITE;'
 FROM information_schema.schemata
where  lower(schema_name) like '%rvlt%' OR lower(schema_name) like '%bvlt%'
union all 
-- all upcoming rvtl and bvlt object
SELECT
    CASE WHEN lower(schema_name) like 'stage%'
        THEN 'GRANT SELECT, INSERT, DELETE, UPDATE, TRUNCATE ON all TABLES IN SCHEMA '
        ELSE 'GRANT SELECT, INSERT, UPDATE ON all TABLES IN SCHEMA '   END || schema_name ||  ' TO DV_D_ACCESS_WRITE;' as sqlstatement
FROM information_schema.schemata
    WHERE lower(schema_name) like '%rvlt%' OR lower(schema_name) like '%bvlt%'
UNION ALL

-- read role
SELECT 'GRANT USAGE ON SCHEMA '
 	|| schema_name
 	|| ' TO DV_D_ACCESS_VAULT_READ;'
 FROM information_schema.schemata
where lower(schema_name) like '%rvlt%' OR lower(schema_name) like '%bvlt%'
union all
-- all upcoming rvtl and bvlt object
SELECT 'GRANT SELECT ON all TABLES IN SCHEMA ' || schema_name ||  ' TO DV_D_ACCESS_VAULT_READ;' as sqlstatement
FROM information_schema.schemata
    WHERE lower(schema_name) like '%rvlt%' OR lower(schema_name) like '%bvlt%'