"""
RIN2 ETL主程序
处理SAP贷项通知单行项目明细数据的ETL流程

作者: Data Vault ETL Framework
创建日期: 2024
描述: 从Azure Blob Storage读取RIN2 JSON文件，加载到Data Vault模型
"""

import sys
import os

# 添加lib路径到Python路径
sys.path.append(os.path.join(os.path.dirname(__file__), '..', '..', 'lib'))

from cimtjobinstance import CimtJobInstance, cimtjobinstance_job
from connection_azrblob import connection_azrblob
from connection_snf import connection_snf_for_dwh_connection_type, Dvf_dwh_connection_type
from blobstorage_utils import get_source_files_from_blob_storage, move_processed_file_to_processed_container
from dvf_ddl_deploymentmanager_snf import DvfDdlDeploymentManagerSnf
from load_vault_1 import load_vault_1


def deploy_datamodel(job_instance):
    """
    部署RIN2相关的Data Vault模型
    
    Args:
        job_instance: 作业实例对象
    """
    deployment_manager = DvfDdlDeploymentManagerSnf(
        connection_snf_for_dwh_connection_type(Dvf_dwh_connection_type.raw_vault),
        job_instance
    )
    
    # 部署Hub表（复用RIN1的Hub表）
    deployment_manager.deploy_table("rvlt_sap", "rsap_credit_memo_line_rin1_hub")
    
    # 部署依赖Link表
    deployment_manager.deploy_table("rvlt_sap", "rsap_credit_memo_line_rin1_rin2_dlnk")
    
    # 部署Satellite表
    deployment_manager.deploy_table("rvlt_sap", "rsap_credit_memo_line_rin1_rin2_j1_l10_sat")
    
    # 部署Stage表
    deployment_manager.deploy_table("stage_rvlt", "rsap_rin2_j1_stage")


@cimtjobinstance_job
def main(**kwargs):
    """
    RIN2 ETL主函数
    
    处理流程：
    1. 初始化作业实例
    2. 部署数据模型
    3. 从Blob Storage获取源文件
    4. 逐个处理文件
    5. 移动已处理文件到processed容器
    
    Args:
        **kwargs: 作业参数，包含instance_job_name等
    """
    # ### FRAMEWORK PHASE: 设置主模块的作业实例
    my_job_instance = CimtJobInstance(kwargs['instance_job_name'])
    my_job_instance.start_instance()
    
    # 配置参数
    sap_source_object = "RIN2"  # SAP源对象标识
    container_name = "rawdata"  # Blob Storage容器名称
    
    # 建立Blob Storage连接
    blob_service_client = connection_azrblob('blob_storage')
    
    try:
        # ### FRAMEWORK PHASE: 执行处理逻辑
        
        # 1. 部署数据模型
        my_job_instance.log_info("开始部署RIN2数据模型...")
        deploy_datamodel(my_job_instance)
        my_job_instance.log_info("数据模型部署完成")
        
        # 2. 获取源文件列表
        my_job_instance.log_info(f"从容器 {container_name} 获取 {sap_source_object} 文件...")
        source_files = get_source_files_from_blob_storage(
            blob_service_client, 
            sap_source_object, 
            container_name
        )
        
        if not source_files:
            my_job_instance.log_warning(f"未找到 {sap_source_object} 文件")
            my_job_instance.end_instance()
            return
        
        # 3. 按文件名排序处理
        sorted_source_file_names = sorted([file.name for file in source_files])
        my_job_instance.log_info(f"找到 {len(sorted_source_file_names)} 个文件待处理")
        
        # 4. 逐个处理文件
        for file_name in sorted_source_file_names:
            my_job_instance.log_info(f"开始处理文件: {file_name}")
            
            # 调用数据加载模块
            load_vault_1(
                parent_job_instance=my_job_instance, 
                file_name=file_name
            )
            
            # 移动已处理文件到processed容器
            move_processed_file_to_processed_container(
                blob_service_client, 
                file_name
            )
            
            my_job_instance.log_info(f"文件处理完成: {file_name}")
        
        # 关闭Blob Storage连接
        blob_service_client.close()
        my_job_instance.log_info("所有文件处理完成")
        
    except Exception as e:
        # ### FRAMEWORK PHASE: 异常处理
        error_message = f"RIN2 ETL处理失败: {str(e)}"
        my_job_instance.log_error(error_message)
        my_job_instance.end_instance_with_error(1, error_message)
        raise e
    
    # ### FRAMEWORK PHASE: 正常结束
    my_job_instance.end_instance()


if __name__ == '__main__':
    """
    本地执行入口
    """
    print("启动RIN2 ETL处理...")
    main()
    print("RIN2 ETL处理完成")
