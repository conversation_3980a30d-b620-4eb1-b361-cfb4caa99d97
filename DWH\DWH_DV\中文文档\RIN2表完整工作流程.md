# RIN2表完整工作流程

## 概述

本文档描述了SAP RIN2（贷项通知单行项目明细）表在Data Vault 2.0架构下的完整工作流程，从DVPD配置到ETL实现的全过程。

## 工作流程图

```
[SAP系统] → [Lobster导出] → [Azure Blob Storage] → [ETL处理] → [Data Vault表]
    ↓              ↓                ↓                ↓              ↓
  RIN2表      JSON文件         rawdata容器        Python ETL    rvlt_sap模式
```

## 详细步骤

### 1. 数据源准备
- **源系统**: SAP Business One
- **源表**: RIN2（贷项通知单行项目明细）
- **导出工具**: Lobster ETL工具
- **导出格式**: JSON文件
- **文件命名**: `RIN2_YYYYMMDD_HHMMSS.json`

### 2. DVPD配置
- **配置文件**: `resources/dvpd/rsap_rin2_j1.dvpd.json`
- **模型类型**: 依赖Link模式
- **关键配置**:
  - 复用RIN1的Hub表
  - 创建RIN1-RIN2依赖Link表
  - 创建对应的Satellite表

### 3. DDL生成和部署
```bash
# 生成DDL脚本
python -m dvpdc.dvpdc_cli resources/dvpd/rsap_rin2_j1.dvpd.json --ddl-snf -o resources/ddl_snf/rvlt_sap/

# 部署到数据库
python processes/jobless_deployment/__main__.py
```

### 4. ETL执行
```bash
# 运行RIN2 ETL
python processes/rsap_rin2_j1/__main__.py
```

### 5. 数据流转过程

#### 阶段1: 文件读取
- 从Azure Blob Storage的rawdata容器读取RIN2 JSON文件
- 解析JSON数据结构
- 验证数据完整性

#### 阶段2: 暂存表加载
- 清空stage_rvlt.rsap_rin2_j1_stage表
- 计算哈希键：
  - Hub哈希键: SHA1(COMPANY||DOCENTRY||LINENUM)
  - Link哈希键: SHA1(HK_RIN1||GROUPNUM)
  - Satellite差异哈希: SHA1(JSON_TEXT)
- 批量插入暂存表

#### 阶段3: Data Vault加载
1. **Hub表加载** (rsap_credit_memo_line_rin1_hub)
   - 检查是否存在相同哈希键的记录
   - 插入新的业务实体记录

2. **依赖Link表加载** (rsap_credit_memo_line_rin1_rin2_dlnk)
   - 建立RIN1与RIN2的依赖关系
   - 包含GROUPNUM业务键

3. **Satellite表加载** (rsap_credit_memo_line_rin1_rin2_j1_l10_sat)
   - 检查差异哈希是否变化
   - 关闭旧版本记录（设置MD_VALID_BEFORE）
   - 插入新版本记录

#### 阶段4: 文件归档
- 将已处理文件移动到processed容器
- 记录处理日志和统计信息

## 监控和验证

### 数据质量检查
```sql
-- 检查暂存表数据
SELECT COUNT(*) as total_records,
       COUNT(DISTINCT HK_RSAP_CREDIT_MEMO_LINE_RIN1) as unique_hubs,
       COUNT(DISTINCT LK_RSAP_CREDIT_MEMO_LINE_RIN1_RIN2) as unique_links
FROM stage_rvlt.rsap_rin2_j1_stage;

-- 检查最终数据
SELECT COUNT(*) as current_records
FROM rvlt_sap.rsap_credit_memo_line_rin1_rin2_j1_l10_sat
WHERE MD_VALID_BEFORE = '9999-12-31 23:59:59';
```

### 作业监控
- 作业实例状态跟踪
- 处理时间监控
- 错误率统计
- 数据量变化趋势

## 业务价值

### 数据完整性
- 保持RIN1-RIN2的完整关联关系
- 支持贷项通知单的完整业务流程
- 提供分组和分配信息的历史追踪

### 查询支持
- 支持当前状态查询
- 支持历史变更追踪
- 支持复杂的业务分析需求

### 扩展性
- 易于添加新的业务规则
- 支持与其他SAP表的关联
- 为业务层提供标准化数据接口

## 故障处理

### 常见问题
1. **文件格式错误**: 检查JSON结构和字段完整性
2. **哈希键冲突**: 验证业务键的唯一性
3. **性能问题**: 优化批处理大小和索引策略
4. **连接问题**: 检查Azure和Snowflake连接配置

### 恢复策略
1. 重新处理失败的文件
2. 数据一致性验证和修复
3. 增量重新加载
4. 完整重新加载（极端情况）

## 总结

RIN2的完整工作流程体现了Data Vault 2.0架构的核心优势：
- **灵活性**: 支持复杂的业务关系建模
- **可追溯性**: 完整的历史记录和审计轨迹
- **标准化**: 统一的ETL模式和数据结构
- **可扩展性**: 易于适应业务需求变化

通过这个完整的工作流程，RIN2数据能够有效地支持企业的贷项通知单管理和分析需求。