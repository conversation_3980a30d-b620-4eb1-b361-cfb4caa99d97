"""
Azure Blob Storage 工具模块

本模块提供了一系列用于操作Azure Blob Storage的工具函数，主要用于数据仓库ETL流程中的文件操作。
主要功能包括：
- 从Blob存储中获取源文件
- 在容器之间移动文件（如将处理完的文件移至processed容器）
- 下载不同格式的文件（JSON、Excel等）

与其他模块的关系：
- 依赖于lib.connection_azrblob模块建立Azure Blob Storage连接
- 被processes目录下的各个处理模块使用，如rsap_oocr_j1等SAP数据处理模块
- 与json_utils.py配合使用处理JSON格式的数据

典型使用场景：
1. ETL流程中获取源数据文件
2. 处理完成后将文件移至processed容器
3. 下载并解析不同格式的源文件

作者：SUND DWH团队
"""

import json
import io

from lib.connection_azrblob import connection_azrblob

def get_source_files_from_blob_storage(blob_connection, sap_source_object, container_name="rawdata"):
    """
    从Azure Blob Storage中获取指定SAP对象的源文件列表
    
    该函数用于ETL流程中获取需要处理的源文件，通常在processes目录下的各个处理模块的__main__.py中被调用，
    如rsap_oocr_j1等SAP数据处理模块。
    
    参数:
        blob_connection: Azure Blob Storage连接对象，通过connection_azrblob模块获取
        sap_source_object: SAP源对象名称，如'OOCR'、'OINV'等
        container_name: Blob容器名称，默认为'rawdata'
        
    返回:
        包含所有匹配的Blob对象的列表
    """
    container_client = blob_connection.get_container_client(container_name)
    blob_list = list(container_client.list_blobs(name_starts_with=sap_source_object))
    if len(blob_list) == 0:
        blob_list_source = container_client.list_blobs()
        for blob in blob_list_source:
            if sap_source_object in blob.name:
                blob_list.append(blob)
    return blob_list

def move_blob(blob_connection, blob_name, source_container_name, destination_container_name):
    """
    在Azure Blob Storage的不同容器之间移动文件
    
    该函数是一个内部工具函数，主要被move_processed_file_to_processed_container函数调用，
    用于在处理完成后将文件从源容器移动到目标容器。
    
    参数:
        blob_connection: Azure Blob Storage连接对象
        blob_name: 要移动的Blob文件名
        source_container_name: 源容器名称
        destination_container_name: 目标容器名称
        
    返回:
        成功返回True，失败抛出异常
    """
    try:
        source_blob_client = blob_connection.get_blob_client(source_container_name, blob_name)
        destination_blob_client = blob_connection.get_blob_client(destination_container_name, blob_name)
        destination_blob_client.start_copy_from_url(source_blob_client.url)
        source_blob_client.delete_blob()
        return True
    except Exception as error:
        print(error)
        raise

def move_processed_file_to_processed_container(blob_connection, blob_name, source_container_name='rawdata', destination_container_name='processed'):
    """
    将处理完成的文件移动到processed容器
    
    该函数在ETL流程中被广泛使用，通常在processes目录下的各个处理模块的__main__.py中被调用，
    用于在数据处理完成后将源文件移动到processed容器，避免重复处理。
    
    参数:
        blob_connection: Azure Blob Storage连接对象
        blob_name: 要移动的Blob文件名
        source_container_name: 源容器名称，默认为'rawdata'
        destination_container_name: 目标容器名称，默认为'processed'
        
    返回:
        无直接返回值，内部调用move_blob函数
    """
    move_blob(blob_connection, blob_name, source_container_name, destination_container_name)

def fetch_source_file_from_blob_container(blob_client):
    """
    从Blob容器中下载文件
    
    这是一个基础函数，被其他特定格式的下载函数调用，如fetch_json_source_file_from_blob_container
    和fetch_excel_source_file_from_blob_container。在ETL流程中用于获取源数据。
    
    参数:
        blob_client: Azure Blob客户端对象
        
    返回:
        下载的Blob数据流对象，失败则打印错误信息
    """
    try:
        return blob_client.download_blob()
    except Exception as e:
        print(f"An error occurred: {e}")

def fetch_json_source_file_from_blob_container(blob_client):
    """
    从Blob容器中下载并解析JSON格式的文件
    
    该函数在处理SAP数据的ETL流程中被广泛使用，如在load_vault_1.py中被调用，
    用于获取JSON格式的源数据并解析为Python对象。与json_utils.py模块配合使用。
    
    参数:
        blob_client: Azure Blob客户端对象
        
    返回:
        解析后的JSON数据（Python对象），失败则打印错误信息
    """
    try:
        json_data = json.load(fetch_source_file_from_blob_container(blob_client))
        return json_data
    except Exception as e:
        print(f"An error occurred: {e}")

def fetch_excel_source_file_from_blob_container(blob_client):
    """
    从Blob容器中下载Excel格式的文件
    
    该函数主要用于处理Excel格式的源数据，如计划数据(Planzahlen)等。
    通常与excel_utils.py模块配合使用，在处理手工上传的Excel数据时使用。
    
    参数:
        blob_client: Azure Blob客户端对象
        
    返回:
        Excel数据的BytesIO对象，可直接被pandas或其他Excel处理库使用
        失败则打印错误信息
    """
    try:
        excel_data = io.BytesIO()
        fetch_source_file_from_blob_container(blob_client).readinto(excel_data)
        excel_data.seek(0)
        return excel_data
    except Exception as e:
        print(f"An error occurred: {e}")


if __name__ == '__main__':
    #test
    blob_service_client = connection_azrblob('blob_storage')
    files = get_source_files_from_blob_storage(blob_service_client, 'OOCR', 'processed')



