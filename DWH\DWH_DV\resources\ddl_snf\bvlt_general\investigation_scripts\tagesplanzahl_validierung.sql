/* YODA Jahresplanzahl */
select g_h.company 
	,jp_dl.jahr
	,jp_s.kundengruppe
	,jp_s.verkaufsgebiet
	,jp_s.verkaeufernummer 
	,jp_s.planzahl
from RVLT_MANUAL_DATA.RMUD_RGNR_GESELLSCHAFT_JAHRESPLANZAHL_DLNK jp_dl
join RVLT_MANUAL_DATA.RMUD_RGNR_GESELLSCHAFT_JAHRESPLANZAHL_P1_L20_MSAT jp_s
    on jp_s.lk_rmud_rgnr_gesellschaft_jahresplanzahl =jp_dl.lk_rmud_rgnr_gesellschaft_jahresplanzahl 
    and jp_s.md_valid_before = lib.dwh_far_future_date()
    and not jp_s.md_is_deleted
join RVLT_GENERAL.RGNR_GESELLSCHAFT_HUB g_h
	on g_h.hk_rgnr_gesellschaft = jp_dl.hk_rgnr_gesellschaft 
	and g_h.md_record_source <>'SYSTEM'
order by 1,2,3,4,5;	

/* YODA Monatssplanzahl */
select g_h.company 
	,mp_dl.jahr
	,mp_dl.monat 
	,mp_s.kundengruppe
	,mp_s.verkaufsgebiet
	,mp_s.verkaeufernummer 
	,mp_s.planzahl
from RVLT_MANUAL_DATA.RMUD_RGNR_GESELLSCHAFT_MONATSPLANZAHL_DLNK mp_dl
join RVLT_MANUAL_DATA.RMUD_RGNR_GESELLSCHAFT_MONATSPLANZAHL_P1_L20_MSAT mp_s
    on mp_s.lk_rmud_rgnr_gesellschaft_monatsplanzahl =mp_dl.lk_rmud_rgnr_gesellschaft_monatsplanzahl 
    and mp_s.md_valid_before = lib.dwh_far_future_date()
    and not mp_s.md_is_deleted
join RVLT_GENERAL.RGNR_GESELLSCHAFT_HUB g_h
	on g_h.hk_rgnr_gesellschaft = mp_dl.hk_rgnr_gesellschaft 
	and g_h.md_record_source <>'SYSTEM'
order by 1,2,3,4,5;	
    
/* Rekombinierter Business Vault Jährlich*/
select g_h.company 
	, year(gk_dl.tagesdatum)
	,tp_ms.kundengruppe
	,tp_ms.verkaufsgebiet
	,tp_ms.verkaeufernummer 
	,sum(tp_ms.geplanter_umsatz)
from RVLT_GENERAL.RGNR_GESELLSCHAFT_HUB g_h
   join RVLT_GENERAL.RGNR_GESELLSCHAFT_KALENDER_DLNK gk_dl 
   on gk_dl.hk_rgnr_gesellschaft =g_h.hk_rgnr_gesellschaft 
   join BVLT_GENERAL.BGNR_GESELLSCHAFT_TAGESPLANZAHL_V1_B10_MSAT tp_ms
   on tp_ms.lk_rgnr_gesellschaft_kalender =gk_dl.lk_rgnr_gesellschaft_kalender 
   and tp_ms.md_valid_before =lib.dwh_far_future_date()
   and not tp_ms.md_is_deleted 
where g_h.company<>'FIPP'      
group by 1,2,3,4,5
order by 1,2,3,4,5;

/* Rekombinierter Business Vault monatlich*/
select g_h.company 
	, year(gk_dl.tagesdatum)
	, month(gk_dl.tagesdatum)
	,tp_ms.kundengruppe
	,tp_ms.verkaufsgebiet
	,tp_ms.verkaeufernummer 
	,sum(tp_ms.geplanter_umsatz)
from RVLT_GENERAL.RGNR_GESELLSCHAFT_HUB g_h
   join RVLT_GENERAL.RGNR_GESELLSCHAFT_KALENDER_DLNK gk_dl 
   on gk_dl.hk_rgnr_gesellschaft =g_h.hk_rgnr_gesellschaft 
   join BVLT_GENERAL.BGNR_GESELLSCHAFT_TAGESPLANZAHL_V1_B10_MSAT tp_ms
   on tp_ms.lk_rgnr_gesellschaft_kalender =gk_dl.lk_rgnr_gesellschaft_kalender 
   and tp_ms.md_valid_before =lib.dwh_far_future_date()
   and not tp_ms.md_is_deleted 
where g_h.company='FIPP'   
group by 1,2,3,4,5,6
order by 1,2,3,4,5,6

/* Business Vault Tagesplanzahlen*/
select gk_dl.tagesdatum
	,g_h.company 
	,tp_ms.kundengruppe
	,tp_ms.verkaufsgebiet
	,tp_ms.verkaeufernummer 
	,tp_ms.geplanter_umsatz
from RVLT_GENERAL.RGNR_GESELLSCHAFT_HUB g_h
   join RVLT_GENERAL.RGNR_GESELLSCHAFT_KALENDER_DLNK gk_dl 
   on gk_dl.hk_rgnr_gesellschaft =g_h.hk_rgnr_gesellschaft 
   join BVLT_GENERAL.BGNR_GESELLSCHAFT_TAGESPLANZAHL_V1_B10_MSAT tp_ms
   on tp_ms.lk_rgnr_gesellschaft_kalender =gk_dl.lk_rgnr_gesellschaft_kalender 
   and tp_ms.md_valid_before =lib.dwh_far_future_date()
   and not tp_ms.md_is_deleted 
