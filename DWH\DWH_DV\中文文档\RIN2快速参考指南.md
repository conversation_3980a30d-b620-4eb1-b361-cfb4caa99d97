# RIN2快速参考指南

## 概览

RIN2是SAP贷项通知单行项目明细表，采用Data Vault 2.0依赖Link模式建模。

## 核心表结构

### 1. Hub表（复用RIN1）
```sql
rsap_credit_memo_line_rin1_hub
├── HK_RSAP_CREDIT_MEMO_LINE_RIN1 (哈希键)
├── COMPANY (公司代码)
├── DOCENTRY (文档编号)
└── LINENUM (行号)
```

### 2. 依赖Link表
```sql
rsap_credit_memo_line_rin1_rin2_dlnk
├── LK_RSAP_CREDIT_MEMO_LINE_RIN1_RIN2 (链接哈希键)
├── HK_RSAP_CREDIT_MEMO_LINE_RIN1 (父Hub哈希键)
└── GROUPNUM (分组编号)
```

### 3. Satellite表
```sql
rsap_credit_memo_line_rin1_rin2_j1_l10_sat
├── LK_RSAP_CREDIT_MEMO_LINE_RIN1_RIN2 (父Link哈希键)
├── RH_RSAP_CREDIT_MEMO_LINE_RIN1_RIN2_J1_L10_SAT (差异哈希)
├── JSON_TEXT (完整数据)
└── MD_VALID_BEFORE (有效期)
```

## DVPD配置要点

### 字段映射
```json
{
  "fields": [
    {"field_name": "company", "targets": [{"table_name": "RSAP_CREDIT_MEMO_LINE_RIN1_HUB"}]},
    {"field_name": "docentry", "targets": [{"table_name": "RSAP_CREDIT_MEMO_LINE_RIN1_HUB"}]},
    {"field_name": "linenum", "targets": [{"table_name": "RSAP_CREDIT_MEMO_LINE_RIN1_HUB"}]},
    {"field_name": "groupnum", "targets": [{"table_name": "RSAP_CREDIT_MEMO_LINE_RIN1_RIN2_DLNK"}]},
    {"field_name": "json_text", "targets": [{"table_name": "RSAP_CREDIT_MEMO_LINE_RIN1_RIN2_J1_L10_SAT"}]}
  ]
}
```

### 模型定义
```json
{
  "data_vault_model": [{
    "schema_name": "rvlt_sap",
    "tables": [
      {"table_name": "RSAP_CREDIT_MEMO_LINE_RIN1_HUB", "table_stereotype": "hub"},
      {"table_name": "RSAP_CREDIT_MEMO_LINE_RIN1_RIN2_DLNK", "table_stereotype": "lnk"},
      {"table_name": "RSAP_CREDIT_MEMO_LINE_RIN1_RIN2_J1_L10_SAT", "table_stereotype": "sat"}
    ]
  }]
}
```

## ETL流程

### 1. 数据提取
```python
# 从Blob Storage读取RIN2文件
blob_client = arzblob_connection.get_blob_client(container="rawdata", blob=file_name)
source_json = fetch_json_source_file_from_blob_container(blob_client)
```

### 2. 哈希键计算
```python
# Hub哈希键
hk_rin1 = sha1(concat([COMPANY, DOCENTRY, LINENUM])).hexdigest()[:28]

# Link哈希键
lk_rin1_rin2 = sha1(concat([hk_rin1, GROUPNUM])).hexdigest()[:28]

# Satellite差异哈希
rh_sat = sha1(concat([JSON_TEXT])).hexdigest()[:28]
```

### 3. 数据加载顺序
1. Stage表 ← JSON数据
2. Hub表 ← Stage表（如果不存在）
3. Link表 ← Stage表
4. Satellite表 ← Stage表（支持历史版本）

## 常用查询

### 当前状态查询
```sql
SELECT h.COMPANY, h.DOCENTRY, h.LINENUM, l.GROUPNUM, s.JSON_TEXT
FROM rvlt_sap.rsap_credit_memo_line_rin1_hub h
JOIN rvlt_sap.rsap_credit_memo_line_rin1_rin2_dlnk l
  ON h.HK_RSAP_CREDIT_MEMO_LINE_RIN1 = l.HK_RSAP_CREDIT_MEMO_LINE_RIN1
JOIN rvlt_sap.rsap_credit_memo_line_rin1_rin2_j1_l10_sat s
  ON l.LK_RSAP_CREDIT_MEMO_LINE_RIN1_RIN2 = s.LK_RSAP_CREDIT_MEMO_LINE_RIN1_RIN2
WHERE s.MD_VALID_BEFORE = '9999-12-31 23:59:59'
  AND s.MD_IS_DELETED = FALSE;
```

### 历史变更查询
```sql
SELECT s.*, s.MD_INSERTED_AT as change_time
FROM rvlt_sap.rsap_credit_memo_line_rin1_rin2_j1_l10_sat s
WHERE s.LK_RSAP_CREDIT_MEMO_LINE_RIN1_RIN2 = 'specific_link_key'
ORDER BY s.MD_INSERTED_AT DESC;
```

## 监控检查

### 数据质量检查
```sql
-- 检查重复记录
SELECT HK_RSAP_CREDIT_MEMO_LINE_RIN1, GROUPNUM, COUNT(*)
FROM stage_rvlt.rsap_rin2_j1_stage
GROUP BY HK_RSAP_CREDIT_MEMO_LINE_RIN1, GROUPNUM
HAVING COUNT(*) > 1;

-- 检查空值
SELECT COUNT(*) as null_company_count
FROM stage_rvlt.rsap_rin2_j1_stage
WHERE COMPANY IS NULL;
```

### 加载统计
```sql
-- 最近加载统计
SELECT MD_RUN_ID, COUNT(*) as records, MIN(MD_INSERTED_AT) as start_time
FROM rvlt_sap.rsap_credit_memo_line_rin1_rin2_j1_l10_sat
WHERE MD_INSERTED_AT >= CURRENT_DATE - 7
GROUP BY MD_RUN_ID
ORDER BY start_time DESC;
```

## 故障排除

### 常见问题
1. **文件读取失败**: 检查Blob Storage连接和权限
2. **哈希键冲突**: 验证业务键唯一性
3. **性能问题**: 检查索引和执行计划
4. **数据一致性**: 验证表间关系完整性

### 调试命令
```python
# 启用详细日志
import logging
logging.basicConfig(level=logging.DEBUG)

# 检查连接
blob_service_client = connection_azrblob('blob_storage')
dwh_connection = connection_snf_for_dwh_connection_type(Dvf_dwh_connection_type.raw_vault)
```

## 文件位置

### 配置文件
- DVPD: `resources/dvpd/rsap_rin2_j1.dvpd.json`
- DVPI: `resources/dvpi/rsap_rin2_j1.dvpi.json`

### DDL脚本
- Hub: `resources/ddl_snf/rvlt_sap/table_rsap_credit_memo_line_rin1_hub.sql`
- Link: `resources/ddl_snf/rvlt_sap/table_rsap_credit_memo_line_rin1_rin2_dlnk.sql`
- Satellite: `resources/ddl_snf/rvlt_sap/table_rsap_credit_memo_line_rin1_rin2_j1_l10_sat.sql`
- Stage: `resources/ddl_snf/rvlt_sap/stage/table_rsap_rin2_j1_stage.sql`

### 进程文件（待创建）
- 主程序: `processes/rsap_rin2_j1/__main__.py`
- 加载模块: `processes/rsap_rin2_j1/load_vault_1.py`

## 关键命令

### 部署模型
```bash
python processes/jobless_deployment/__main__.py
```

### 运行ETL
```bash
python processes/rsap_rin2_j1/__main__.py
```

### 生成DDL
```bash
python -m dvpdc.dvpdc_cli resources/dvpd/rsap_rin2_j1.dvpd.json --ddl-snf -o resources/ddl_snf/rvlt_sap/
```

## 业务关系

```
ORIN (贷项通知单头) 1:N RIN1 (贷项通知单行) 1:1 RIN2 (行项目明细)
```

RIN2通过GROUPNUM字段提供RIN1行项目的分组和分配信息，是贷项通知单处理的重要组成部分。
