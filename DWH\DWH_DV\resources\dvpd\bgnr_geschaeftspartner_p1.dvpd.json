{
	/* DVPD版本号，用于确保配置文件与处理框架的兼容性 */
	"dvpd_version": "0.6.2",
	/* 定义数据加载的暂存区属性，指定使用业务保险库的暂存区 */
	"stage_properties" : [{"stage_schema":"stage_bvlt"}],
	/* 定义数据管道名称，用于业务伙伴数据处理 */
	"pipeline_name": "bgnr_geschaeftspartner_p1",
	/* 定义数据来源标识 */
	"record_source_name_expression": "bgnr_geschaeftspartner_p1",
	/* 数据提取配置，这里仅用于DDL生成，不执行实际提取 */
	"data_extraction": {
		"fetch_module_name":"none - ddl generation only"
	},

	/* 字段映射部分：定义源系统字段如何映射到Data Vault模型中的表 */
	/* 字段映射部分：定义源系统字段如何映射到Data Vault模型中的表 */
	"fields": [
		      /* 业务伙伴ID，作为业务伙伴Hub表的主要业务键 */
		      {"field_name": "xd_geschaeftspartner_id",
												"field_type": "VARCHAR(255)",	"targets": [{"table_name": "bgnr_geschaeftspartner_hub"}]},
				/* 公司字段，存储在业务伙伴卫星表中 */
				{"field_name": "Gesellschaft",	"field_type": "VARCHAR(255)",	"targets": [{"table_name": "bgnr_geschaeftspartner_p1_b10_sat"}]},
				/* 名称字段，存储在业务伙伴卫星表中 */
				{"field_name": "Name",			"field_type": "VARCHAR(255)",	"targets": [{"table_name": "bgnr_geschaeftspartner_p1_b10_sat"}]},
				/* 客户组字段，存储在业务伙伴卫星表中 */
				{"field_name": "Kundengruppe ",	"field_type": "VARCHAR(255)",	"targets": [{"table_name": "bgnr_geschaeftspartner_p1_b10_sat"}]},
				/* 销售区域字段，存储在业务伙伴卫星表中 */
				{"field_name": "Verkaufsgebiet ","field_type": "VARCHAR(255)",	"targets": [{"table_name": "bgnr_geschaeftspartner_p1_b10_sat"}]},
				/* 销售员编号字段，存储在业务伙伴卫星表中 */
				{"field_name": "Verkaeufernummer ","field_type": "VARCHAR(255)",	"targets": [{"table_name": "bgnr_geschaeftspartner_p1_b10_sat"}]},
		 	  /* 公司字段，映射到SAP业务伙伴Hub表，用于建立与SAP系统的关联 */
		 	  {"field_name": "Company",			"field_type": "VARCHAR(255)",	"targets": [{"table_name": "rsap_business_partner_ocrd_hub"}]},
		 	  /* 卡号字段，映射到SAP业务伙伴Hub表，用于建立与SAP系统的关联 */
		 	  {"field_name": "Cardcode",		"field_type": "VARCHAR(255)",	"targets": [{"table_name": "rsap_business_partner_ocrd_hub"}]},

		 	  /* BillBee商店ID，映射到BillBee商店Hub表，用于建立与BillBee系统的关联 */
		 	  {"field_name": "BillBeeShopId",				"field_type": "NUMBER(36,0)",	"targets": [{"table_name": "RBBE_SHOP_HUB"}]}

			 ],
	/* Data Vault模型定义部分：定义目标数据库中的表结构和关系 */
	/* 该模型实现了业务伙伴与SAP和BillBee系统之间的集成关系，通过链接表建立跨系统的业务实体映射 */
	"data_vault_model": [
		/* SAP模式下的表定义 - 这些表存储在SAP专用的数据仓库模式中 */
		{"schema_name": "rvlt_sap", 
		 "tables": [
						/* SAP业务伙伴Hub表定义：仅作为结构元素，实际数据由SAP相关ETL流程加载 */
						/* 该表是rsap_ocrd_j1.dvpd.json中定义的主表，在此仅作为引用，用于建立与业务伙伴的关联 */
						{"table_name": "rsap_business_partner_ocrd_hub",
						"table_stereotype": "hub",
						"is_only_structural_element":"true",
						"hub_key_column_name": "HK_RSAP_BUSINESS_PARTNER_OCRD"}
			]
		},
		
		/* BillBee模式下的表定义 - 这些表存储在BillBee专用的数据仓库模式中 */
		{"schema_name": "rvlt_billbee", 
		"tables":[
						/* BillBee商店Hub表定义：仅作为结构元素，实际数据由BillBee相关ETL流程加载 */
						/* 该表是rbbe_order_p1.dvpd.json中定义的相关表，在此仅作为引用，用于建立与业务伙伴的关联 */
						{"table_name": "RBBE_SHOP_HUB",
						"table_stereotype": "hub",
						"is_only_structural_element":"true",
						"hub_key_column_name": "HK_RBBE_SHOP"}
			]
		},
		
		/* 业务通用模式下的表定义 - 这些表存储在业务通用数据仓库模式中，是业务伙伴数据的核心存储 */
		{"schema_name": "bvlt_general", 
		 "tables": [
				/* 业务伙伴Hub表 - 存储所有业务伙伴的唯一标识和业务键 */
				/* 这是业务伙伴数据的主表，所有与业务伙伴相关的卫星表和链接表都与此表关联 */
				{"table_name": "bgnr_geschaeftspartner_hub",
				"table_stereotype": "hub",
				"hub_key_column_name": "HK_bgnr_geschaeftspartner"}

				/* 业务伙伴基本信息卫星表 - 存储业务伙伴的基本属性信息 */
				/* 包含公司、名称、客户组、销售区域等业务伙伴的描述性信息 */
				,{"table_name": "bgnr_geschaeftspartner_p1_b10_sat",	
				"table_stereotype": "sat",
				"satellite_parent_table": "bgnr_geschaeftspartner_hub",
				"diff_hash_column_name": "rh_bgnr_geschaeftspartner_p1_b10_sat"}
				
				/* 业务伙伴与SAP业务伙伴的链接表 - 建立两个系统间业务伙伴的关联关系 */
				/* 此链接表实现了跨系统的业务实体映射，使得可以在不同系统间追踪同一业务伙伴 */
				,{"table_name": "bgnr_geschaeftspartner_rsap_business_partner_LNK",		
				"table_stereotype": "lnk",
				"link_key_column_name": "LK_bgnr_geschaeftspartner_rsap_business_partner",
				"link_parent_tables": ["bgnr_geschaeftspartner_hub","RSAP_BUSINESS_PARTNER_OCRD_HUB"]}

				/* 业务伙伴与SAP业务伙伴关联的事件卫星表 - 记录关联关系的有效性 */
				/* 使用driving_keys指定由业务伙伴Hub驱动，确保关联关系的完整性和一致性 */
				,{"table_name": "bgnr_geschaeftspartner_rsap_business_partner_ESAT",		
				"table_stereotype": "sat",
				"satellite_parent_table": "bgnr_geschaeftspartner_rsap_business_partner_LNK",
				 "driving_keys": ["HK_bgnr_geschaeftspartner"]}

				/* 业务伙伴与BillBee商店的链接表 - 建立业务伙伴与电子商务平台的关联关系 */
				/* 此链接表连接了内部业务伙伴与BillBee电子商务平台中的商店实体 */
				,{"table_name": "bgnr_geschaeftspartner_rbbe_shop_LNK",		
				"table_stereotype": "lnk",
				"link_key_column_name": "LK_bgnr_geschaeftspartner_rbbe_shop",
				"link_parent_tables": ["bgnr_geschaeftspartner_hub","RBBE_SHOP_HUB"]}

				/* 业务伙伴与BillBee商店关联的事件卫星表 - 记录关联关系的有效性 */
				/* 同样使用driving_keys指定由业务伙伴Hub驱动，确保数据的一致性 */
				,{"table_name": "bgnr_geschaeftspartner_rbbe_shop_ESAT",		
				"table_stereotype": "sat",
				"satellite_parent_table": "bgnr_geschaeftspartner_rbbe_shop_LNK",
			 	"driving_keys": ["HK_bgnr_geschaeftspartner"]}
			
			]		
		}
	],
	/* 删除检测配置 - 定义如何处理源系统中已删除的数据 */
	/* 使用stage_comparison过程比较暂存区和目标表，识别并处理已删除的记录 */
	"deletion_detection":[{"procedure":"stage_comparison"
                            /* 需要清理的表列表 - 这些表中的记录将根据源系统的变化进行更新或标记为已删除 */
                            ,"tables_to_cleanup":   ["bgnr_geschaeftspartner_p1_b10_sat"
													,"bgnr_geschaeftspartner_rsap_business_partner_ESAT"
													,"bgnr_geschaeftspartner_rbbe_customer_ESAT"
							]}
						]
}