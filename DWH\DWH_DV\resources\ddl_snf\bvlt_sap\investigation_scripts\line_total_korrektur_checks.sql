/* Vergleich line Total mit line Total korrektur */

Select line_s.linetotal, line_korr_s.linetotal_korrigiert,line_s.linetype, oinv_s.canceled ,oinv_s.discsum 
from RVLT_SAP.RSAP_INVOICE_LINE_INV1_P1_L20_SAT line_s
left join  BVLT_SAP.BSAP_INVOICE_LINE_INV1_KORRIGIERT_V1_B10_CURRENT_SAT line_korr_s
     on line_korr_s.hk_rsap_invoice_line_inv1 =line_s.hk_rsap_invoice_line_inv1 
join RVLT_SAP.RSAP_INVOICE_LINE_INV1_INVOICE_OINV_LNK oinv_l
    on oinv_l.hk_rsap_invoice_line_inv1 = line_s.hk_rsap_invoice_line_inv1 
join RVLT_SAP.RSAP_INVOICE_OINV_P1_L20_SAT oinv_s 
    on oinv_s.hk_rsap_invoice_oinv = oinv_l.hk_rsap_invoice_oinv 
    and oinv_s.md_valid_before=lib.dwh_far_future_date()
and not oinv_s.md_is_deleted 
where line_s.md_valid_before=lib.dwh_far_future_date()
and not line_s.md_is_deleted 
--and canceled='C' 
and oinv_s.doctype ='I'
and oinv_s.docdate = '2024-10-25'
order by oinv_s.hk_rsap_invoice_oinv, line_s.hk_rsap_invoice_line_inv1 ;


Select line_s.linetotal, line_korr_s.linetotal_korrigiert,line_s.linetype, head_s.canceled ,head_s.discsum 
from RVLT_SAP.rsap_credit_memo_line_rin1_p1_l20_sat  line_s
left join  BVLT_SAP.BSAP_credit_memo_line_rin1_KORRIGIERT_V1_B10_CURRENT_SAT line_korr_s
     on line_korr_s.hk_rsap_credit_memo_line_rin1 =line_s.hk_rsap_credit_memo_line_rin1
join RVLT_SAP.rsap_credit_memo_line_rin1_credit_memo_orin_lnk head_l
    on head_l.hk_rsap_credit_memo_line_rin1 = line_s.hk_rsap_credit_memo_line_rin1
join RVLT_SAP.rsap_credit_memo_orin_p1_l20_sat head_s 
    on head_s.hk_rsap_credit_memo_orin = head_l.hk_rsap_credit_memo_orin  
    and head_s.md_valid_before=lib.dwh_far_future_date()
and not head_s.md_is_deleted 
where line_s.md_valid_before=lib.dwh_far_future_date()
and not line_s.md_is_deleted 
--and canceled<>'C' 
order by head_s.hk_rsap_credit_memo_orin , line_s.hk_rsap_credit_memo_line_rin1 ;



/* Abruf Invioce aus Raw Vault + Korrektur für einzeltag + slpcode,name*/


Select 
		head_s.docdate,
		slp_s.u_dim1 ,
		slp_h.slpcode,
		slp_s.slpname ,
		--oinv_h.docentry ,
		--canceled,
		--itm_s.itmgrpcod ,
		sum(round(line_korr_s.linetotal_korrigiert,2)) summe_korrigiert,
		count(1)
from RVLT_SAP.RSAP_INVOICE_LINE_INV1_P1_L20_SAT line_s
left join  BVLT_SAP.BSAP_INVOICE_LINE_INV1_KORRIGIERT_V1_B10_CURRENT_SAT line_korr_s
     on line_korr_s.hk_rsap_invoice_line_inv1 =line_s.hk_rsap_invoice_line_inv1 
join RVLT_SAP.RSAP_INVOICE_LINE_INV1_INVOICE_OINV_LNK oinv_l
    on oinv_l.hk_rsap_invoice_line_inv1 = line_s.hk_rsap_invoice_line_inv1 
join RVLT_SAP.RSAP_INVOICE_OINV_P1_L20_SAT head_s 
    on head_s.hk_rsap_invoice_oinv = oinv_l.hk_rsap_invoice_oinv 
    and head_s.md_valid_before=lib.dwh_far_future_date()
	and not head_s.md_is_deleted 
join rvlt_sap.rsap_invoice_oinv_business_partner_ocrd_sales_employee_oslp_lnk slp_l
	on slp_l.hk_rsap_invoice_oinv = head_s.hk_rsap_invoice_oinv 
join rvlt_sap.rsap_invoice_oinv_business_partner_ocrd_sales_employee_oslp_esat slp_e
	on slp_e.lk_rsap_invoice_oinv_business_partner_ocrd_sales_employee_oslp = slp_l.lk_rsap_invoice_oinv_business_partner_ocrd_sales_employee_oslp 
    and slp_e.md_valid_before=lib.dwh_far_future_date()
	and not slp_e.md_is_deleted 
join rvlt_sap.rsap_sales_employee_oslp_hub slp_h
	on slp_h.hk_rsap_sales_employee_oslp = slp_l.hk_rsap_sales_employee_oslp 
join 	DV_D_MAIN_DATABASE.RVLT_SAP.RSAP_SALES_EMPLOYEE_OSLP_P1_L20_SAT slp_s
	on slp_s.hk_rsap_sales_employee_oslp =slp_l.hk_rsap_sales_employee_oslp 
    and slp_s.md_valid_before=lib.dwh_far_future_date()
	and not slp_s.md_is_deleted 
join rvlt_sap.rsap_invoice_oinv_hub oinv_h
    on oinv_h.hk_rsap_invoice_oinv = head_s.hk_rsap_invoice_oinv 
join  DV_D_MAIN_DATABASE.RVLT_SAP.RSAP_INVOICE_LINE_INV1_ITEM_OITM_LNK itm_l  
	on itm_l.hk_rsap_invoice_line_inv1 = line_s.hk_rsap_invoice_line_inv1 
join  DV_D_MAIN_DATABASE.RVLT_SAP.RSAP_INVOICE_LINE_INV1_ITEM_OITM_esat itm_e  
	on itm_e.lk_rsap_invoice_line_inv1_item_oitm = itm_l.lk_rsap_invoice_line_inv1_item_oitm 
    and itm_e.md_valid_before=lib.dwh_far_future_date()
	and not itm_e.md_is_deleted 
join DV_D_MAIN_DATABASE.RVLT_SAP.RSAP_ITEM_OITM_P1_L20_SAT itm_s
	on itm_s.hk_rsap_item_oitm = itm_l.hk_rsap_item_oitm 
    and itm_s.md_valid_before=lib.dwh_far_future_date()
	and not itm_s.md_is_deleted 
where line_s.md_valid_before=lib.dwh_far_future_date()
and not line_s.md_is_deleted 
--and canceled='C' 
and head_s.doctype ='I'
and head_s.docdate between '2024-10-20' and '2024-10-29' 
--and slp_h.slpcode ='8'
and oinv_h.company ='DEISS_DE'
and	itm_s.itmgrpcod not in('229','230')
and U_dim1 in ('10','20','01')
and slpname like'%Dede%'
group by 1,2,3,4 --,2,3
order by 1,2,3;



/* Abruf credit memo+ Korrektur für einzeltag mit docentry */


Select 
		/*head_h.docentry ,
		line_s.hk_rsap_credit_memo_line_rin1 ,
		canceled,
		itm_s.itmgrpcod ,*/
		head_s.docdate,
		slp_s.u_dim1 ,	
		slp_h.slpcode,
		slp_s.slpname ,
		sum(round(line_korr_s.linetotal_korrigiert,2)) summe_korrigiert,
		count(1)
from RVLT_SAP.rsap_credit_memo_line_rin1_p1_l20_sat  line_s
left join  BVLT_SAP.BSAP_credit_memo_line_rin1_KORRIGIERT_V1_B10_CURRENT_SAT line_korr_s
     on line_korr_s.hk_rsap_credit_memo_line_rin1 =line_s.hk_rsap_credit_memo_line_rin1
join RVLT_SAP.rsap_credit_memo_line_rin1_credit_memo_orin_lnk head_l
    on head_l.hk_rsap_credit_memo_line_rin1 = line_s.hk_rsap_credit_memo_line_rin1
join RVLT_SAP.rsap_credit_memo_orin_p1_l20_sat head_s 
    on head_s.hk_rsap_credit_memo_orin = head_l.hk_rsap_credit_memo_orin  
    and head_s.md_valid_before=lib.dwh_far_future_date()
	and not head_s.md_is_deleted 
join RVLT_SAP.rsap_credit_memo_orin_hub head_h
	on head_h.hk_rsap_credit_memo_orin =head_s.hk_rsap_credit_memo_orin 
join rvlt_sap.rsap_credit_memo_orin_business_partner_ocrd_sales_employee_oslp_lnk slp_l
	on slp_l.hk_rsap_credit_memo_orin = head_s.hk_rsap_credit_memo_orin 
join rvlt_sap.rsap_credit_memo_orin_business_partner_ocrd_sales_employee_oslp_esat slp_e
	on slp_e.lk_rsap_credit_memo_orin_business_partner_ocrd_sales_employee_oslp = slp_l.lk_rsap_credit_memo_orin_business_partner_ocrd_sales_employee_oslp 
    and slp_e.md_valid_before=lib.dwh_far_future_date()
	and not slp_e.md_is_deleted 
join rvlt_sap.rsap_sales_employee_oslp_hub slp_h
	on slp_h.hk_rsap_sales_employee_oslp = slp_l.hk_rsap_sales_employee_oslp 	
join 	DV_D_MAIN_DATABASE.RVLT_SAP.RSAP_SALES_EMPLOYEE_OSLP_P1_L20_SAT slp_s
	on slp_s.hk_rsap_sales_employee_oslp =slp_l.hk_rsap_sales_employee_oslp 
    and slp_s.md_valid_before=lib.dwh_far_future_date()
	and not slp_s.md_is_deleted 	
join  DV_D_MAIN_DATABASE.RVLT_SAP.RSAP_credit_memo_line_rin1_ITEM_OITM_LNK itm_l  
	on itm_l.hk_rsap_credit_memo_line_rin1 = line_s.hk_rsap_credit_memo_line_rin1 
join  DV_D_MAIN_DATABASE.RVLT_SAP.RSAP_credit_memo_line_rin1_ITEM_OITM_esat itm_e  
	on itm_e.lk_rsap_credit_memo_line_rin1_item_oitm = itm_l.lk_rsap_credit_memo_line_rin1_item_oitm 
    and itm_e.md_valid_before=lib.dwh_far_future_date()
	and not itm_e.md_is_deleted 
join DV_D_MAIN_DATABASE.RVLT_SAP.RSAP_ITEM_OITM_P1_L20_SAT itm_s
	on itm_s.hk_rsap_item_oitm = itm_l.hk_rsap_item_oitm 
    and itm_s.md_valid_before=lib.dwh_far_future_date()
	and not itm_s.md_is_deleted 	
where line_s.md_valid_before=lib.dwh_far_future_date()
and not line_s.md_is_deleted 
and head_s.doctype ='I'
and head_s.docdate between '2024-10-20' and '2024-10-29'
and	itm_s.itmgrpcod not in('229','230')
and head_h.company ='DEISS_DE'
and slpname like'%Dede%'
group by 1,2,3,4--,3,4,5
order by 1;
 ;



