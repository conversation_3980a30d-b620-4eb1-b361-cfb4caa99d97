{
	"dvpd_version": "0.6.2",
	"stage_properties" : [{"stage_schema":"stage_rvlt","stage_table_name":"rsap_ocrd_j1_stage"}],
	"pipeline_name": "rsap_ocrd_j1",
	"record_source_name_expression": "sap.ocrd",
	"data_extraction": {
		"fetch_module_name":"none - ddl generation only"
	},

	/* 字段映射部分：定义源系统字段如何映射到Data Vault模型中的表 */
	/* 此部分定义了SAP业务伙伴数据的字段结构及其在Data Vault模型中的映射关系 */
	"fields": [
		      /* 公司字段，作为业务伙伴Hub表的业务键之一 */
		      /* 标识业务伙伴所属的公司，与cardcode一起构成复合业务键 */
		      {"field_name": "company", 	"field_type": "Varchar(50)",	"targets": [{"table_name": "rsap_business_partner_ocrd_hub"}]},
		      /* 卡号字段，作为业务伙伴Hub表的业务键之一 */
		      /* 这是SAP系统中业务伙伴的唯一标识符，与company一起构成复合业务键 */
		 	  {"field_name": "cardcode",	"field_type": "VARCHAR(20)",	"targets": [{"table_name": "rsap_business_partner_ocrd_hub"}]},
		      /* JSON文本字段，存储业务伙伴的完整数据 */
		      /* 使用JSON格式存储所有业务伙伴属性，提供灵活性和扩展性 */
		      /* exclude_json_paths_from_change_detection参数排除更新日期字段，避免因时间戳变化而创建新历史记录 */
		 	  {"field_name": "json_text",	"field_type": "VARCHAR",	"targets": [{"table_name": "rsap_business_partner_ocrd_j1_l10_sat",
								"exclude_json_paths_from_change_detection":["UpdateDate"]}]}
			 ],
	/* Data Vault模型定义部分：定义目标数据库中的表结构和关系 */
	/* 此模型实现了SAP业务伙伴数据的存储结构，是bgnr_geschaeftspartner_p1.dvpd.json中引用的核心组件 */
	"data_vault_model": [
		/* SAP模式下的表定义 - 这些表存储在SAP专用的数据仓库模式中 */
		{"schema_name": "rvlt_sap", 
		 "tables": [
				/* SAP业务伙伴Hub表 - 存储所有SAP业务伙伴的唯一标识和业务键 */
				/* 这是SAP业务伙伴数据的主表，被多个业务流程引用，包括业务伙伴集成、发票处理等 */
				{"table_name": "rsap_business_partner_ocrd_hub",		"table_stereotype": "hub","hub_key_column_name": "HK_RSAP_BUSINESS_PARTNER_OCRD"},
				/* SAP业务伙伴卫星表 - 存储业务伙伴的完整JSON数据 */
				/* 使用JSON格式存储所有属性，避免了频繁的模型变更，适应SAP数据结构的复杂性 */
				/* 命名中的j1表示作业1，l10表示加载类型10（完整JSON加载） */
				{"table_name": "rsap_business_partner_ocrd_j1_l10_sat",	"table_stereotype": "sat","satellite_parent_table": "rsap_business_partner_ocrd_hub"
																			,"diff_hash_column_name": "RH_rsap_business_partner_ocrd_j1_l10_sat"}
				]
		}
	]
}