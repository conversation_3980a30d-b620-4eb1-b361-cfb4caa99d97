select item_order_l.hk_rbbe_order,
	count_if( item_prod_l.hk_rbbe_product<>'0000000000000000000000000000') product_item_count,
	sum(case when item_prod_l.hk_rbbe_product='0000000000000000000000000000' 
			then item_s.totalprice
			else 0
			end) totalprice_sum_of_non_product,
	sum(case when item_prod_l.hk_rbbe_product='0000000000000000000000000000' 
			then item_s.taxamount 
			else 0
			end) taxamount_sum_of_non_product,			
	count(1) full_item_count
from DV_D_MAIN_DATABASE.RVLT_BILLBEE.RBBE_ORDER_ITEM_ORDER_LNK item_order_l
join DV_D_MAIN_DATABASE.RVLT_BILLBEE.RBBE_ORDER_ITEM_ORDER_ESAT item_order_e
	on item_order_e.lk_rbbe_order_item_order =item_order_l.lk_rbbe_order_item_order 
	and item_order_e.md_valid_before =lib.dwh_far_future_date()
	and not item_order_e.md_is_deleted
join DV_D_MAIN_DATABASE.RVLT_BILLBEE.RBBE_ORDER_ITEM_ORDER_P1_L10_SAT	item_s
	on item_s.hk_rbbe_order_item = item_order_l.hk_rbbe_order_item 
	and item_s.md_valid_before =lib.dwh_far_future_date()
	and not item_s.md_is_deleted
join RVLT_BILLBEE.RBBE_ORDER_ITEM_PRODUCT_LNK item_prod_l
	on item_prod_l.hk_rbbe_order_item = item_order_l.hk_rbbe_order_item 
join RVLT_BILLBEE.RBBE_ORDER_ITEM_PRODUCT_ESAT item_prod_e
  on item_prod_e.lk_rbbe_order_item_product = item_prod_l.lk_rbbe_order_item_product 
	and item_prod_e.md_valid_before =lib.dwh_far_future_date()
	and not item_prod_e.md_is_deleted	
group by 1
order by full_item_count desc, product_item_count desc;

-- Order with 3 Items but only 2 product items: IaKpLGRbVpca0XZ4faJ8ts0Dcfk=
-- order with only non product items: e96djmrVi30WDGFtkSyMGTK0bAo=



/* Order items in data */
select count(1)
from RVLT_BILLBEE.RBBE_ORDER_ITEM_ORDER_P1_L10_SAT	item_s
where item_s.md_valid_before =lib.dwh_far_future_date()
	and not item_s.md_is_deleted;

/* Item to Product relation analysis */
select hk_rbbe_product ,count(1) 
from RVLT_BILLBEE.RBBE_ORDER_ITEM_PRODUCT_LNK item_prod_l
join RVLT_BILLBEE.RBBE_ORDER_ITEM_PRODUCT_ESAT item_prod_e
  on item_prod_e.lk_rbbe_order_item_product = item_prod_l.lk_rbbe_order_item_product 
	and item_prod_e.md_valid_before =lib.dwh_far_future_date()
	and not item_prod_e.md_is_deleted
group by 1
order by 2 desc



	
	