# Use an official Python runtime as a parent image
FROM python:3.12.6-slim

# general package installation
RUN apt-get update && \
    apt-get install -y --no-install-recommends \
        build-essential \
        curl \
        gpg \
        unixodbc \
        unixodbc-dev

# Install ODBC for DB Connection | Notwendig??
# https://learn.microsoft.com/en-us/sql/connect/odbc/linux-mac/installing-the-microsoft-odbc-driver-for-sql-server?view=sql-server-ver16&tabs=ubuntu18-install%2Calpine17-install%2Cdebian8-install%2Credhat7-13-install%2Crhel7-offline   
RUN curl -fsSL https://packages.microsoft.com/keys/microsoft.asc -o microsoft.asc && \
    gpg --dearmor microsoft.asc && \
    mv microsoft.asc.gpg /usr/share/keyrings/microsoft-prod.gpg && \
    rm microsoft.asc && \
    sh -c 'echo "deb [signed-by=/usr/share/keyrings/microsoft-prod.gpg] https://packages.microsoft.com/debian/12/prod bookworm main" > /etc/apt/sources.list.d/microsoft-prod.list' && \
    apt-get update && \
    # Installation des Microsoft ODBC SQL Treibers
    ACCEPT_EULA=Y apt-get install -y msodbcsql18 --no-install-recommends && \
    # Hinzufügen des Path's
    export PATH="$PATH:/opt/mssql-tools18/bin" 

# Run and own only the runtime files as a non-root user for security
RUN groupadd --system --gid 1000 sunddi && \    
    useradd sunddi --uid 1000 --gid 1000 --create-home --shell /bin/bash
USER 1000:1000

# Set the working directory in the container
WORKDIR /app

# Copy the current directory contents into the container at /app
COPY . /app

# Install any needed packages specified in requirements.txt
RUN export PATH="$PATH:/home/<USER>/.local/bin" && \
    pip install --no-cache-dir -r requirements.txt

USER 0
RUN apt-get -y purge build-essential && \
    rm -rf /var/lib/apt/lists /var/cache/apt/archives && \
    chown -R 1000:1000 /app

USER 1000
ENV PYTHONPATH=/app

# Run XXX.py when the container launches
# CMD ["python", "./path/to/file/XXX.py"}