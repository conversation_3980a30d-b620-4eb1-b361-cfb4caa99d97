import json

from lib.json_utils import get_json_text_content_column, sort_json_content_column
from lib.blobstorage_utils import fetch_json_source_file_from_blob_container
from lib.cimtjobinstance import CimtJobInstance, cimtjobinstance_job
from lib.connection_snf import  connection_snf_for_dwh_connection_type

from lib.dvf_basics import dvf_assemble_datavault_hash, Dvf_dwh_connection_type, HASHKEY_FOR_MISSING_DATA
from lib.dvf_sqlByConvention_snf import dvf_get_datavault_hub_elt_sql, \
    dvf_get_datavault_sat_elt_sql, dvf_execute_elt_statement_list, dvf_get_check_hash_collision_hub_elt_sql, dvf_get_check_singularity_sat_elt_sql, \
    dvf_get_datavault_esat_elt_sql, dvf_get_datavault_lnk_elt_sql, dvf_get_check_hash_collision_lnk_elt_sql

from lib.dbutils_snf import get_snf_dict_insert_sql, execute_snf_dict_bulk_insert



@cimtjobinstance_job
def fetch_and_stage_data(parent_job_instance, dwh_connection, **kwargs):

    my_job_instance = CimtJobInstance(kwargs['instance_job_name'], parent_job_instance)
    my_job_instance.start_instance()

    try:
        # ### FRAMEWORK PHASE: do processing here

        # prepare stage insert operation
        dwh_cursor = dwh_connection.cursor()
        dwh_cursor.execute("TRUNCATE TABLE stage_bvlt.bgnr_geschaeftspartner_p1_stage")
        insert_statement = get_snf_dict_insert_sql(dwh_connection, "stage_bvlt", "bgnr_geschaeftspartner_p1_stage")
        stage_data_row = {'md_inserted_at': my_job_instance.get_job_started_at().isoformat(),
                    'md_record_source': 'bgnr_geschaeftspartner_p1',
                    'md_run_id': my_job_instance.get_job_instance_id(),
                    'md_is_deleted': False
                   }

        # connect to source, and order data
        source_connection = connection_snf_for_dwh_connection_type(Dvf_dwh_connection_type.owner)
        source_cursor = source_connection.cursor()

        source_sql = f"""select concat('SAP-', ocrd_h.company, '-', ocrd_h.cardcode) as xd_geschaeftspartner_id,
                        ocrd_h.company,
                        ocrd_h.cardcode,
                        ocrd_h.hk_rsap_business_partner_ocrd,
                        999999999999999999 as id,
                        '{HASHKEY_FOR_MISSING_DATA}' as hk_rbbe_shop,
                        ocrd_h.company as gesellschaft,
                        ocrd_s.cardname as name,
                        ocrg_r.groupname as kundengruppe,
                        ocrd_s.u_dim1 as verkaufsgebiet,
                        ocrd_s.u_dim2 as verkaeufernummer
                    from RVLT_SAP.RSAP_BUSINESS_PARTNER_OCRD_HUB ocrd_h
                    join RVLT_SAP.RSAP_BUSINESS_PARTNER_OCRD_P1_L20_SAT ocrd_s 
                        on ocrd_h.hk_rsap_business_partner_ocrd = ocrd_s.hk_rsap_business_partner_ocrd
                        and ocrd_s.md_record_source <> 'SYSTEM'
                        and ocrd_s.md_valid_before = lib.dwh_far_future_date()
                        and not ocrd_s.md_is_deleted
                    left join BVLT_SAP.BSAP_CARD_GROUP_OCRG_CURRENT_REF ocrg_r on ocrg_r.company=ocrd_h.company
                        and ocrg_r.groupcode = ocrd_s.groupcode
                    
                    union all
                    
                    select distinct concat('SUND_DIGITAL-', s_h.billbeeshopid) as xd_geschaeftspartner_id,
                        '!#!missing!#!',
                        '!#!missing!#!',
                        '{HASHKEY_FOR_MISSING_DATA}' as hk_rsap_business_partner_ocrd,
                        s_h.billbeeshopid,
                        s_h.hk_rbbe_shop,
                        'SUND_DIGITAL' as gesellschaft,
                        s_sat.seller_billbeeshopname,
                        '---' as kundengruppe,
                        '---' as verkaufsgebiet,
                        '---' as verkaeufernummer   
                    from RVLT_BILLBEE.RBBE_SHOP_HUB s_h 
                    inner join rvlt_billbee.rbbe_order_shop_lnk s_lnk on s_lnk.hk_rbbe_shop=s_h.hk_rbbe_shop
                        and s_h.md_record_source <> 'SYSTEM'
                    inner join rvlt_billbee.rbbe_order_shop_p1_l10_sat s_sat on s_sat.lk_rbbe_order_shop=s_lnk.lk_rbbe_order_shop
                        and s_sat.md_record_source<>'SYSTEM'
                        and s_sat.md_valid_before = lib.dwh_far_future_date();"""
        source_cursor.execute(source_sql)
        # transform  and stage the source rows into stage
        source_rows = source_cursor.fetchall()

        stage_data_rows = []


        for source_row in source_rows:
            stage_data_row_temp = dict(stage_data_row)

            stage_data_row_temp['xd_geschaeftspartner_id'] = source_row[0]
            stage_data_row_temp['company'] = source_row[1]
            stage_data_row_temp['cardcode'] = source_row[2]
            stage_data_row_temp['billbeeshopid'] = source_row[4]
            stage_data_row_temp['gesellschaft'] = source_row[6]
            stage_data_row_temp['name'] = source_row[7]
            stage_data_row_temp['kundengruppe'] = source_row[8]
            stage_data_row_temp['verkaufsgebiet'] = source_row[9]
            stage_data_row_temp['verkaeufernummer'] = source_row[10]
            stage_data_row_temp['hk_rsap_business_partner_ocrd'] = source_row[3]
            stage_data_row_temp['hk_rbbe_shop'] = source_row[5]

            ### hashes
            stage_data_row_temp['hk_bgnr_geschaeftspartner'] = dvf_assemble_datavault_hash([stage_data_row_temp['xd_geschaeftspartner_id']])
            stage_data_row_temp['lk_bgnr_geschaeftspartner_rsap_business_partner'] = dvf_assemble_datavault_hash([stage_data_row_temp['xd_geschaeftspartner_id'], stage_data_row_temp['cardcode'], stage_data_row_temp['company']])
            stage_data_row_temp['lk_bgnr_geschaeftspartner_rbbe_shop'] = dvf_assemble_datavault_hash([stage_data_row_temp['xd_geschaeftspartner_id'],stage_data_row_temp['billbeeshopid']])
            stage_data_row_temp['rh_bgnr_geschaeftspartner_p1_b10_sat'] = dvf_assemble_datavault_hash([stage_data_row_temp['gesellschaft'],
                                                                                                       stage_data_row_temp['name'],
                                                                                                       stage_data_row_temp['kundengruppe'],
                                                                                                       stage_data_row_temp['verkaufsgebiet'],
                                                                                                       stage_data_row_temp['verkaeufernummer']])

            stage_data_rows.append(stage_data_row_temp)
            my_job_instance.count_input(1)

        execute_snf_dict_bulk_insert(dwh_cursor, insert_statement, stage_data_rows)
        dwh_connection.commit()

    # ### FRAMEWORK PHASE: Log any exception to instance
    except Exception:
        my_job_instance.end_instance_with_error(1, 'something went wrong')
        raise

    # # ### FRAMEWORK PHASE: End the instance normally
    my_job_instance.end_instance()


@cimtjobinstance_job
def load_data_to_vault(parent_job_instance, dwh_connection, file_name=None, **kwargs):


    # ### FRAMEWORK PHASE: setup your job_instance
    my_job_instance = CimtJobInstance(kwargs['instance_job_name'], parent_job_instance)
    my_job_instance.set_work_item(file_name)
    my_job_instance.start_instance()
    try:

        # ### FRAMEWORK PHASE: do processing here
        # BEGIN LOAD DATA TO VLT PART
        # general constants
        stage_schema = 'stage_bvlt'
        stage_table = 'bgnr_geschaeftspartner_p1_stage'


        dwh_cursor = dwh_connection.cursor()


        hash_collision_check_statement_list = dvf_get_check_hash_collision_hub_elt_sql(vault_table='bgnr_geschaeftspartner_hub',
        vault_schema='bvlt_general',
        stage_hk_column='HK_BGNR_GESCHAEFTSPARTNER',
        stage_bk_column_list=['XD_GESCHAEFTSPARTNER_ID'],
        db_connection=dwh_connection,
        stage_schema=stage_schema,
        stage_table=stage_table)

        dvf_execute_elt_statement_list(dwh_cursor, hash_collision_check_statement_list)

        statement_list = dvf_get_datavault_hub_elt_sql(vault_table='bgnr_geschaeftspartner_hub',
        vault_schema='bvlt_general',
        stage_hk_column='HK_BGNR_GESCHAEFTSPARTNER',
        stage_bk_column_list=['XD_GESCHAEFTSPARTNER_ID'],
        db_connection=dwh_connection,
        stage_schema=stage_schema,
        stage_table=stage_table,
        meta_job_instance_id = my_job_instance.get_job_instance_id(),
        meta_inserted_at = my_job_instance.get_job_started_at() )

        dvf_execute_elt_statement_list(dwh_cursor, statement_list)

        # ----------


        hash_collision_check_statement_list = dvf_get_check_hash_collision_lnk_elt_sql(vault_table='bgnr_geschaeftspartner_rsap_business_partner_lnk',
        vault_schema='bvlt_general',
        stage_lk_column='LK_BGNR_GESCHAEFTSPARTNER_RSAP_BUSINESS_PARTNER',
        stage_hk_column_list=['HK_BGNR_GESCHAEFTSPARTNER', 'HK_RSAP_BUSINESS_PARTNER_OCRD'],
        db_connection=dwh_connection,
        stage_schema=stage_schema,
        stage_table=stage_table)

        dvf_execute_elt_statement_list(dwh_cursor, hash_collision_check_statement_list)

        statement_list = dvf_get_datavault_lnk_elt_sql(vault_table='bgnr_geschaeftspartner_rsap_business_partner_lnk',
        vault_schema='bvlt_general',
        stage_lk_column='LK_BGNR_GESCHAEFTSPARTNER_RSAP_BUSINESS_PARTNER',
        stage_hk_column_list=['HK_BGNR_GESCHAEFTSPARTNER', 'HK_RSAP_BUSINESS_PARTNER_OCRD'],
        db_connection=dwh_connection,
        stage_schema=stage_schema,
        stage_table=stage_table,
        meta_job_instance_id = my_job_instance.get_job_instance_id(),
        meta_inserted_at = my_job_instance.get_job_started_at() )

        dvf_execute_elt_statement_list(dwh_cursor, statement_list)

        # ----------


        hash_collision_check_statement_list = dvf_get_check_hash_collision_lnk_elt_sql(vault_table='bgnr_geschaeftspartner_rbbe_shop_lnk',
        vault_schema='bvlt_general',
        stage_lk_column='LK_BGNR_GESCHAEFTSPARTNER_RBBE_SHOP',
        stage_hk_column_list=['HK_BGNR_GESCHAEFTSPARTNER', 'HK_RBBE_SHOP'],
        db_connection=dwh_connection,
        stage_schema=stage_schema,
        stage_table=stage_table)

        dvf_execute_elt_statement_list(dwh_cursor, hash_collision_check_statement_list)

        statement_list = dvf_get_datavault_lnk_elt_sql(vault_table='bgnr_geschaeftspartner_rbbe_shop_lnk',
        vault_schema='bvlt_general',
        stage_lk_column='LK_BGNR_GESCHAEFTSPARTNER_RBBE_SHOP',
        stage_hk_column_list=['HK_BGNR_GESCHAEFTSPARTNER', 'HK_RBBE_SHOP'],
        db_connection=dwh_connection,
        stage_schema=stage_schema,
        stage_table=stage_table,
        meta_job_instance_id = my_job_instance.get_job_instance_id(),
        meta_inserted_at = my_job_instance.get_job_started_at() )

        dvf_execute_elt_statement_list(dwh_cursor, statement_list)

        # ----------


        statement_list = dvf_get_datavault_esat_elt_sql(vault_esat_table='bgnr_geschaeftspartner_rsap_business_partner_esat',
        vault_schema='bvlt_general',
        stage_lk_column='LK_BGNR_GESCHAEFTSPARTNER_RSAP_BUSINESS_PARTNER',
        vault_lnk_table='bgnr_geschaeftspartner_rsap_business_partner_lnk',
        vault_driving_key_column_list=['HK_BGNR_GESCHAEFTSPARTNER'],
        stage_driving_key_column_list=['HK_BGNR_GESCHAEFTSPARTNER'],
        with_deletion_detection=False,
        db_connection=dwh_connection,
        stage_schema=stage_schema,
        stage_table=stage_table,
        meta_job_instance_id = my_job_instance.get_job_instance_id(),
        meta_inserted_at = my_job_instance.get_job_started_at() )

        dvf_execute_elt_statement_list(dwh_cursor, statement_list)

        # ----------


        statement_list = dvf_get_datavault_esat_elt_sql(vault_esat_table='bgnr_geschaeftspartner_rbbe_shop_esat',
        vault_schema='bvlt_general',
        stage_lk_column='LK_BGNR_GESCHAEFTSPARTNER_RBBE_SHOP',
        vault_lnk_table='bgnr_geschaeftspartner_rbbe_shop_lnk',
        vault_driving_key_column_list=['HK_BGNR_GESCHAEFTSPARTNER'],
        stage_driving_key_column_list=['HK_BGNR_GESCHAEFTSPARTNER'],
        with_deletion_detection=False,
        db_connection=dwh_connection,
        stage_schema=stage_schema,
        stage_table=stage_table,
        meta_job_instance_id = my_job_instance.get_job_instance_id(),
        meta_inserted_at = my_job_instance.get_job_started_at() )

        dvf_execute_elt_statement_list(dwh_cursor, statement_list)

        # ----------


        singularity_check_statement_list = dvf_get_check_singularity_sat_elt_sql(vault_table='bgnr_geschaeftspartner_p1_b10_sat',
        stage_hk_column='HK_BGNR_GESCHAEFTSPARTNER',
        stage_rh_column='RH_BGNR_GESCHAEFTSPARTNER_P1_B10_SAT',
        stage_schema=stage_schema,
        stage_table=stage_table )

        dvf_execute_elt_statement_list(dwh_cursor, singularity_check_statement_list)

        statement_list = dvf_get_datavault_sat_elt_sql(vault_table='bgnr_geschaeftspartner_p1_b10_sat',
        vault_schema='bvlt_general',
        stage_hk_column='HK_BGNR_GESCHAEFTSPARTNER',
        stage_rh_column='RH_BGNR_GESCHAEFTSPARTNER_P1_B10_SAT',
        stage_content_column_list=['GESELLSCHAFT', 'KUNDENGRUPPE ', 'NAME', 'VERKAEUFERNUMMER ', 'VERKAUFSGEBIET '],
        with_deletion_detection=False,
        db_connection=dwh_connection,
        stage_schema=stage_schema,
        stage_table=stage_table,
        meta_job_instance_id = my_job_instance.get_job_instance_id(),
        meta_inserted_at = my_job_instance.get_job_started_at() )

        dvf_execute_elt_statement_list(dwh_cursor, statement_list)

        # ----------


        # END LOAD DATA TO VLT PART
        dwh_connection.commit()
    # ### FRAMEWORK PHASE: Log any exception to instance
    except Exception:
        my_job_instance.end_instance_with_error(1, 'something went wrong')
        raise
    # # ### FRAMEWORK PHASE: End the instance normally
    my_job_instance.end_instance()



@cimtjobinstance_job
def load_vault_1(parent_job_instance=None, **kwargs):

    # ### FRAMEWORK PHASE: setup your job_instance
    my_job_instance = CimtJobInstance(kwargs['instance_job_name'], parent_job_instance)
    #my_job_instance.set_work_item(file_name)
    my_job_instance.start_instance()

    try:
        # ### FRAMEWORK PHASE: do processing here
        dwh_connection = connection_snf_for_dwh_connection_type(Dvf_dwh_connection_type.raw_vault)
        fetch_and_stage_data(my_job_instance, dwh_connection)
        load_data_to_vault(my_job_instance, dwh_connection)
        dwh_connection.close()

    # ### FRAMEWORK PHASE: Log any exception to instance
    except Exception:
        my_job_instance.end_instance_with_error(1, 'some processing failed')
        raise

    # # ### FRAMEWORK PHASE: End the instance normally
    my_job_instance.end_instance()


if __name__ == '__main__':
    # local execution
    load_vault_1()