-- generated script for stage_rvlt.rsap_rin1_j1_stage

-- DROP TABLE stage_rvlt.rsap_rin1_j1_stage;

CREATE TABLE stage_rvlt.rsap_rin1_j1_stage (
--metadata,
MD_INSERTED_AT TIMESTAMP NOT NULL,
MD_<PERSON>UN_ID INT NOT NULL,
MD_RECORD_SOURCE VARCHAR(255) NOT NULL,
MD_IS_DELETED BOOLEAN NOT NULL,
--hash keys,
HK_RSAP_CREDIT_MEMO_LINE_RIN1 CHAR(28) NOT NULL,
HK_RSAP_ITEM_OITM CHAR(28) NOT NULL,
LK_RSAP_CREDIT_MEMO_LINE_RIN1_ITEM_OITM CHAR(28) NOT NULL,
RH_RSAP_CREDIT_MEMO_LINE_RIN1_J1_L10_SAT CHAR(28) NOT NULL,
--business keys,
COMPANY Varchar(50) NULL,
DOCENTRY INTEGER NULL,
ITEMCODE Varchar(50) NULL,
<PERSON>INENUM INTEGER NULL,
--content,
JSON_TEXT VARCHAR NULL
);
-- end of script --