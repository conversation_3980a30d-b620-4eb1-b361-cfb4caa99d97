/* 
 *  GHOST Record generator.   Replace ____TARGET_TABLE_NAME____ by your target table and past result into the create statement script
 */
WITH TABLELIST ( TARGET_NAME ) AS (
SELECT lower('____TARGET_TABLE_NAME____')
)
SELECT 
'INSERT INTO ' || TABLE_SCHEMA || '.' || TABLE_NAME || chr(13) || 
' (' || STRING_AGG(COLUMN_NAME,',' ORDER BY COLUMN_NAME) || chr(13) || 
' ) VALUES ( ' || chr(13) ||
	STRING_AGG(
		CASE 
			WHEN COLUMN_NAME = 'meta_inserted_at' THEN 'now() --' 									||COLUMN_NAME || chr(13)
			WHEN COLUMN_NAME = 'meta_record_source' THEN '''SYSTEM'' --'							||COLUMN_NAME || chr(13)
			WHEN COLUMN_NAME = 'meta_job_instance_id' THEN '0 --'									||COLUMN_NAME || chr(13)
			WHEN COLUMN_NAME = 'meta_is_deleted' THEN 'false --'								    ||COLUMN_NAME || chr(13)
			WHEN COLUMN_NAME = 'meta_valid_before' THEN '''2099-01-01 00:00:00'' --' 				||COLUMN_NAME || chr(13)
			WHEN COLUMN_NAME = 'encryption_key_index' THEN '0 --'									||COLUMN_NAME || chr(13)
			WHEN LEFT(COLUMN_NAME,3) = 'eki' THEN '0 --'											||COLUMN_NAME || chr(13)
			WHEN COLUMN_NAME = 'encryption_key' THEN '''!#!missing!#!'' --'							||COLUMN_NAME || chr(13)
			WHEN LEFT(COLUMN_NAME,2) = 'hk' THEN '''ffffffffffffffffffffffffffff'' --' 				||COLUMN_NAME || chr(13)
			WHEN LEFT(COLUMN_NAME,2) = 'lk' THEN '''ffffffffffffffffffffffffffff'' --' 				||COLUMN_NAME || chr(13)
			WHEN LEFT(COLUMN_NAME,2) = 'rh' THEN '''ffffffffffffffffffffffffffff'' --' 				||COLUMN_NAME || chr(13)
			WHEN LEFT(COLUMN_NAME,2) = 'gh' THEN '''ffffffffffffffffffffffffffff'' --' 				||COLUMN_NAME || chr(13)
			WHEN DATA_TYPE 				= 'date' THEN '''1971-01-01'' --' 									||COLUMN_NAME || chr(13)
			WHEN LEFT(DATA_TYPE,9)		= 'timestamp' THEN '''1971-01-01 01:01:01'' --'				||COLUMN_NAME || chr(13)
			WHEN DATA_TYPE 				= 'character' THEN ''''||LEFT('!#!missing!#!',character_maximum_length)||''' --'										||COLUMN_NAME || chr(13)
			WHEN DATA_TYPE 				= 'numeric' THEN 'null --'									||COLUMN_NAME || chr(13)
			WHEN DATA_TYPE 				= 'character varying' THEN '''!#!missing!#!''--'			||COLUMN_NAME || chr(13)
			WHEN DATA_TYPE 				= 'boolean' THEN 'null--'									||COLUMN_NAME || chr(13)
			WHEN DATA_TYPE				= 'double precision' THEN 'null --'									||COLUMN_NAME || chr(13)
			WHEN DATA_TYPE				= 'integer' THEN 'null --'									||COLUMN_NAME || chr(13)
			WHEN DATA_TYPE				= 'json' THEN '''{"DatavaultGhostRecord":"!#!missing!#!"}'' --'	||COLUMN_NAME || chr(13)
			ELSE '##NOT_IN_CASE --'||COLUMN_NAME||'(' ||DATA_TYPE	||')'|| chr(13)		END
   ,',' ORDER BY COLUMN_NAME)
|| ');' AS statement_text___________________________________________
FROM information_schema.columns  
JOIN TABLELIST ON  table_name =  TABLELIST.TARGET_NAME
--where  table_name = 'rsowk_sellout_report_weekly_hub' 
GROUP BY table_SCHEMA,table_name
UNION
SELECT 
'INSERT INTO ' || TABLE_SCHEMA || '.' || TABLE_NAME || chr(13) || 
' (' || STRING_AGG(COLUMN_NAME,',' ORDER BY COLUMN_NAME) || chr(13) || 
' ) VALUES ( ' || chr(13) ||
	STRING_AGG(
		CASE 
			WHEN COLUMN_NAME = 'meta_inserted_at' THEN 'now() --' 									||COLUMN_NAME || chr(13)
			WHEN COLUMN_NAME = 'meta_record_source' THEN '''SYSTEM'' --'							||COLUMN_NAME || chr(13)
			WHEN COLUMN_NAME = 'meta_job_instance_id' THEN '0 --'									||COLUMN_NAME || chr(13)
			WHEN COLUMN_NAME = 'meta_is_deleted' THEN 'false --'										||COLUMN_NAME || chr(13)
			WHEN COLUMN_NAME = 'meta_valid_before' THEN '''2099-01-01 00:00:00'' --' 				||COLUMN_NAME || chr(13)
			WHEN LEFT(COLUMN_NAME,3) = 'eki' THEN '0 --'											||COLUMN_NAME || chr(13)
			WHEN COLUMN_NAME = 'encryption_key_index' THEN '0 --'									||COLUMN_NAME || chr(13)
			WHEN COLUMN_NAME = 'encryption_key' THEN '''!#!null!#!'' --'							||COLUMN_NAME || chr(13)
			WHEN LEFT(COLUMN_NAME,2) = 'hk' THEN '''0000000000000000000000000000'' --' 				||COLUMN_NAME || chr(13)
			WHEN LEFT(COLUMN_NAME,2) = 'lk' THEN '''0000000000000000000000000000'' --' 				||COLUMN_NAME || chr(13)
			WHEN LEFT(COLUMN_NAME,2) = 'rh' THEN '''0000000000000000000000000000'' --' 				||COLUMN_NAME || chr(13)
			WHEN LEFT(COLUMN_NAME,2) = 'gh' THEN '''0000000000000000000000000000'' --' 				||COLUMN_NAME || chr(13)
			WHEN DATA_TYPE 				= 'date' THEN 'null --'										||COLUMN_NAME || chr(13)
			WHEN LEFT(DATA_TYPE,9)		= 'timestamp' THEN 'null --'									||COLUMN_NAME || chr(13)
			WHEN DATA_TYPE 				= 'character' THEN 'null --'										||COLUMN_NAME || chr(13)
			WHEN DATA_TYPE 				= 'numeric' THEN 'null --'									||COLUMN_NAME || chr(13)
			WHEN DATA_TYPE 				= 'character varying' THEN 'NULL --'						||COLUMN_NAME || chr(13)
			WHEN DATA_TYPE 				= 'boolean' THEN 'null --'									||COLUMN_NAME || chr(13)
			WHEN DATA_TYPE				= 'double precision' THEN 'null --'									||COLUMN_NAME || chr(13)
			WHEN DATA_TYPE				= 'integer' THEN 'null --'									||COLUMN_NAME || chr(13)
			WHEN DATA_TYPE				= 'json' THEN '''{"DatavaultGhostRecord":"!#!null!#!"}'' --'	||COLUMN_NAME || chr(13)
			ELSE '##NOT_IN_CASE --'||COLUMN_NAME||'(' ||DATA_TYPE	||')'|| chr(13)
		END
   ,',' ORDER BY COLUMN_NAME)
|| ');'
FROM information_schema.columns 
JOIN TABLELIST ON  table_name = TABLELIST.TARGET_NAME
GROUP BY TABLE_SCHEMA,TABLE_NAME
;
