/*
 * 表名: rvlt_sap.rsap_invoice_oinv_hub
 * 
 * 描述：
 * 此表是SAP发票数据的Hub表，存储发票的唯一标识信息。
 * 作为Data Vault模型中的Hub表，它保存业务实体的唯一标识符和业务键。
 * 每条记录代表一个唯一的发票实体，不包含描述性属性。
 * 
 * 字段说明：
 * - MD_INSERTED_AT: 记录插入时间戳
 * - MD_RUN_ID: 加载过程的运行ID
 * - MD_RECORD_SOURCE: 数据来源系统
 * - HK_RSAP_INVOICE_OINV: 发票的哈希键（主键）
 * - COMPANY: 公司代码（业务键）
 * - DOCENTRY: 文档编号（业务键）
 * 
 * 相关表：
 * - rsap_invoice_oinv_j1_l10_sat: 存储发票的描述性属性
 * - rsap_invoice_line_inv1_invoice_oinv_lnk: 连接发票与发票行项目
 * - rsap_invoice_oinv_business_partner_ocrd_sales_employee_oslp_lnk: 连接发票与业务伙伴和销售员
 */

-- DROP TABLE rvlt_sap.rsap_invoice_oinv_hub;

CREATE TABLE rvlt_sap.rsap_invoice_oinv_hub (
MD_INSERTED_AT TIMESTAMP NOT NULL, -- 记录插入时间戳
MD_RUN_ID INT NOT NULL, -- 加载过程的运行ID
MD_RECORD_SOURCE VARCHAR(255) NOT NULL, -- 数据来源系统
HK_RSAP_INVOICE_OINV CHAR(28) NOT NULL, -- 发票的哈希键（主键）
COMPANY VARCHAR(50) NULL, -- 公司代码（业务键）
DOCENTRY INTEGER NULL -- 文档编号（业务键）
);

--COMMENT STATEMENTS

-- end of script --