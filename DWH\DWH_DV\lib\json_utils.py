\"""  
JSON数据处理工具模块

本模块提供了一系列用于处理JSON数据的工具函数，主要用于数据仓库ETL流程中的JSON数据处理。
主要功能包括：
- 从JSON数据中移除指定的键
- 对JSON数据进行排序，确保数据一致性

与其他模块的关系：
- 与lib.blobstorage_utils模块配合使用，处理从Blob存储获取的JSON数据
- 被processes目录下的各个处理模块使用，如rsap_oocr_j1等SAP数据处理模块

典型使用场景：
1. ETL流程中处理从SAP系统获取的JSON格式数据
2. 在计算哈希值前对JSON数据进行标准化处理

作者：SUND DWH团队
"""

import json

def get_json_text_content_column(json_data, keys_to_remove):
    """
    从JSON数据中移除指定的键
    
    该函数在ETL流程中被广泛使用，特别是在处理SAP数据时，用于移除不需要的字段，
    或者在计算哈希值前移除不应影响哈希值的字段。
    
    参数:
        json_data: 要处理的JSON数据（Python字典对象）
        keys_to_remove: 要移除的键列表
        
    返回:
        处理后的JSON数据（Python字典对象）
    """
    json_content = json_data.copy()
    for key in keys_to_remove:
        json_content.pop(key, None)
    return json_content


def sort_json_content_column(json_data):
    """
    对JSON数据内容进行排序
    
    该函数是sort_json的简单封装，在ETL流程中被广泛使用，特别是在处理SAP数据时，
    用于确保JSON数据的一致性，便于比较和计算哈希值。通常与get_json_text_content_column
    函数配合使用。
    
    参数:
        json_data: 要排序的JSON数据（Python字典对象）
        
    返回:
        排序后的JSON数据（Python字典对象）
    """
    return sort_json(json_data)


def sort_json(data):
    """
    递归地对JSON结构中的字典进行排序
    
    该函数是一个内部工具函数，主要被sort_json_content_column调用，用于确保JSON数据的一致性。
    它会递归地处理嵌套的字典和列表，对字典按键排序，确保相同内容的JSON数据具有相同的顺序。
    这在计算哈希值时特别重要，因为不同顺序的相同数据会产生不同的哈希值。
    
    参数:
        data: 要排序的数据，可以是字典、列表或基本类型
        
    返回:
        排序后的数据，保持原始结构但字典按键排序
    """
    if isinstance(data, dict):
        # Sort dictionary by keys and recursively sort values
        return {key: sort_json(value) for key, value in sorted(data.items())}
    elif isinstance(data, list):
        # Recursively sort each element in the list
        return [sort_json(item) if isinstance(item, dict) else item for item in data]
    else:
        # Return primitive types as-is (base case)
        return data
