-- generated script for stage_rvlt.rsap_inv1_j1_stage

-- DROP TABLE stage_rvlt.rsap_inv1_j1_stage;

CREATE TABLE stage_rvlt.rsap_inv1_j1_stage (
--metadata,
MD_INSERTED_AT TIMESTAMP NOT NULL,
MD_<PERSON>UN_ID INT NOT NULL,
MD_RECORD_SOURCE VARCHAR(255) NOT NULL,
MD_IS_DELETED BOOLEAN NOT NULL,
--hash keys,
HK_RSAP_INVOICE_LINE_INV1 CHAR(28) NOT NULL,
HK_RSAP_ITEM_OITM CHAR(28) NOT NULL,
LK_RSAP_INVOICE_LINE_INV1_ITEM_OITM CHAR(28) NOT NULL,
RH_RSAP_INVOICE_LINE_INV1_J1_L10_SAT CHAR(28) NOT NULL,
--business keys,
COMPANY Varchar(50) NULL,
DOCENTRY INTEGER NULL,
ITEMCODE Varchar(50) NULL,
LINENUM INTEGER NULL,
--content,
J<PERSON><PERSON>_TEXT VARCHAR NULL
);
-- end of script --