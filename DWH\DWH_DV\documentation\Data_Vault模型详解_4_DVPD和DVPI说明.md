# Data Vault模型详解：DVPD和DVPI说明

## 引言

本文档将详细解释DVPD（Data Vault Pipeline Definition）和DVPI（Data Vault Pipeline Implementation）在数据仓库项目中的作用和工作原理。这些工具是自动化数据加载过程的关键组件，对于非技术人员来说，理解它们的基本概念有助于更好地理解整个数据仓库系统。

## DVPD：Data Vault Pipeline Definition

### 什么是DVPD？

DVPD是一个JSON格式的配置文件，它定义了如何将源数据映射到Data Vault模型。简单来说，DVPD就像是一张详细的地图，告诉系统如何将源系统的数据转换为Data Vault模型中的表和字段。

### DVPD的主要组成部分

一个典型的DVPD文件包含以下主要部分：

1. **基本信息**：
   - 管道名称（pipeline_name）
   - 记录源名称（record_source_name_expression）
   - DVPD版本（dvpd_version）

2. **数据提取配置**：
   - 获取模块（fetch_module_name）：如何从源系统获取数据
   - 解析模块（parse_module_name）：如何解析数据（如JSON解析）
   - 加载模块（load_module_name）：如何加载数据到目标表

3. **字段映射**：
   - 源字段名称和类型
   - 目标表和列
   - 字段转换规则

4. **Data Vault模型定义**：
   - 模式名称（schema_name）
   - 表定义（表名、类型、关系等）
   - 键列定义（哈希键、业务键等）

### DVPD示例解析

以下是一个简化的DVPD示例，基于图中的发票行模型：

```json
{
  "dvpd_version": "0.6.2",
  "pipeline_name": "rsap_inv1_j1",
  "record_source_name_expression": "sap.inv1",
  "data_extraction": {
    "fetch_module_name": "lobster",
    "parse_module_name": "json",
    "load_module_name": "python_framework"
  },
  "fields": [
    {
      "field_name": "COMPANY",
      "field_type": "Varchar(50)",
      "targets": [
        {"table_name": "RSAP_INVOICE_LINE_INV1_HUB"}
      ]
    },
    {
      "field_name": "DOCENTRY",
      "field_type": "INTEGER",
      "targets": [{"table_name": "RSAP_INVOICE_LINE_INV1_HUB"}]
    },
    {
      "field_name": "LINENUM",
      "field_type": "INTEGER",
      "targets": [{"table_name": "RSAP_INVOICE_LINE_INV1_HUB"}]
    },
    {
      "field_name": "JSON_TEXT",
      "field_type": "VARCHAR",
      "targets": [{"table_name": "RSAP_INVOICE_LINE_INV1_J1_L10_SAT"}]
    }
  ],
  "data_vault_model": [
    {
      "schema_name": "rvlt_sap",
      "tables": [
        {
          "table_name": "RSAP_INVOICE_LINE_INV1_HUB",
          "table_stereotype": "hub",
          "hub_key_column_name": "HK_RSAP_INVOICE_LINE_INV1"
        },
        {
          "table_name": "RSAP_INVOICE_LINE_INV1_J1_L10_SAT",
          "table_stereotype": "sat",
          "satellite_parent_table": "RSAP_INVOICE_LINE_INV1_HUB",
          "diff_hash_column_name": "RH_RSAP_INVOICE_LINE_INV1_J1_L10_SAT"
        }
      ]
    }
  ]
}
```

### DVPD解释

让我们逐部分解释这个DVPD文件：

#### 基本信息

```json
"dvpd_version": "0.6.2",
"pipeline_name": "rsap_inv1_j1",
"record_source_name_expression": "sap.inv1",
```

- **dvpd_version**：DVPD格式的版本号
- **pipeline_name**：管道的唯一名称，用于标识这个数据加载过程
- **record_source_name_expression**：数据来源的标识，这里是"sap.inv1"，表示数据来自SAP系统的INV1表

#### 数据提取配置

```json
"data_extraction": {
  "fetch_module_name": "lobster",
  "parse_module_name": "json",
  "load_module_name": "python_framework"
},
```

- **fetch_module_name**："lobster"，表示使用Lobster工具从SAP系统提取数据
- **parse_module_name**："json"，表示数据格式为JSON，需要使用JSON解析器
- **load_module_name**："python_framework"，表示使用Python框架加载数据

#### 字段映射

```json
"fields": [
  {
    "field_name": "COMPANY",
    "field_type": "Varchar(50)",
    "targets": [
      {"table_name": "RSAP_INVOICE_LINE_INV1_HUB"}
    ]
  },
  // 其他字段...
],
```

- **field_name**：源数据中的字段名称
- **field_type**：字段的数据类型
- **targets**：字段映射的目标表，这里COMPANY字段映射到RSAP_INVOICE_LINE_INV1_HUB表

#### Data Vault模型定义

```json
"data_vault_model": [
  {
    "schema_name": "rvlt_sap",
    "tables": [
      {
        "table_name": "RSAP_INVOICE_LINE_INV1_HUB",
        "table_stereotype": "hub",
        "hub_key_column_name": "HK_RSAP_INVOICE_LINE_INV1"
      },
      // 其他表...
    ]
  }
],
```

- **schema_name**：数据库模式名称，这里是"rvlt_sap"
- **tables**：模型中的表定义
  - **table_name**：表名
  - **table_stereotype**：表类型（hub、link、sat等）
  - **hub_key_column_name**：Hub表的哈希键列名

## DVPI：Data Vault Pipeline Implementation

### 什么是DVPI？

DVPI是DVPD的实现细节，它包含了如何执行DVPD中定义的映射的具体步骤。DVPI不是一个单独的文件，而是一个过程，它将DVPD转换为可执行的代码和SQL语句。

### DVPI的主要功能

1. **代码生成**：
   - 根据DVPD生成数据提取、转换和加载的代码
   - 生成创建和更新Data Vault表的SQL语句

2. **哈希计算**：
   - 根据业务键计算Hub表的哈希键
   - 根据关联的Hub表计算Link表的哈希键
   - 根据内容计算Satellite表的差异哈希

3. **数据加载逻辑**：
   - 实现Hub表的加载逻辑（只插入新记录）
   - 实现Link表的加载逻辑（只插入新关系）
   - 实现Satellite表的加载逻辑（插入新记录或更新历史记录）

4. **开发人员支持**：
   - 生成开发人员备忘单（devsheet.txt）
   - 提供调试和监控信息

### DVPI生成的开发人员备忘单

DVPI会生成一个开发人员备忘单（devsheet.txt），帮助开发人员理解数据流。以下是一个简化的示例：

```
Data vault pipeline developer cheat sheet 
rendered from rsap_inv1_j1.dvpi

pipeline name: rsap_inv1_j1

------------------------------------------------------
record source: sap.inv1

Source fields:
       COMPANY    Varchar(50)
       DOCENTRY   INTEGER
       LINENUM    INTEGER
       JSON_TEXT  VARCHAR

------------------------------------------------------
Table List:
stage_table.rvlt_sap.rsap_inv1_j1_stage
table.rvlt_sap.rsap_invoice_line_inv1_hub
table.rvlt_sap.rsap_invoice_line_inv1_j1_l10_sat

------------------------------------------------------
Hash value composition

HK_RSAP_INVOICE_LINE_INV1 (key)
       COMPANY 
       DOCENTRY 
       LINENUM 

RH_RSAP_INVOICE_LINE_INV1_J1_L10_SAT (diff_hash)
       JSON_TEXT 

------------------------------------------------------
Table load method
(STAGE >  VAULT)

rsap_invoice_line_inv1_hub (/) can be loaded by convention
       key: HK_RSAP_INVOICE_LINE_INV1  >  HK_RSAP_INVOICE_LINE_INV1
       business_key: COMPANY  >  COMPANY 
       business_key: DOCENTRY  >  DOCENTRY 
       business_key: LINENUM  >  LINENUM 

rsap_invoice_line_inv1_j1_l10_sat (/) can be loaded by convention
       parent_key: HK_RSAP_INVOICE_LINE_INV1  >  HK_RSAP_INVOICE_LINE_INV1
       diff_hash: RH_RSAP_INVOICE_LINE_INV1_J1_L10_SAT  >  RH_RSAP_INVOICE_LINE_INV1_J1_L10_SAT
       content: JSON_TEXT  >  JSON_TEXT 
```

### DVPI解释

让我们逐部分解释这个开发人员备忘单：

#### 基本信息

```
Data vault pipeline developer cheat sheet 
rendered from rsap_inv1_j1.dvpi

pipeline name: rsap_inv1_j1
```

- 这是从DVPI生成的开发人员备忘单
- 管道名称是"rsap_inv1_j1"

#### 记录源和源字段

```
record source: sap.inv1

Source fields:
       COMPANY    Varchar(50)
       DOCENTRY   INTEGER
       LINENUM    INTEGER
       JSON_TEXT  VARCHAR
```

- 数据来源是"sap.inv1"
- 源字段包括COMPANY、DOCENTRY、LINENUM和JSON_TEXT，以及它们的数据类型

#### 表列表

```
Table List:
stage_table.rvlt_sap.rsap_inv1_j1_stage
table.rvlt_sap.rsap_invoice_line_inv1_hub
table.rvlt_sap.rsap_invoice_line_inv1_j1_l10_sat
```

- 这个管道涉及的表包括：
  - 一个暂存表（stage_table）
  - 一个Hub表（rsap_invoice_line_inv1_hub）
  - 一个Satellite表（rsap_invoice_line_inv1_j1_l10_sat）

#### 哈希值组成

```
Hash value composition

HK_RSAP_INVOICE_LINE_INV1 (key)
       COMPANY 
       DOCENTRY 
       LINENUM 

RH_RSAP_INVOICE_LINE_INV1_J1_L10_SAT (diff_hash)
       JSON_TEXT 
```

- Hub表的哈希键（HK_RSAP_INVOICE_LINE_INV1）由COMPANY、DOCENTRY和LINENUM字段组成
- Satellite表的差异哈希（RH_RSAP_INVOICE_LINE_INV1_J1_L10_SAT）由JSON_TEXT字段组成

#### 表加载方法

```
Table load method
(STAGE >  VAULT)

rsap_invoice_line_inv1_hub (/) can be loaded by convention
       key: HK_RSAP_INVOICE_LINE_INV1  >  HK_RSAP_INVOICE_LINE_INV1
       business_key: COMPANY  >  COMPANY 
       business_key: DOCENTRY  >  DOCENTRY 
       business_key: LINENUM  >  LINENUM 

rsap_invoice_line_inv1_j1_l10_sat (/) can be loaded by convention
       parent_key: HK_RSAP_INVOICE_LINE_INV1  >  HK_RSAP_INVOICE_LINE_INV1
       diff_hash: RH_RSAP_INVOICE_LINE_INV1_J1_L10_SAT  >  RH_RSAP_INVOICE_LINE_INV1_J1_L10_SAT
       content: JSON_TEXT  >  JSON_TEXT 
```

- 数据从暂存表（STAGE）加载到保管库表（VAULT）
- Hub表的加载方法：
  - 哈希键和业务键的映射关系
- Satellite表的加载方法：
  - 父键、差异哈希和内容字段的映射关系

## DVPD和DVPI如何支持图中的模型

现在让我们看看DVPD和DVPI如何支持图中显示的完整Data Vault模型：

### 1. 定义Hub表

DVPD定义了图中的所有Hub表（深蓝色椭圆/六边形）：
- `invoice_line_inv1`
- `invoice_oinv`
- `item_oitm`
- `business_partner_ocrd`

每个Hub表都有自己的业务键和哈希键定义。

### 2. 定义Link表

DVPD定义了图中的所有Link表（浅蓝色椭圆/六边形）：
- `invoice_line_inv1_invoice`
- `invoice_line_inv1_inv2`
- `invoice_line_inv1_item_oitm`
- `invoice_oinv_business_partner_ocrd`

每个Link表都定义了与相关Hub表的关系。

### 3. 定义Satellite表

DVPD定义了图中的所有Satellite表（黄色矩形）：
- `invoice_line_inv1_j1_l10`
- `invoice_line_inv1_p1_l20`
- `invoice_oinv_j1_l10`
- `invoice_oinv_p1_l20`
- 等等

每个Satellite表都定义了与其父表（Hub或Link）的关系，以及包含的内容字段。

### 4. 实现数据加载

DVPI根据DVPD生成代码，实现数据从源系统到Data Vault模型的加载：
- 从SAP提取数据
- 计算哈希键和差异哈希
- 加载数据到Hub、Link和Satellite表
- 处理历史变化（对于Satellite表）

## 为什么使用DVPD和DVPI？

使用DVPD和DVPI有几个重要优势：

### 1. 标准化

- 提供一致的方法来定义和实现Data Vault加载过程
- 确保所有管道遵循相同的模式和最佳实践

### 2. 自动化

- 减少手动编码工作
- 自动生成复杂的ETL逻辑
- 降低错误风险

### 3. 文档化

- 自动生成开发人员文档
- 提供清晰的数据流视图
- 便于理解和维护

### 4. 可扩展性

- 轻松添加新的数据源和目标表
- 适应业务变化和新需求
- 支持大规模数据仓库项目

## 总结

DVPD和DVPI是Data Vault实现中的关键组件，它们通过标准化和自动化数据加载过程，大大简化了数据仓库的开发和维护。虽然这些概念对于非技术人员来说可能有些抽象，但理解它们的基本原理有助于更好地理解整个数据仓库系统的工作方式。

在我们的项目中，DVPD和DVPI支持图中显示的复杂Data Vault模型，确保数据能够从源系统（如SAP）正确加载到数据仓库，并支持各种业务分析需求。
