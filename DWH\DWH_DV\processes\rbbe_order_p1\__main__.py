"""

__main__.py

"""
from lib.cimtjobinstance import CimtJobInstance, cimtjobinstance_job
from lib.dvf_basics import Dvf_dwh_connection_type
from lib.dvf_ddl_deploymentmanager_snf import Dvf_ddl_deploymentmanager_snf
from lib.connection_snf import  connection_snf_for_dwh_connection_type



import load_vault_order_1
import load_vault_orderitem_1
from fetch_billbee_data import fetch_billbee_data


def deploy_datamodel(my_job_instance):
    run_id=my_job_instance.get_job_instance_id()
    insert_date=my_job_instance.get_job_started_at()
    deployment_manager = Dvf_ddl_deploymentmanager_snf(connection_snf_for_dwh_connection_type(Dvf_dwh_connection_type.owner),run_id,insert_date)
    deployment_manager.deploy_stage_table("rvlt_billbee", "rbbe_order_p1_stage")
    deployment_manager.deploy_table("rvlt_billbee", "rbbe_order_hub")
    deployment_manager.deploy_table("rvlt_billbee", "rbbe_order_order_p1_l10_sat")
    deployment_manager.deploy_table("rvlt_billbee", "rbbe_order_order_p2_l10_sat")
    deployment_manager.deploy_table("rvlt_billbee", "rbbe_customer_hub")
    deployment_manager.deploy_table("rvlt_billbee", "rbbe_order_customer_lnk")
    deployment_manager.deploy_table("rvlt_billbee", "rbbe_order_customer_esat")
    deployment_manager.deploy_table("rvlt_billbee", "rbbe_shop_hub")
    deployment_manager.deploy_table("rvlt_billbee", "rbbe_order_shop_lnk")
    deployment_manager.deploy_table("rvlt_billbee", "rbbe_order_shop_p1_l10_sat")
    deployment_manager.deploy_table("rvlt_billbee", "rbbe_order_parent_order_lnk")
    deployment_manager.deploy_table("rvlt_billbee", "rbbe_order_parent_order_esat")
    deployment_manager.deploy_stage_table("rvlt_billbee", "rbbe_order_orderitem_p1_stage")
    deployment_manager.deploy_table("rvlt_billbee", "rbbe_order_item_hub")
    deployment_manager.deploy_table("rvlt_billbee", "rbbe_order_item_order_p1_l10_sat")
    deployment_manager.deploy_table("rvlt_billbee", "rbbe_order_hub")
    deployment_manager.deploy_table("rvlt_billbee", "rbbe_order_item_order_lnk")
    deployment_manager.deploy_table("rvlt_billbee", "rbbe_order_item_order_esat")
    deployment_manager.deploy_table("rvlt_billbee", "rbbe_product_hub")
    deployment_manager.deploy_table("rvlt_billbee", "rbbe_order_item_product_lnk")
    deployment_manager.deploy_table("rvlt_billbee", "rbbe_order_item_product_esat")



@cimtjobinstance_job
def main(**kwargs):
    # ### FRAMEWORK PHASE: setup job_instance for main module
    my_job_instance = CimtJobInstance(kwargs['instance_job_name'])
    my_job_instance.start_instance()

    # loads a previous job instance of this pipeline
    my_previous_job_instance = CimtJobInstance(kwargs['instance_job_name'], my_job_instance)

    if my_previous_job_instance.load_previous_instance(): #if previous load exists and was successful
        # gets time range that data was loaded last load for
        previous_timerange = my_previous_job_instance.get_time_range_end()
    else:
        previous_timerange = '1990-01-01 00:00:00'

    try:        # ### FRAMEWORK PHASE: do processing here
        deploy_datamodel(my_job_instance)
        # returns fetched data and the highest modified_at among all fetched data
        billbee_data, highest_modified_at = fetch_billbee_data(modified_at_min=previous_timerange)
        # loads billbee order pipeline
        load_vault_order_1.load_vault_1(billbee_data,parent_job_instance=my_job_instance) # loads billbee order pipeline
        # loads billbee order item pipeline
        load_vault_orderitem_1.load_vault_1(billbee_data,parent_job_instance=my_job_instance)

    # # ### FRAMEWORK PHASE: End with bad or good result
    except Exception as e:
        my_job_instance.end_instance_with_error(1, 'some processing failed')
        raise e
    # sets time range for this run (start = time_range_end from previous job, end = highest modified_at from fetched billbee data), that will be starting point in time for the next run
    my_job_instance.set_time_range(time_range_start=previous_timerange, time_range_end=highest_modified_at)
    my_job_instance.end_instance()


if __name__ == '__main__':
    main()
