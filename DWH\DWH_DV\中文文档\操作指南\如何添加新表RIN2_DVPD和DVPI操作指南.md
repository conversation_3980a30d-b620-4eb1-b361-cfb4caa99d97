# 如何添加新表RIN2：DVPD和DVPI操作指南

## 引言

本文档提供了在数据仓库项目中添加新表RIN2（贷项通知单行）的详细操作指南。RIN2表是SAP系统中贷项通知单行的扩展信息表，类似于INV2表与发票行的关系。本指南将帮助您了解如何通过DVPD（Data Vault Pipeline Definition）和DVPI（Data Vault Pipeline Implementation）来实现这一过程。

## 前提条件

在开始之前，请确保您具备以下条件：

1. 对SAP系统中RIN2表的结构有基本了解
2. 对Data Vault模型有基本了解，特别是Hub、Link和Satellite的概念
3. 对DVPD和DVPI的基本概念有所了解
4. 具有项目环境的访问权限

## 操作步骤概述

添加新表RIN2的过程包括以下主要步骤：

1. 分析RIN2表的结构和关系
2. 创建DVPD配置文件
3. 生成DVPI文件和开发人员备忘单
4. 创建必要的DDL脚本
5. 部署和测试

下面我们将详细介绍每个步骤。

## 1. 分析RIN2表的结构和关系

首先，我们需要了解RIN2表的结构和它与其他表的关系：

### RIN2表的基本结构

RIN2表是SAP系统中贷项通知单行的扩展信息表，包含以下关键字段：

- **公司代码（COMPANY）**：标识公司
- **文档编号（DOCENTRY）**：贷项通知单的唯一标识符2
- **行号（LINENUM）**：贷项通知单中的行号
- **其他扩展字段**：如税额、折扣、分配等信息

### 与其他表的关系

RIN2表与以下表有关系：

- **RIN1表**：贷项通知单行表，一对一关系（一个RIN1行对应一个RIN2行）
- **ORIN表**：贷项通知单头表，通过RIN1间接关联

## 2. 创建DVPD配置文件

接下来，我们需要创建DVPD配置文件，定义如何将RIN2表的数据映射到Data Vault模型：

### 创建DVPD文件

1. 在`resources/dvpd`目录下创建一个新文件：`rsap_rin2_j1.dvpd.json`
2. 参考现有的`rsap_inv2_j1.dvpd.json`文件，进行适当修改

### DVPD文件内容示例

```json
{
  "dvpd_version": "0.6.2",
  "stage_properties": [{"stage_schema":"stage_rvlt","stage_table_name":"rsap_rin2_j1_stage"}],
  "pipeline_name": "rsap_rin2_j1",
  "record_source_name_expression": "sap.rin2",
  "data_extraction": {
    "fetch_module_name":"none - ddl and cnode snippet generation only"
  },
  
  "fields": [
    {"field_name": "company", "field_type": "VARCHAR(50)", "targets": [{"table_name": "rsap_credit_memo_line_rin1_hub"}]},
    {"field_name": "docentry", "field_type": "INTEGER", "targets": [{"table_name": "rsap_credit_memo_line_rin1_hub"}]},
    {"field_name": "linenum", "field_type": "INTEGER", "targets": [{"table_name": "rsap_credit_memo_line_rin1_hub"}]},
    {"field_name": "json_text", "field_type": "VARCHAR", "targets": [{"table_name": "rsap_credit_memo_line_rin1_rin2_j1_l10_sat"}]}
  ],
  
  "data_vault_model": [
    {
      "schema_name": "rvlt_sap",
      "tables": [
        {
          "table_name": "rsap_credit_memo_line_rin1_hub",
          "table_stereotype": "hub",
          "hub_key_column_name": "HK_RSAP_CREDIT_MEMO_LINE_RIN1"
        },
        {
          "table_name": "rsap_credit_memo_line_rin1_rin2_lnk",
          "table_stereotype": "lnk",
          "link_key_column_name": "LK_RSAP_CREDIT_MEMO_LINE_RIN1_RIN2",
          "link_parent_tables": [
            {
              "parent_table_name": "rsap_credit_memo_line_rin1_hub",
              "parent_hub_key_column_name": "HK_RSAP_CREDIT_MEMO_LINE_RIN1"
            }
          ]
        },
        {
          "table_name": "rsap_credit_memo_line_rin1_rin2_j1_l10_sat",
          "table_stereotype": "sat",
          "satellite_parent_table": "rsap_credit_memo_line_rin1_rin2_lnk",
          "diff_hash_column_name": "RH_RSAP_CREDIT_MEMO_LINE_RIN1_RIN2_J1_L10_SAT"
        }
      ]
    }
  ]
}
```

### 关键配置说明

- **pipeline_name**：管道名称，这里是`rsap_rin2_j1`
- **record_source_name_expression**：记录源名称，这里是`sap.rin2`
- **fields**：定义源字段和目标表的映射
- **data_vault_model**：定义Data Vault模型中的表结构

## 3. 生成DVPI文件和开发人员备忘单

创建DVPD文件后，我们需要生成DVPI文件和开发人员备忘单：

### 生成DVPI文件

1. 使用DVPD编译器生成DVPI文件：

```bash
python -m dvpdc.dvpdc_cli resources/dvpd/rsap_rin2_j1.dvpd.json -o resources/dvpi/rsap_rin2_j1.dvpi.json
```

2. 生成开发人员备忘单：

```bash
python -m dvpdc.dvpdc_cli resources/dvpd/rsap_rin2_j1.dvpd.json -o resources/documentation/rsap_rin2_j1.devsheet.txt --devsheet
```

### 开发人员备忘单内容示例

开发人员备忘单将包含以下信息：

```
Data vault pipeline developer cheat sheet 
rendered from rsap_rin2_j1.dvpi

pipeline name: rsap_rin2_j1

------------------------------------------------------
record source: sap.rin2

Source fields:
       COMPANY    VARCHAR(50)
       DOCENTRY   INTEGER
       LINENUM    INTEGER
       JSON_TEXT  VARCHAR

------------------------------------------------------
Table List:
stage_table.rvlt_sap.rsap_rin2_j1_stage
table.rvlt_sap.rsap_credit_memo_line_rin1_hub
table.rvlt_sap.rsap_credit_memo_line_rin1_rin2_lnk
table.rvlt_sap.rsap_credit_memo_line_rin1_rin2_j1_l10_sat

------------------------------------------------------
Hash value composition

HK_RSAP_CREDIT_MEMO_LINE_RIN1 (key)
       COMPANY 
       DOCENTRY 
       LINENUM 

LK_RSAP_CREDIT_MEMO_LINE_RIN1_RIN2 (key)
       HK_RSAP_CREDIT_MEMO_LINE_RIN1

RH_RSAP_CREDIT_MEMO_LINE_RIN1_RIN2_J1_L10_SAT (diff_hash)
       JSON_TEXT
```

## 4. 创建必要的DDL脚本

接下来，我们需要创建必要的DDL脚本来创建数据库表：

### 生成DDL脚本

1. 使用DVPI文件生成DDL脚本：

```bash
python -m dvpdc.dvpdc_cli resources/dvpd/rsap_rin2_j1.dvpd.json --ddl-snf -o resources/ddl_snf/rvlt_sap/
```

2. 检查生成的DDL脚本，确保它们符合预期

### DDL脚本示例

以下是可能生成的DDL脚本示例：

```sql
-- table_rsap_credit_memo_line_rin1_rin2_lnk.sql
CREATE TABLE rvlt_sap.rsap_credit_memo_line_rin1_rin2_lnk (
  MD_INSERTED_AT TIMESTAMP NOT NULL,
  MD_RUN_ID INT NOT NULL,
  MD_RECORD_SOURCE VARCHAR(255) NOT NULL,
  LK_RSAP_CREDIT_MEMO_LINE_RIN1_RIN2 CHAR(28) NOT NULL,
  HK_RSAP_CREDIT_MEMO_LINE_RIN1 CHAR(28) NOT NULL
);

-- table_rsap_credit_memo_line_rin1_rin2_j1_l10_sat.sql
CREATE TABLE rvlt_sap.rsap_credit_memo_line_rin1_rin2_j1_l10_sat (
  MD_INSERTED_AT TIMESTAMP NOT NULL,
  MD_RUN_ID INT NOT NULL,
  MD_RECORD_SOURCE VARCHAR(255) NOT NULL,
  LK_RSAP_CREDIT_MEMO_LINE_RIN1_RIN2 CHAR(28) NOT NULL,
  RH_RSAP_CREDIT_MEMO_LINE_RIN1_RIN2_J1_L10_SAT CHAR(28) NOT NULL,
  MD_VALID_BEFORE TIMESTAMP NOT NULL,
  MD_IS_DELETED BOOLEAN NOT NULL,
  JSON_TEXT VARCHAR
);
```

## 5. 部署和测试

最后，我们需要部署DDL脚本并测试新表：

### 部署DDL脚本

1. 使用部署管理器部署DDL脚本：

```python
from lib.dvf_ddl_deploymentmanager_snf import Dvf_ddl_deploymentmanager_snf

# 创建部署管理器实例
deployment_manager = Dvf_ddl_deploymentmanager_snf(db_connection)

# 部署表
deployment_manager.deploy_table("rvlt_sap", "rsap_credit_memo_line_rin1_rin2_lnk")
deployment_manager.deploy_table("rvlt_sap", "rsap_credit_memo_line_rin1_rin2_j1_l10_sat")
```

### 测试新表

1. 创建测试数据加载脚本
2. 执行测试数据加载
3. 验证数据是否正确加载到新表中

## 总结

通过以上步骤，我们成功地在数据仓库项目中添加了新表RIN2。这个过程包括创建DVPD配置文件、生成DVPI文件和开发人员备忘单、创建DDL脚本，以及部署和测试。

这种方法的优点是：
- 标准化：遵循项目的标准方法和最佳实践
- 自动化：利用DVPD和DVPI工具自动生成必要的文件和脚本
- 文档化：自动生成开发人员备忘单，便于理解和维护
- 可扩展性：轻松适应未来的变化和新需求

## 附录：相关文件路径

- DVPD文件：`resources/dvpd/rsap_rin2_j1.dvpd.json`
- DVPI文件：`resources/dvpi/rsap_rin2_j1.dvpi.json`
- 开发人员备忘单：`resources/documentation/rsap_rin2_j1.devsheet.txt`
- DDL脚本：`resources/ddl_snf/rvlt_sap/table_rsap_credit_memo_line_rin1_rin2_lnk.sql`等

## 参考资料

- Data Vault模型详解文档
- DVPD和DVPI说明文档
- 现有的DVPD文件，如`rsap_inv2_j1.dvpd.json`
