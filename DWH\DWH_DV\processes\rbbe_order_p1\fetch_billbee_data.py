import requests
from requests.auth import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
from lib.configuration import configuration_read
from requests.exceptions import HTTPError
import time
import json
from lib.cimtjobinstance import CimtJobInstance, cimtjobinstance_job

def fetch_paginated_billbee_data(conn_params, page_num, modified_at_min):
    retries = 0
    max_retries = 5

    url = f"{conn_params['endpoint']}?page={page_num}"
    if modified_at_min is not None:
        url += f"&modifiedAtMin={modified_at_min}"

    headers = {
        'X-Billbee-Api-Key':  conn_params['api_key'],
        'Content-Type': 'application/json'
    }
    user = conn_params['user']
    password = conn_params['password']

    while retries < max_retries:
        try:
            response = requests.get(url, headers=headers, auth=HTTPBasicAuth(user, password))
            response.raise_for_status()
            return response.content
        except HTTPError as http_err:
            if response.status_code == 429:
                retry_after = int(response.headers.get("Retry-After", 1))  # Default to 1 second if not provided
                print(f"Rate limit exceeded. Retrying after {retry_after} seconds...")
                time.sleep(retry_after)
                retries += 1
            else:
                print(f"HTTP error occurred: {http_err}")
                break
        except Exception as err:
            print(f"An error occurred: {err}")
            break
    raise Exception("Max retries exceeded.")



def fetch_billbee_data(modified_at_min=None):

    params = configuration_read('dwh_billbee', 'orders', mandatory_elements=['endpoint', 'api_key', 'user', 'password'])

    page_num = 1
    total_pages = None
    all_data = []
    #collects data from each page
    while total_pages is None or page_num <= total_pages:

        data = json.loads(fetch_paginated_billbee_data(params, page_num, modified_at_min))
        #print(data.get('Paging').get('TotalPages')) #debugging
        all_data.extend(data.get('Data'))

        if total_pages is None:
            total_pages = data.get('Paging').get('TotalPages')
        page_num += 1
    #calculates the highest modified_at to know the starting point for the next load
    highest_modified_at = max(item.get('LastModifiedAt') for item in all_data)
    return all_data, highest_modified_at



if __name__ == '__main__':
    data = fetch_billbee_data(None)
    print(len(data))
    print(data)