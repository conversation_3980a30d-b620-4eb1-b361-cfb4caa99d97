{
	/* ============================================================
	 * DVPD配置文件：SAP发票行数据处理管道
	 * 文件名：rsap_inv1_j1.dvpd.json
	 * 描述：此配置文件定义了从SAP系统提取发票行数据并加载到Data Vault模型的ETL流程
	 * 相关文件：
	 *   - rsap_oinv_j1.dvpd.json (发票头数据处理)
	 *   - rsap_rin1_j1.dvpd.json (贷项通知单行数据处理)
	 *   - processes/rsap_inv1_j1/__main__.py (处理主程序)
	 *   - processes/rsap_inv1_j1/load_vault_1.py (数据加载模块)
	 * ============================================================ */

	/* DVPD版本号，用于确保配置文件与处理框架的兼容性 */
	"dvpd_version": "0.6.2",

	/* 定义数据加载的暂存区属性，包括模式名和表名 */
	/* 暂存表作为SAP源系统与Data Vault模型之间的缓冲区，便于数据验证和转换 */
	"stage_properties" : [{"stage_schema":"stage_rvlt","stage_table_name":"rsap_inv1_j1_stage"}],

	/* 定义数据管道名称，通常与源系统表名相关 */
	/* 命名约定：rsap前缀表示SAP系统，inv1表示发票行表，j1表示第一个作业 */
	"pipeline_name": "rsap_inv1_j1",
	/* 定义数据来源标识，用于记录数据的来源系统 */
	/* 此标识符将被记录在所有目标表的RECORD_SOURCE列中，用于数据溯源 */
	"record_source_name_expression": "sap.inv1",

	/* 数据提取配置，这里仅用于DDL生成，不执行实际提取 */
	/* 实际的数据提取逻辑在相应的Python模块中实现 */
	"data_extraction": {
		"fetch_module_name":"none - ddl generation only"
	},

	"fields": [
		      {"field_name": "company", 	"field_type": "Varchar(50)",	"targets": [{"table_name": "RSAP_INVOICE_LINE_INV1_HUB"}, {"table_name": "rsap_item_oitm_hub"}]},
		 	  {"field_name": "docentry",	"field_type": "INTEGER",	"targets": [{"table_name": "RSAP_INVOICE_LINE_INV1_HUB"}]},
			  {"field_name": "linenum",	"field_type": "INTEGER",	"targets": [{"table_name": "RSAP_INVOICE_LINE_INV1_HUB"}]},
		      {"field_name": "itemcode",	"field_type": "Varchar(50)",	"targets": [{"table_name": "rsap_item_oitm_hub"}]},
		 	  {"field_name": "json_text",	"field_type": "VARCHAR",	"targets": [{"table_name": "RSAP_INVOICE_LINE_INV1_J1_L10_SAT",
								"exclude_json_paths_from_change_detection":["UpdateDate"]}]}
			 ],
	"data_vault_model": [
		{"schema_name": "rvlt_sap", 
		 "tables": [
				{"table_name": "RSAP_INVOICE_LINE_INV1_HUB",
				"table_stereotype": "hub",
				"hub_key_column_name": "HK_RSAP_INVOICE_LINE_INV1"},

				{"table_name": "RSAP_INVOICE_LINE_INV1_J1_L10_SAT",	
				"table_stereotype": "sat",
				"satellite_parent_table": "RSAP_INVOICE_LINE_INV1_HUB",
				"diff_hash_column_name": "RH_RSAP_INVOICE_LINE_INV1_J1_L10_SAT"},

				 {"table_name": "rsap_item_oitm_hub",
				 "table_stereotype": "hub",
				 "hub_key_column_name": "hk_rsap_item_oitm"},

			 	{"table_name": "RSAP_INVOICE_LINE_INV1_ITEM_OITM_LNK",
				"table_stereotype": "lnk",
				"link_key_column_name": "LK_RSAP_INVOICE_LINE_INV1_ITEM_OITM",
				"link_parent_tables": ["RSAP_INVOICE_LINE_INV1_HUB","rsap_item_oitm_hub"]},

				{"table_name": "RSAP_INVOICE_LINE_INV1_ITEM_OITM_ESAT",
				"table_stereotype": "sat",
				"satellite_parent_table": "RSAP_INVOICE_LINE_INV1_ITEM_OITM_LNK",
				"driving_keys": ["HK_RSAP_INVOICE_LINE_INV1"]}

				]
		}
	]
}