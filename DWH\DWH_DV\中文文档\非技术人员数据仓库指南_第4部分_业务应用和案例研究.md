# 数据仓库项目指南：非技术人员版（第4部分：业务应用和案例研究）

## 业务应用：数据仓库如何支持业务决策

数据仓库的最终目的是支持业务决策。在本部分中，我们将探讨数据仓库如何为业务提供价值，并通过一个详细的案例研究来说明整个流程。

### 数据仓库的业务价值

1. **统一的数据视图**：整合来自不同系统（SAP、Billbee等）的数据，提供业务的全面视图
2. **历史分析**：保留历史数据，支持趋势分析和比较
3. **数据质量**：通过标准化和清洗提高数据质量
4. **业务洞察**：支持复杂的分析和报告，发现业务机会和问题

### 我们项目中的主要业务用例

1. **销售报告**：分析不同渠道、产品、客户的销售表现
2. **计划与实际比较**：将实际销售与计划目标进行比较
3. **客户分析**：了解客户购买行为和偏好
4. **产品分析**：评估产品性能和盈利能力

## 案例研究：销售报告流程

让我们通过一个详细的案例研究来说明数据如何从源系统流向最终报告。

### 业务需求

销售团队需要一个报告，显示：
- 按公司、日期、产品组、客户组、销售渠道的销售额
- 实际销售与计划目标的比较
- 同比和环比增长率

### 数据源

1. **SAP Business One**：
   - 发票数据（OINV、INV1表）
   - 贷项通知单数据（ORIN、RIN1表）
   - 客户数据（OCRD表）
   - 产品数据（OITM、OITB表）

2. **Billbee**：
   - 订单数据
   - 订单项目数据
   - 商店数据

3. **手动输入**：
   - 年度和月度销售计划数据（Excel表格）

### 数据流程详解

#### 1. 数据提取和加载到Raw Vault

**SAP数据**：
- 通过Lobster从SAP数据库提取数据，转换为JSON格式
- 加载到Raw Vault表中，如`RSAP_INVOICE_OINV_HUB`、`RSAP_INVOICE_LINE_INV1_HUB`等

**Billbee数据**：
- 通过API获取Billbee订单数据
- 加载到Raw Vault表中，如`RBBE_ORDER_HUB`、`RBBE_ORDER_ITEM_HUB`等

**计划数据**：
- 从Excel表格加载销售计划数据
- 存储在`RMUD_RGNR_GESELLSCHAFT_JAHRESPLANZAHL_DLNK`或`RMUD_RGNR_GESELLSCHAFT_MONATSPLANZAHL_DLNK`

#### 2. 业务转换在Business Vault中进行

**SAP发票数据转换**：
- 计算调整后的行总价（考虑整体折扣）
- 存储在`BSAP_INVOICE_LINE_INV1_KORRIGIERT_V1_B10_CURRENT_SAT`

**Billbee订单数据转换**：
- 处理非产品项目（如折扣、运费）
- 计算净价和税额
- 存储在`BBBE_ORDER_ITEM_ARTIKELKOSTEN_KORRIGIERT_V1_B10_SAT`

**计划数据转换**：
- 将年度/月度计划分配到工作日
- 存储在`BGNR_GESELLSCHAFT_TAGESPLANZAHL_V1_B10_SAT`

**业务伙伴整合**：
- 整合来自不同源系统的业务伙伴数据
- 存储在`BGNR_GESCHAEFTSPARTNER_P1_B10_SAT`

#### 3. 数据集市集成

创建以下表和视图：

**事实表**：
- `F_UMSATZ`：销售额事实表，包含实际销售数据
- `F_UMSATZPLANZAHL`：销售计划事实表，包含计划销售数据

**维度表**：
- `D_GESCHAEFTSPARTNER`：业务伙伴维度表

**视图**：
- `F_UMSATZ_UNGEFILTERT`：未过滤的销售数据
- `F_UMSATZ_CUBE2024`：用于分析的销售数据立方体

### 虚拟数据示例

让我们通过一些虚拟数据来说明整个流程：

#### 源数据示例

**SAP发票（OINV/INV1）**：
```
发票号：1001，日期：2024-05-15，客户：C001，公司：DEISS_DE
- 行1：产品P001，数量10，单价100欧元，总价1000欧元
- 行2：产品P002，数量5，单价200欧元，总价1000欧元
折扣：200欧元（整体折扣10%）
```

**Billbee订单**：
```
订单号：B001，日期：2024-05-15，商店：S001
- 项目1：产品P003，数量2，总价100欧元
- 项目2：运费，总价10欧元
```

**销售计划（月度）**：
```
公司：DEISS_DE，月份：2024-05，计划销售额：50000欧元
```

#### Raw Vault数据示例

**RSAP_INVOICE_LINE_INV1_HUB**：
| HK_RSAP_INVOICE_LINE_INV1 | COMPANY | DOCENTRY | LINENUM |
|---------------------------|---------|----------|---------|
| a1b2c3d4e5f6g7h8i9j0      | DEISS_DE| 1001     | 1       |
| b2c3d4e5f6g7h8i9j0k1      | DEISS_DE| 1001     | 2       |

**RSAP_INVOICE_LINE_INV1_J1_L10_SAT**：
| HK_RSAP_INVOICE_LINE_INV1 | JSON_TEXT |
|---------------------------|-----------|
| a1b2c3d4e5f6g7h8i9j0      | {"LineTotal": 1000.00, "Currency": "EUR", "ItemCode": "P001"} |
| b2c3d4e5f6g7h8i9j0k1      | {"LineTotal": 1000.00, "Currency": "EUR", "ItemCode": "P002"} |

**RBBE_ORDER_ITEM_HUB**：
| HK_RBBE_ORDER_ITEM | BILLBEEID |
|--------------------|-----------|
| c3d4e5f6g7h8i9j0k1 | 1         |
| d4e5f6g7h8i9j0k1l2 | 2         |

**RBBE_ORDER_ITEM_ORDER_P1_L10_SAT**：
| HK_RBBE_ORDER_ITEM | TOTALPRICE | TAXAMOUNT | PRODUCT_TITLE |
|--------------------|------------|-----------|---------------|
| c3d4e5f6g7h8i9j0k1 | 100.00     | 16.00     | 产品P003       |
| d4e5f6g7h8i9j0k1l2 | 10.00      | 1.60      | 运费           |

#### Business Vault数据示例

**BSAP_INVOICE_LINE_INV1_KORRIGIERT_V1_B10_CURRENT_SAT**：
| HK_RSAP_INVOICE_LINE_INV1 | ANTEIL_AM_GESAMTNETTO | LINETOTAL_KORRIGERT |
|---------------------------|------------------------|---------------------|
| a1b2c3d4e5f6g7h8i9j0      | 0.5                    | 900.00              |
| b2c3d4e5f6g7h8i9j0k1      | 0.5                    | 900.00              |

**BBBE_ORDER_ITEM_ARTIKELKOSTEN_KORRIGIERT_V1_B10_SAT**：
| HK_RBBE_ORDER_ITEM | TOTAL_PRICE_NETTO | ZU_VERTEILENDER_WERT_NETTO | PRODUCT_VERTEILSCHUESSEL | TOTAL_PRICE_NETTO_KORRIGIERT |
|--------------------|-------------------|----------------------------|--------------------------|------------------------------|
| c3d4e5f6g7h8i9j0k1 | 84.00             | 8.40                       | 1.0                      | 92.40                        |

**BGNR_GESELLSCHAFT_TAGESPLANZAHL_V1_B10_SAT**：
| HK_RGNR_GESELLSCHAFT | TAGESDATUM  | GEPLANTER_UMSATZ_EUR |
|----------------------|-------------|----------------------|
| e5f6g7h8i9j0k1l2m3n4 | 2024-05-15  | 2500.00              |

#### Data Mart数据示例

**F_UMSATZ**（销售额事实表）：
| DK_GESCHAEFTSPARTNER | GESELLSCHAFT | TAGESDATUM  | ARTIKELGRUPPE | KUNDENGRUPPE | KANAL      | UMSATZ_EUR |
|----------------------|--------------|-------------|---------------|--------------|------------|------------|
| f6g7h8i9j0k1l2m3n4o5 | DEISS_DE     | 2024-05-15  | 电子产品       | 批发商        | SAP        | 1800.00    |
| g7h8i9j0k1l2m3n4o5p6 | SUND_DIGITAL | 2024-05-15  | 电子产品       | 零售          | Billbee    | 92.40      |

**F_UMSATZPLANZAHL**（销售计划事实表）：
| GESELLSCHAFT | TAGESDATUM  | GEPLANTER_UMSATZ_EUR |
|--------------|-------------|----------------------|
| DEISS_DE     | 2024-05-15  | 2500.00              |
| SUND_DIGITAL | 2024-05-15  | 500.00               |

### 最终报告示例

基于上述数据，销售团队可以生成如下报告：

**每日销售与计划比较**：
| 日期       | 公司        | 实际销售额 | 计划销售额 | 完成率  |
|------------|-------------|------------|------------|---------|
| 2024-05-15 | DEISS_DE    | 1800.00    | 2500.00    | 72%     |
| 2024-05-15 | SUND_DIGITAL| 92.40      | 500.00     | 18.5%   |
| 合计       |             | 1892.40    | 3000.00    | 63.1%   |

**按产品组销售分析**：
| 产品组     | 销售额     | 占比    |
|------------|------------|---------|
| 电子产品   | 1892.40    | 100%    |
| 合计       | 1892.40    | 100%    |

**按销售渠道分析**：
| 销售渠道   | 销售额     | 占比    |
|------------|------------|---------|
| SAP        | 1800.00    | 95.1%   |
| Billbee    | 92.40      | 4.9%    |
| 合计       | 1892.40    | 100%    |

通过这种方式，业务用户可以获得全面、准确的销售数据，支持他们做出明智的业务决策。
