Data vault pipeline developer cheat sheet 
rendered from  rsap_oitm_j1.dvpi

pipeline name:  rsap_oitm_j1

------------------------------------------------------
record source:  sap.oitm

Source fields:
       COMPAN<PERSON>    Varchar(50)
       ITEMCODE   VARCHAR(50)
       JSON_TEXT  VARCHAR


------------------------------------------------------
Table List:
stage_table.rvlt_sap.rsap_oitm_j1_stage
table.rvlt_sap.rsap_item_oitm_hub
table.rvlt_sap.rsap_item_oitm_j1_l10_sat

------------------------------------------------------
stage table:  stage_rvlt.rsap_oitm_j1_stage
Field to Stage mapping:
	--business keys,
		COMPANY    >  COMPANY,
		ITEMCODE   >  ITEMCODE,

	--content,
		JSON_TEXT  >  JSON_TEXT

------------------------------------------------------
Hash value composition

HK_RSAP_ITEM_OITM (key)
		COMPANY 
		ITEMCODE 

RH_RSAP_ITEM_OITM_J1_L10_SAT (diff_hash)
		JSON_TEXT 


------------------------------------------------------
Table load method
(STAGE >  VAULT)

rsap_item_oitm_hub (/) can be loaded by convention
		  key: HK_RSAP_ITEM_OITM  >  HK_RSAP_ITEM_OITM
		  business_key: COMPANY  >  COMPANY 
		  business_key: ITEMCODE  >  ITEMCODE 

rsap_item_oitm_j1_l10_sat (/) can be loaded by convention
		  parent_key: HK_RSAP_ITEM_OITM  >  HK_RSAP_ITEM_OITM
		  diff_hash: RH_RSAP_ITEM_OITM_J1_L10_SAT  >  RH_RSAP_ITEM_OITM_J1_L10_SAT
		  content: JSON_TEXT  >  JSON_TEXT 

