-- generated script for stage_rvlt.rmud_planzahlen_monatlich_p1_stage

-- DROP TABLE stage_rvlt.rmud_planzahlen_monatlich_p1_stage;

CREATE TABLE stage_rvlt.rmud_planzahlen_monatlich_p1_stage (
--metadata,
MD_INSERTED_AT TIMESTAMP NOT NULL,
MD_RUN_ID INT NOT NULL,
MD_RECORD_SOURCE VARCHAR(255) NOT NULL,
MD_IS_DELETED BOOLEAN NOT NULL,
--hash keys,
GH_RMUD_RGNR_GESELLSCHAFT_MONATSPLANZAHL_P1_L10_MSAT CHAR(28) NOT NULL,
HK_RGNR_GESELLSCHAFT CHAR(28) NOT NULL,
LK_RMUD_RGNR_GESELLSCHAFT_MONATSPLANZAHL CHAR(28) NOT NULL,
--business keys,
FIRMA Varchar(50) NULL,
JAHR NUMBER(20,0) NULL,
M<PERSON>AT NUMBER(20,0) NULL,
--content,
PLANZAHL NUMBER(20,2) NULL,
UNTERGR<PERSON>PE_JSON VARCHAR NULL
);
-- end of script --