CREATE VIEW bvlt_sap.bsap_card_group_ocrg_current_ref AS
SELECT ocrg_s.* exclude(MD_RUN_ID, MD_IS_DELETED,MD_VALID_BEFORE,HK_RSAP_CARD_GROUP_OCRG,RH_RSAP_CARD_GROUP_OCRG_J1_L10_SAT),
        ocrg_h.company,
        ocrg_h.groupcode
FROM RVLT_SAP.RSAP_CARD_GROUP_OCRG_HUB ocrg_h
JOIN RVLT_SAP.RSAP_CARD_GROUP_OCRG_P1_L20_SAT ocrg_s ON ocrg_h.hk_rsap_card_group_ocrg = ocrg_s.hk_rsap_card_group_ocrg
    AND ocrg_s.md_valid_before = lib.dwh_far_future_date()
    AND NOT ocrg_s.md_is_deleted
    AND ocrg_s.MD_RECORD_SOURCE <> 'SYSTEM';