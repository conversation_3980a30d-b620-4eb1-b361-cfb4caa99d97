{
	/* ============================================================
	 * DVPD配置文件：SAP销售员数据处理管道
	 * 文件名：rsap_oslp_j1.dvpd.json
	 * 描述：此配置文件定义了从SAP系统提取销售员数据并加载到Data Vault模型的ETL流程
	 * 相关文件：
	 *   - rsap_oinv_j1.dvpd.json (发票头数据处理，引用销售员数据)
	 *   - rsap_orin_j1.dvpd.json (贷项通知单头数据处理，引用销售员数据)
	 *   - processes/rsap_oslp_j1/__main__.py (处理主程序)
	 *   - processes/rsap_oslp_j1/load_vault_1.py (数据加载模块)
	 * ============================================================ */

	/* DVPD版本号，用于确保配置文件与处理框架的兼容性 */
	"dvpd_version": "0.6.2",

	/* 定义数据加载的暂存区属性，包括模式名和表名 */
	/* 暂存表作为SAP源系统与Data Vault模型之间的缓冲区，便于数据验证和转换 */
	"stage_properties" : [{"stage_schema":"stage_rvlt","stage_table_name":"rsap_oslp_j1_stage"}],

	/* 定义数据管道名称，通常与源系统表名相关 */
	/* 命名约定：rsap前缀表示SAP系统，oslp表示销售员表，j1表示第一个作业 */
	"pipeline_name": "rsap_oslp_j1",

	/* 定义数据来源标识，用于记录数据的来源系统 */
	/* 此标识符将被记录在所有目标表的RECORD_SOURCE列中，用于数据溯源 */
	"record_source_name_expression": "sap.oslp",

	/* 数据提取配置，这里仅用于DDL生成，不执行实际提取 */
	/* 实际的数据提取逻辑在相应的Python模块中实现 */
	"data_extraction": {
		"fetch_module_name":"none - ddl generation only"
	},

	/* ============================================================
	 * 字段映射部分：定义源系统字段如何映射到Data Vault模型中的表
	 * 每个字段定义包含：字段名称、数据类型和目标表映射
	 * ============================================================ */
	"fields": [
		      /* company字段映射到销售员Hub表，作为业务键的一部分 */
		      /* 在SAP环境中，公司代码是区分不同法律实体的关键标识符 */
		      {"field_name": "company", 	"field_type": "Varchar(50)",	"targets": [{"table_name": "rsap_sales_employee_oslp_hub"}]},
		 	  /* slpcode是销售员Hub的主要业务键之一 */
		 	  /* 在SAP中，slpcode是销售员的唯一标识符 */
		 	  {"field_name": "slpcode",	"field_type": "VARCHAR(20)",	"targets": [{"table_name": "rsap_sales_employee_oslp_hub"}]},
		 	  /* json_text字段存储完整的销售员JSON数据，用于保留所有原始信息 */
		 	  /* exclude_json_paths_from_change_detection属性指定某些路径不参与变更检测 */
		 	  /* 例如，UpdateDate字段的变化不会触发新版本的创建，避免无意义的历史记录 */
		 	  {"field_name": "json_text",	"field_type": "VARCHAR",	"targets": [{"table_name": "rsap_sales_employee_oslp_j1_l10_sat",
								"exclude_json_paths_from_change_detection":["UpdateDate"]}]}
			 ],
	/* ============================================================
	 * Data Vault模型定义部分
	 * 此部分定义了目标数据仓库中的表结构，包括Hub和Satellite表
	 * 遵循Data Vault 2.0建模方法论，实现高度可扩展和灵活的数据模型
	 * ============================================================ */
	"data_vault_model": [
		/* 定义模式名称，通常按照业务领域或源系统分组 */
		{"schema_name": "rvlt_sap", 
		 /* 定义该模式下的所有表 */
		 "tables": [
				/* 销售员Hub表定义：存储销售员的业务键和哈希键 */
				{"table_name": "rsap_sales_employee_oslp_hub",		"table_stereotype": "hub","hub_key_column_name": "HK_RSAP_sales_employee_oslp"},
				/* 销售员卫星表定义：存储销售员的详细属性和历史变化 */
				{"table_name": "rsap_sales_employee_oslp_j1_l10_sat",	"table_stereotype": "sat","satellite_parent_table": "rsap_sales_employee_oslp_hub"
																											,"diff_hash_column_name": "RH_rsap_sales_employee_oslp_j1_l10_sat"}
				]
		}
	],
	 /* ============================================================
	 * 删除检测规则：定义如何处理源系统中已删除的数据
	 * 此规则确保当销售员在源系统中被删除时，数据仓库中的记录也能相应更新
	 * ============================================================ */
	 "deletion_detection_rules":[{"procedure":"stage_comparison" /* 使用暂存表比较方法检测删除 */
						,"tables_to_cleanup":["rsap_sales_employee_oslp_j1_l10_sat"]}] /* 指定需要清理的表 */
}