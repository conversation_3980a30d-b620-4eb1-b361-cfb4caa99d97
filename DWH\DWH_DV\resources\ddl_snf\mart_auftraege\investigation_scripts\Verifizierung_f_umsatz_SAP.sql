/* Basisanalyse nach Gesellschaft */
select gesellschaft,handelsplatz_aka_u_dim1_von_vertriebsmitarbeiter ,count(1) anzahl_zeilen_in_f_umsatz,min(tagesdatum),max(tagesdatum)
from MART_AUFTRAEGE.F_UMSATZ
group by 1,2
order by 1,2

/* Kombinationen von handleplatz und verkaufsgebiet */
select gesellschaft ,HANDELSPLATZ_AKA_U_DIM1_VON_VERTRIEBSMITARBEITER,VERKAUFSGEBIET,count(1) anzahl
from MART_AUFTRAEGE.F_UMSATZ
group by 1,2,3
order by 1,2,3

/* Artikelgruppen */
select gesellschaft ,artikelgruppe , ARITKELGRUPPE_CODE, count(1) anzahl
from MART_AUFTRAEGE.F_UMSATZ
group by 1,2,3
order by 1,2,3

/* kundengruppen */
select gesellschaft ,kundengruppe, count(1) anzahl
from MART_AUFTRAEGE.F_UMSATZ
group by 1,2
order by 1,2

/* Währungs Umrechungswerte in f_umsatz*/
select waehrung ,rate,gesellschaft,count(1) anzahl_positionen,min(tagesdatum),max(tagesdatum)
from MART_AUFTRAEGE.F_UMSATZ
group by 1,2,3
order by 1,2,3



/* Datenänderungsanalyse */
select min(md_inserted_at ), max(md_inserted_at)
from DV_D_MAIN_DATABASE.RVLT_SAP.RSAP_INVOICE_LINE_INV1_J1_L10_SAT
where md_record_source <>'SYSTEM'
and md_valid_before = lib.dwh_far_future_date();

select min(md_inserted_at ), max(md_inserted_at)
from DV_D_MAIN_DATABASE.RVLT_SAP.rsap_credit_memo_line_rin1_j1_l10_sat 
where md_record_source <>'SYSTEM'
and md_valid_before = lib.dwh_far_future_date();

select min(md_inserted_at ), max(md_inserted_at)
from DV_D_MAIN_DATABASE.RVLT_SAP.rsap_credit_memo_line_rin1_j1_l10_sat 
where md_record_source <>'SYSTEM';

/* Referenzdatenmanagement und Vergleich */

-- create schema ZZ_VERIFIZIERUNG_REFERENZ;

create table ZZ_VERIFIZIERUNG_REFERENZ.MAFT_F_UMSATZ_2024_oktober_20241128_xxx
as 
select 
	current_timestamp()  as md_inserted_at,
	gesellschaft,
	tagesdatum ,
	handelsplatz_aka_u_dim1_von_vertriebsmitarbeiter hp,
	kundengruppe ,
	ARTIKELGRUPPE_CODE,
	sum(umsatz_eur) summe_umsatz_eur
from DV_D_MAIN_DATABASE.MART_AUFTRAEGE.F_UMSATZ
where tagesdatum between '2024-10-01' and '2024-10-31'
and kundengruppe <>'Intercompany'
group by 1,2,3,4,5,6;

------------- ###################################################
------------- ###################################################
------------- vvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvv

with current_result as (
select 
	gesellschaft,
	handelsplatz_aka_u_dim1_von_vertriebsmitarbeiter hp,
	kundengruppe ,
	tagesdatum ,
	ARTIKELGRUPPE_CODE,
	sum(umsatz_eur) summe_umsatz_eur
from DV_D_MAIN_DATABASE.MART_AUFTRAEGE.F_UMSATZ
where tagesdatum between '2024-10-01' and '2024-10-31'
and kundengruppe <>'Intercompany'
group by 1,2,3,4,5
)
, reference_result as (
select 
	gesellschaft,
	hp,
	kundengruppe,
	tagesdatum ,
	ARTIKELGRUPPE_CODE,
	summe_umsatz_eur
--from ZZ_VERIFIZIERUNG_REFERENZ.MAFT_F_UMSATZ_2024_oktober_20241128_0954
from ZZ_VERIFIZIERUNG_REFERENZ.MAFT_F_UMSATZ_2024_oktober_20241128_1157
where tagesdatum between '2024-10-01' and '2024-10-31'
and kundengruppe <>'Intercompany'
)
select *,'<-- current' location  from current_result
minus
select *,'<-- current' from reference_result
union
select *,'<-- reference' from reference_result
minus
select *,'<-- reference' from current_result
order by 1,2,3,4 ;
------------- ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
------------- ###################################################
------------- ###################################################

/* Zusammenfassung für Abgleich  DEISS_DE*/
select 
	gesellschaft,
	handelsplatz_aka_u_dim1_von_vertriebsmitarbeiter hp,
	tagesdatum ,
	sum(umsatz_eur) summe_umsatz_eur
from DV_D_MAIN_DATABASE.MART_AUFTRAEGE.F_UMSATZ
where tagesdatum between '2024-10-01' and '2024-10-31'
and gesellschaft = 'DEISS_DE'
--and kundengruppe <> 'Intercompany'
and handelsplatz_aka_u_dim1_von_vertriebsmitarbeiter in ('GH','HGH','Fabrik')
and ARTIKELGRUPPE_CODE not in ('229','230') -- ohne Handschuhe
group by 1,2,3
order by 1,2,3;

/* Zusammenfassung für Abgleich  DEISS_DE - Bingold Verkäufe*/
select 
	gesellschaft,
	handelsplatz_aka_u_dim1_von_vertriebsmitarbeiter,
	tagesdatum ,
	sum(umsatz_eur) summe_umsatz_eur
from DV_D_MAIN_DATABASE.MART_AUFTRAEGE.F_UMSATZ
where tagesdatum between '2024-10-01' and '2024-10-31'
and gesellschaft = 'DEISS_DE'
--and kundengruppe <> 'Intercompany'
and handelsplatz_aka_u_dim1_von_vertriebsmitarbeiter in ('GH','HGH','Fabrik')
and ARTIKELGRUPPE_CODE  in ('229','230') -- nur Handschuhe
group by 1,2,3
order by 1,2,3;


/* Zusammenfassung für Abgleich  BINGOLD*/
select 
	gesellschaft,
	tagesdatum ,
	handelsplatz_aka_u_dim1_von_vertriebsmitarbeiter,
	sum(umsatz_eur) summe_umsatz_eur
from DV_D_MAIN_DATABASE.MART_AUFTRAEGE.F_UMSATZ
where tagesdatum between '2024-10-01' and '2024-10-31'
and gesellschaft = 'BINGOLD_DE'
and kundengruppe <> 'Intercompany'
--where tagesdatum between '2024-11-01' and '2024-11-11'
--and handelsplatz_aka_u_dim1_von_vertriebsmitarbeiter in ('GH','HGH','Fabrik')
--and handelsplatz_aka_u_dim1_von_vertriebsmitarbeiter in ('GH','HGH','Fabrik')
--and ARTIKELGRUPPE_CODE not in ('229','230') -- ohne Handschuhe
group by 1,2,3
order by 1,2,3;


/* Zusammenfassung für Abgleich  DEISS_CH*/
select 
	gesellschaft,
	tagesdatum ,
	handelsplatz_aka_u_dim1_von_vertriebsmitarbeiter hp,
--	artikelgruppe ,
	sum(umsatz_eur) summe_umsatz_eur
from DV_D_MAIN_DATABASE.MART_AUFTRAEGE.F_UMSATZ
where tagesdatum between '2024-10-01' and '2024-10-31'
and gesellschaft = 'DEISS_CH'
group by 1,2,3
order by 1,2,3;


/* Zusammenfassung für Abgleich  DEISS_NL*/
select 
	gesellschaft,
	tagesdatum ,
	handelsplatz_aka_u_dim1_von_vertriebsmitarbeiter hp,
--	artikelgruppe ,
	sum(umsatz_eur) summe_umsatz_eur
from DV_D_MAIN_DATABASE.MART_AUFTRAEGE.F_UMSATZ
where tagesdatum between '2024-10-01' and '2024-10-31'
and gesellschaft = 'DEISS_NL'
--and kundengruppe <> 'HIGHCLEAN GROUP'
--where tagesdatum between '2024-11-01' and '2024-11-11'
--and handelsplatz_aka_u_dim1_von_vertriebsmitarbeiter in ('GH','HGH','Fabrik')
--and handelsplatz_aka_u_dim1_von_vertriebsmitarbeiter in ('GH','HGH','Fabrik')
--and ARTIKELGRUPPE_CODE not in ('229','230') -- ohne Handschuhe
group by 1,2,3
order by 1,2,3;



/* Zusammenfassung für Abgleich  DEISS_AT*/
select 
	gesellschaft,
	tagesdatum ,
	handelsplatz_aka_u_dim1_von_vertriebsmitarbeiter hp,
--	artikelgruppe ,
	sum(umsatz_eur) summe_umsatz_eur
from DV_D_MAIN_DATABASE.MART_AUFTRAEGE.F_UMSATZ
where tagesdatum between '2024-10-01' and '2024-10-31'
and gesellschaft = 'DEISS_AT'
--where tagesdatum between '2024-11-01' and '2024-11-11'
--and handelsplatz_aka_u_dim1_von_vertriebsmitarbeiter in ('GH','HGH','Fabrik')
--and handelsplatz_aka_u_dim1_von_vertriebsmitarbeiter in ('GH','HGH','Fabrik')
--and ARTIKELGRUPPE_CODE not in ('229','230') -- ohne Handschuhe
group by 1,2,3
order by 1,2,3;

/* Zusammenfassung für Abgleich  FIPP*/
select 
	gesellschaft,
	kundengruppe,
	tagesdatum ,
	sum(umsatz_eur) summe_umsatz_eur
from DV_D_MAIN_DATABASE.MART_AUFTRAEGE.F_UMSATZ
where tagesdatum between '2024-10-01' and '2024-10-31'
and gesellschaft = 'FIPP_DE'
group by 1,2,3
order by 1,2,3;

-- aus den Referenzdaten
select gesellschaft,
	kundengruppe,
	tagesdatum ,
	sum(summe_umsatz_eur)
from MART_AUFTRAEGE_REFERENZ._20241128_0954_F_UMSATZ_2024_OKTOBER
where gesellschaft = 'FIPP_DE'
group by 1,2,3
order by 1,2,3;


/* Zusammenfassung für Abgleich  FIPP- Rossmann*/
select 
	gesellschaft,
	--kundengruppe,
	tagesdatum ,
	sum(umsatz_eur) summe_umsatz_eur
from DV_D_MAIN_DATABASE.MART_AUFTRAEGE.F_UMSATZ
where tagesdatum between '2024-10-01' and '2024-10-31'
and gesellschaft = 'FIPP_DE'
and kundengruppe like 'Rossmann%'
--where tagesdatum between '2024-11-01' and '2024-11-11'
--and handelsplatz_aka_u_dim1_von_vertriebsmitarbeiter in ('GH','HGH','Fabrik')
--and handelsplatz_aka_u_dim1_von_vertriebsmitarbeiter in ('GH','HGH','Fabrik')
--and ARTIKELGRUPPE_CODE not in ('229','230') -- ohne Handschuhe
group by 1,2
order by 1,2;


/* Zusammenfassung für Abgleich  SUND_COLOR*/
select 
	gesellschaft,
	--kundengruppe,
	tagesdatum ,
	sum(umsatz_eur) summe_umsatz_eur
from DV_D_MAIN_DATABASE.MART_AUFTRAEGE.F_UMSATZ
where tagesdatum between '2024-10-01' and '2024-10-31'
and gesellschaft = 'SUND_COLOR'
--where tagesdatum between '2024-11-01' and '2024-11-11'
--and handelsplatz_aka_u_dim1_von_vertriebsmitarbeiter in ('GH','HGH','Fabrik')
--and handelsplatz_aka_u_dim1_von_vertriebsmitarbeiter in ('GH','HGH','Fabrik')
--and ARTIKELGRUPPE_CODE not in ('229','230') -- ohne Handschuhe
group by 1,2
order by 1,2;
