{"dvdp_compiler": "dvpdc reference compiler,  release 0.6.2", "dvpi_version": "0.6.2", "compile_timestamp": "2025-05-21 11:34:37", "dvpd_version": "0.6.2", "pipeline_name": "rsap_rin2_j1", "pipeline_revision_tag": "--none--", "dvpd_filename": "rsap_rin2_j1.dvpd.json", "tables": [{"table_name": "rsap_credit_memo_line_rin1_hub", "table_stereotype": "hub", "schema_name": "rvlt_sap", "storage_component": "", "columns": [{"column_name": "MD_INSERTED_AT", "is_nullable": false, "column_class": "meta_load_date", "column_type": "TIMESTAMP"}, {"column_name": "MD_RUN_ID", "is_nullable": false, "column_class": "meta_load_process_id", "column_type": "INT"}, {"column_name": "MD_RECORD_SOURCE", "is_nullable": false, "column_class": "meta_record_source", "column_type": "VARCHAR(255)"}, {"column_name": "HK_RSAP_CREDIT_MEMO_LINE_RIN1", "is_nullable": false, "column_class": "key", "column_type": "CHAR(28)"}, {"column_name": "COMPANY", "is_nullable": true, "column_class": "business_key", "column_type": "VARCHAR(50)", "prio_for_column_position": 50000}, {"column_name": "DOCENTRY", "is_nullable": true, "column_class": "business_key", "column_type": "INTEGER", "prio_for_column_position": 50000}, {"column_name": "LINENUM", "is_nullable": true, "column_class": "business_key", "column_type": "INTEGER", "prio_for_column_position": 50000}]}, {"table_name": "rsap_credit_memo_line_rin1_rin2_dlnk", "table_stereotype": "lnk", "schema_name": "rvlt_sap", "storage_component": "", "columns": [{"column_name": "MD_INSERTED_AT", "is_nullable": false, "column_class": "meta_load_date", "column_type": "TIMESTAMP"}, {"column_name": "MD_RUN_ID", "is_nullable": false, "column_class": "meta_load_process_id", "column_type": "INT"}, {"column_name": "MD_RECORD_SOURCE", "is_nullable": false, "column_class": "meta_record_source", "column_type": "VARCHAR(255)"}, {"column_name": "HK_RSAP_CREDIT_MEMO_LINE_RIN1", "is_nullable": false, "column_class": "parent_key", "column_type": "CHAR(28)", "parent_key_column_name": "HK_RSAP_CREDIT_MEMO_LINE_RIN1", "parent_table_name": "rsap_credit_memo_line_rin1_hub"}, {"column_name": "LK_RSAP_CREDIT_MEMO_LINE_RIN1_RIN2", "is_nullable": false, "column_class": "key", "column_type": "CHAR(28)"}, {"column_name": "GROUPNUM", "is_nullable": true, "column_class": "dependent_child_key", "column_type": "INTEGER", "prio_for_column_position": 50000}]}, {"table_name": "rsap_credit_memo_line_rin1_rin2_j1_l10_sat", "table_stereotype": "sat", "schema_name": "rvlt_sap", "storage_component": "", "has_deletion_flag": true, "is_effectivity_sat": false, "is_enddated": true, "is_multiactive": false, "compare_criteria": "key+current", "uses_diff_hash": true, "columns": [{"column_name": "MD_INSERTED_AT", "is_nullable": false, "column_class": "meta_load_date", "column_type": "TIMESTAMP"}, {"column_name": "MD_RUN_ID", "is_nullable": false, "column_class": "meta_load_process_id", "column_type": "INT"}, {"column_name": "MD_RECORD_SOURCE", "is_nullable": false, "column_class": "meta_record_source", "column_type": "VARCHAR(255)"}, {"column_name": "MD_IS_DELETED", "is_nullable": false, "column_class": "meta_deletion_flag", "column_type": "BOOLEAN"}, {"column_name": "MD_VALID_BEFORE", "is_nullable": false, "column_class": "meta_load_enddate", "column_type": "TIMESTAMP"}, {"column_name": "LK_RSAP_CREDIT_MEMO_LINE_RIN1_RIN2", "is_nullable": false, "column_class": "parent_key", "column_type": "CHAR(28)", "parent_key_column_name": "LK_RSAP_CREDIT_MEMO_LINE_RIN1_RIN2", "parent_table_name": "rsap_credit_memo_line_rin1_rin2_dlnk"}, {"column_name": "RH_RSAP_CREDIT_MEMO_LINE_RIN1_RIN2_J1_L10_SAT", "is_nullable": false, "column_class": "diff_hash", "column_type": "CHAR(28)"}, {"column_name": "JSON_TEXT", "is_nullable": true, "column_class": "content", "column_type": "VARCHAR", "exclude_from_change_detection": false, "prio_for_column_position": 50000}]}], "data_extraction": {"fetch_module_name": "none - ddl and cnode snippet generation only"}, "parse_sets": [{"stage_properties": [{"stage_schema": "stage_rvlt", "stage_table_name": "rsap_rin2_j1_stage", "storage_component": ""}], "record_source_name_expression": "sap.rin2", "fields": [{"field_type": "<PERSON><PERSON><PERSON><PERSON>(50)", "field_position": 1, "needs_encryption": false, "field_name": "COMPANY"}, {"field_type": "INTEGER", "field_position": 2, "needs_encryption": false, "field_name": "DOCENTRY"}, {"field_type": "INTEGER", "field_position": 3, "needs_encryption": false, "field_name": "LINENUM"}, {"field_type": "INTEGER", "field_position": 4, "needs_encryption": false, "field_name": "GROUPNUM"}, {"field_type": "VARCHAR", "field_position": 5, "needs_encryption": false, "field_name": "JSON_TEXT"}], "hashes": [{"stage_column_name": "HK_RSAP_CREDIT_MEMO_LINE_RIN1", "hash_origin_table": "rsap_credit_memo_line_rin1_hub", "column_class": "key", "hash_fields": [{"field_name": "COMPANY", "prio_in_key_hash": 0, "field_target_table": "rsap_credit_memo_line_rin1_hub", "field_target_column": "COMPANY"}, {"field_name": "DOCENTRY", "prio_in_key_hash": 0, "field_target_table": "rsap_credit_memo_line_rin1_hub", "field_target_column": "DOCENTRY"}, {"field_name": "LINENUM", "prio_in_key_hash": 0, "field_target_table": "rsap_credit_memo_line_rin1_hub", "field_target_column": "LINENUM"}], "column_type": "CHAR(28)", "hash_encoding": "BASE64", "hash_function": "sha-1", "hash_concatenation_seperator": "|", "hash_timestamp_format_sqlstyle": "YYYY-MM-DD HH24:MI:SS.US", "hash_null_value_string": "", "model_profile_name": "_default", "hash_name": "KEY_OF_RSAP_CREDIT_MEMO_LINE_RIN1_HUB"}, {"stage_column_name": "LK_RSAP_CREDIT_MEMO_LINE_RIN1_RIN2", "hash_origin_table": "rsap_credit_memo_line_rin1_rin2_dlnk", "column_class": "key", "hash_fields": [{"field_name": "COMPANY", "prio_in_key_hash": 0, "field_target_table": "rsap_credit_memo_line_rin1_hub", "field_target_column": "COMPANY", "parent_declaration_position": 1}, {"field_name": "DOCENTRY", "prio_in_key_hash": 0, "field_target_table": "rsap_credit_memo_line_rin1_hub", "field_target_column": "DOCENTRY", "parent_declaration_position": 1}, {"field_name": "LINENUM", "prio_in_key_hash": 0, "field_target_table": "rsap_credit_memo_line_rin1_hub", "field_target_column": "LINENUM", "parent_declaration_position": 1}, {"field_name": "GROUPNUM", "prio_in_key_hash": 0, "field_target_table": "rsap_credit_memo_line_rin1_rin2_dlnk", "field_target_column": "GROUPNUM"}], "column_type": "CHAR(28)", "hash_encoding": "BASE64", "hash_function": "sha-1", "hash_concatenation_seperator": "|", "hash_timestamp_format_sqlstyle": "YYYY-MM-DD HH24:MI:SS.US", "hash_null_value_string": "", "model_profile_name": "_default", "hash_name": "KEY_OF_RSAP_CREDIT_MEMO_LINE_RIN1_RIN2_DLNK"}, {"stage_column_name": "RH_RSAP_CREDIT_MEMO_LINE_RIN1_RIN2_J1_L10_SAT", "hash_origin_table": "rsap_credit_memo_line_rin1_rin2_j1_l10_sat", "multi_row_content": false, "column_class": "diff_hash", "hash_fields": [{"field_name": "JSON_TEXT", "prio_in_diff_hash": 0, "prio_for_row_order": 50000, "field_target_table": "rsap_credit_memo_line_rin1_rin2_j1_l10_sat", "field_target_column": "JSON_TEXT"}], "column_type": "CHAR(28)", "hash_encoding": "BASE64", "hash_function": "sha-1", "hash_concatenation_seperator": "|", "hash_timestamp_format_sqlstyle": "YYYY-MM-DD HH24:MI:SS.US", "hash_null_value_string": "", "model_profile_name": "_default", "related_key_hash": "KEY_OF_RSAP_CREDIT_MEMO_LINE_RIN1_RIN2_DLNK", "hash_name": "DIFF_OF_RSAP_CREDIT_MEMO_LINE_RIN1_RIN2_J1_L10_SAT"}], "load_operations": [{"table_name": "rsap_credit_memo_line_rin1_hub", "relation_name": "/", "operation_origin": "field mapping relation", "hash_mappings": [{"hash_class": "key", "column_name": "HK_RSAP_CREDIT_MEMO_LINE_RIN1", "hash_name": "KEY_OF_RSAP_CREDIT_MEMO_LINE_RIN1_HUB", "stage_column_name": "HK_RSAP_CREDIT_MEMO_LINE_RIN1"}], "data_mapping": [{"column_name": "COMPANY", "field_name": "COMPANY", "column_class": "business_key", "stage_column_name": "COMPANY"}, {"column_name": "DOCENTRY", "field_name": "DOCENTRY", "column_class": "business_key", "stage_column_name": "DOCENTRY"}, {"column_name": "LINENUM", "field_name": "LINENUM", "column_class": "business_key", "stage_column_name": "LINENUM"}]}, {"table_name": "rsap_credit_memo_line_rin1_rin2_dlnk", "relation_name": "/", "operation_origin": "field mapping relation", "hash_mappings": [{"hash_class": "parent_key_1", "column_name": "HK_RSAP_CREDIT_MEMO_LINE_RIN1", "hash_name": "KEY_OF_RSAP_CREDIT_MEMO_LINE_RIN1_HUB", "stage_column_name": "HK_RSAP_CREDIT_MEMO_LINE_RIN1"}, {"hash_class": "key", "column_name": "LK_RSAP_CREDIT_MEMO_LINE_RIN1_RIN2", "hash_name": "KEY_OF_RSAP_CREDIT_MEMO_LINE_RIN1_RIN2_DLNK", "stage_column_name": "LK_RSAP_CREDIT_MEMO_LINE_RIN1_RIN2"}], "data_mapping": [{"column_name": "GROUPNUM", "field_name": "GROUPNUM", "column_class": "dependent_child_key", "stage_column_name": "GROUPNUM"}]}, {"table_name": "rsap_credit_memo_line_rin1_rin2_j1_l10_sat", "relation_name": "/", "operation_origin": "field mapping relation", "hash_mappings": [{"hash_class": "parent_key", "column_name": "LK_RSAP_CREDIT_MEMO_LINE_RIN1_RIN2", "hash_name": "KEY_OF_RSAP_CREDIT_MEMO_LINE_RIN1_RIN2_DLNK", "stage_column_name": "LK_RSAP_CREDIT_MEMO_LINE_RIN1_RIN2"}, {"hash_class": "diff_hash", "column_name": "RH_RSAP_CREDIT_MEMO_LINE_RIN1_RIN2_J1_L10_SAT", "hash_name": "DIFF_OF_RSAP_CREDIT_MEMO_LINE_RIN1_RIN2_J1_L10_SAT", "stage_column_name": "RH_RSAP_CREDIT_MEMO_LINE_RIN1_RIN2_J1_L10_SAT"}], "data_mapping": [{"column_name": "JSON_TEXT", "field_name": "JSON_TEXT", "column_class": "content", "stage_column_name": "JSON_TEXT"}]}], "stage_columns": [{"stage_column_name": "MD_INSERTED_AT", "is_nullable": false, "stage_column_class": "meta_load_date", "column_type": "TIMESTAMP"}, {"stage_column_name": "MD_RUN_ID", "is_nullable": false, "stage_column_class": "meta_load_process_id", "column_type": "INT"}, {"stage_column_name": "MD_RECORD_SOURCE", "is_nullable": false, "stage_column_class": "meta_record_source", "column_type": "VARCHAR(255)"}, {"stage_column_name": "MD_IS_DELETED", "is_nullable": false, "stage_column_class": "meta_deletion_flag", "column_type": "BOOLEAN"}, {"stage_column_name": "HK_RSAP_CREDIT_MEMO_LINE_RIN1", "stage_column_class": "hash", "is_nullable": false, "hash_name": "KEY_OF_RSAP_CREDIT_MEMO_LINE_RIN1_HUB", "column_type": "CHAR(28)"}, {"stage_column_name": "LK_RSAP_CREDIT_MEMO_LINE_RIN1_RIN2", "stage_column_class": "hash", "is_nullable": false, "hash_name": "KEY_OF_RSAP_CREDIT_MEMO_LINE_RIN1_RIN2_DLNK", "column_type": "CHAR(28)"}, {"stage_column_name": "RH_RSAP_CREDIT_MEMO_LINE_RIN1_RIN2_J1_L10_SAT", "stage_column_class": "hash", "is_nullable": false, "hash_name": "DIFF_OF_RSAP_CREDIT_MEMO_LINE_RIN1_RIN2_J1_L10_SAT", "column_type": "CHAR(28)"}, {"stage_column_name": "COMPANY", "stage_column_class": "data", "field_name": "COMPANY", "is_nullable": true, "column_type": "<PERSON><PERSON><PERSON><PERSON>(50)", "column_classes": ["business_key"]}, {"stage_column_name": "DOCENTRY", "stage_column_class": "data", "field_name": "DOCENTRY", "is_nullable": true, "column_type": "INTEGER", "column_classes": ["business_key"]}, {"stage_column_name": "LINENUM", "stage_column_class": "data", "field_name": "LINENUM", "is_nullable": true, "column_type": "INTEGER", "column_classes": ["business_key"]}, {"stage_column_name": "GROUPNUM", "stage_column_class": "data", "field_name": "GROUPNUM", "is_nullable": true, "column_type": "INTEGER", "column_classes": ["dependent_child_key"]}, {"stage_column_name": "JSON_TEXT", "stage_column_class": "data", "field_name": "JSON_TEXT", "is_nullable": true, "column_type": "VARCHAR", "column_classes": ["content"]}]}]}