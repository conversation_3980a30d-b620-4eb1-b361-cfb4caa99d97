Data vault pipeline developer cheat sheet 
rendered from  bgnr_kalender_p1.dvpi

pipeline name:  bgnr_kalender_p1

------------------------------------------------------
record source:  bgnr_kalender_p1

Source fields:
       COMPANY      Varchar(50)
       TAGESDATUM   DATE
       IST_WERKTAG  BOOLEAN
       RMRKS        varchar(2000)


------------------------------------------------------
Table List:
stage_table.bvlt_general.bgnr_kalender_p1_stage
table.rvlt_general.rgnr_gesellschaft_hub
table.rvlt_general.rgnr_gesellschaft_kalender_dlnk
table.bvlt_general.bgnr_gesellschaft_kalender_p1_b10_sat

------------------------------------------------------
stage table:  stage_bvlt.bgnr_kalender_p1_stage
Field to Stage mapping:
	--business keys,
		COMPANY      >  COMPANY,
		TAGESDATUM   >  TAGESDATUM,

	--content,
		IST_WERKTAG  >  IST_WERKTAG,
		RMRKS        >  RMRKS

------------------------------------------------------
Hash value composition

HK_RGNR_GESELLSCHAFT (key)
		COMPANY 

LK_RGNR_GESELLSCHAFT_KALENDER (key)
		TAGESDATUM 
		COMPANY 

RH_BGNR_GESELLSCHAFT_KALENDER_P1_B10_SAT (diff_hash)
		RMRKS 
		IST_WERKTAG 


------------------------------------------------------
Table load method
(STAGE >  VAULT)

rgnr_gesellschaft_hub (/) can be loaded by convention
		  key: HK_RGNR_GESELLSCHAFT  >  HK_RGNR_GESELLSCHAFT
		  business_key: COMPANY  >  COMPANY 

rgnr_gesellschaft_kalender_dlnk (/) can be loaded by convention
		  parent_key_1: HK_RGNR_GESELLSCHAFT  >  HK_RGNR_GESELLSCHAFT
		  key: LK_RGNR_GESELLSCHAFT_KALENDER  >  LK_RGNR_GESELLSCHAFT_KALENDER
		  dependent_child_key: TAGESDATUM  >  TAGESDATUM 

bgnr_gesellschaft_kalender_p1_b10_sat (/) needs explicit loading:
		  parent_key: LK_RGNR_GESELLSCHAFT_KALENDER  >  LK_RGNR_GESELLSCHAFT_KALENDER
		  diff_hash: RH_BGNR_GESELLSCHAFT_KALENDER_P1_B10_SAT  >  RH_BGNR_GESELLSCHAFT_KALENDER_P1_B10_SAT
		  content: IST_WERKTAG  >  IST_WERKTAG 
		* content: RMRKS  >  BEMERKUNG 

