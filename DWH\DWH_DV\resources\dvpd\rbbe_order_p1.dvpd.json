{
	/* ============================================================
	 * DVPD配置文件：BillBee订单数据处理管道
	 * 文件名：rbbe_order_p1.dvpd.json
	 * 描述：此配置文件定义了从BillBee电子商务平台提取订单数据并加载到Data Vault模型的ETL流程
	 * 相关文件：
	 *   - rbbe_order_orderitem_p1.dvpd.json (订单项处理)
	 *   - processes/rbbe_order_p1/__main__.py (处理主程序)
	 *   - processes/rbbe_order_p1/fetch_billbee_data.py (数据获取模块)
	 *   - processes/rbbe_order_p1/load_vault_order_1.py (订单数据加载模块)
	 * ============================================================ */

	/* DVPD版本号，用于确保配置文件与处理框架的兼容性 */
	"dvpd_version": "0.6.2",

	/* 定义数据加载的暂存区属性，包括模式名和表名 */
	/* 此处指定了专用的暂存表，用于临时存储从BillBee API获取的订单数据 */
	/* 暂存表作为源系统与Data Vault模型之间的缓冲区，便于数据验证和转换 */
	"stage_properties" : [{"stage_schema":"stage_rvlt","stage_table_name":"rbbe_ORDER_P1_stage"}],

	/* 定义数据管道名称，用于BillBee订单数据处理 */
	/* 此管道专门处理BillBee平台的订单数据，是电子商务数据集成的核心组件 */
	/* 命名约定：rbbe前缀表示BillBee系统，ORDER表示订单实体，P1表示第一个处理管道 */
	"pipeline_name": "rbbe_ORDER_P1",

	/* 定义数据来源标识，表明这是来自BillBee平台的订单数据 */
	/* 此标识符将被记录在所有目标表的RECORD_SOURCE列中，用于数据溯源 */
	"record_source_name_expression": "billbee.order",

	/* 数据提取配置，定义如何从BillBee API获取和解析数据 */
	/* 与其他DVPD不同，此处配置了完整的ETL流程，包括获取、解析和加载模块 */
	/* 这是一个实时API集成示例，而非仅用于DDL生成的配置 */
	"data_extraction": {
		/* 指定使用BillBee API模块获取数据，此模块在fetch_billbee_data.py中实现 */
		"fetch_module_name":"billbee api",

		/* 指定使用JSON数组解析模块处理返回的数据 */
		/* BillBee API返回JSON格式的订单数据，需要特定解析器处理 */
		"parse_module_name":"json_array",

		/* 指定使用Python框架加载模块将数据加载到暂存区 */
		/* 此模块负责数据类型转换和初步验证 */
		"load_module_name":"python_framework",

		/* 指定JSON响应中订单数据的路径，用于提取订单数组 */
		/* BillBee API将实际数据嵌套在Data属性中 */
		"json_array_path":"$.Data"
	},

	/* ============================================================
	 * 字段映射部分：定义源系统字段如何映射到Data Vault模型中的表
	 * 每个字段定义包含：字段名称、JSON路径、数据类型和目标表映射
	 * ============================================================ */
	"fields": [
		      /* 订单ID字段，作为订单Hub表的主要业务键 */
		      {"field_name": "BillBeeOrderId","json_path":"BillBeeOrderId",
											"field_type": "NUMBER(36,0)",	"targets": [{"table_name": "RBBE_ORDER_HUB"}]},
		      /* 父订单ID字段，用于建立订单之间的父子关系 */
		      /* 通过relation_names属性标识这是父订单关系 */
		      {"field_name": "BillBeeParentOrderId","json_path":"BillBeeParentOrderId",
											"field_type": "NUMBER(36,0)",	"targets": [{"table_name": "RBBE_ORDER_HUB"
																						,"column_name":"BillBeeOrderId"
																						,"relation_names":["parent"]}]},
		      /* 客户ID字段，用于关联客户Hub表 */
		      /* 此字段建立了订单与客户之间的关系 */
		      {"field_name": "Customer_Id",	"json_path":"Customer_Id",
											"field_type": "NUMBER(36,0)", 	"json_path":"Customer.Id",
																			"targets": [{"table_name": "RBBE_CUSTOMER_HUB"
																						,"column_name": "ID"}]},

		 	  /* 商店ID字段，用于关联商店Hub表 */
		 	  /* 此字段建立了订单与销售商店之间的关系 */
		 	  {"field_name": "BillbeeShopId",	"json_path":"Seller.BillbeeShopId",
											"field_type": "NUMBER(36,0)",	"targets": [{"table_name": "RBBE_SHOP_HUB"}]},


		 	  /* 以下字段映射到订单基本信息卫星表(P1) */
		 	  /* 订单编号，客户可见的订单标识符 */
		 	  {"field_name": "OrderNumber",	"json_path":"OrderNumber",
											"field_type": "VARCHAR(200)",	"targets": [{"table_name": "RBBE_ORDER_ORDER_P1_L10_SAT"}]},
		 	  /* 订单总成本 */
		 	  {"field_name": "TotalCost",	"json_path":"TotalCost",
											"field_type": "NUMBER(20,2)",	"targets": [{"table_name": "RBBE_ORDER_ORDER_P1_L10_SAT"}]},
		 	  /* 订单调整成本 */
		 	  {"field_name": "AdjustmentCost","json_path":"AdjustmentCost",
											"field_type": "NUMBER(20,2)",	"targets": [{"table_name": "RBBE_ORDER_ORDER_P1_L10_SAT"}]},
		 	  /* 订单货币 */
		 	  {"field_name": "Currency",	"json_path":"Currency",
											"field_type": "VARCHAR(200)",	"targets": [{"table_name": "RBBE_ORDER_ORDER_P1_L10_SAT"}]},
		 	  /* 订单运费 */
		 	  {"field_name": "ShippingCost",	"json_path":"ShippingCost",
											"field_type": "NUMBER(20,2)",	"targets": [{"table_name": "RBBE_ORDER_ORDER_P1_L10_SAT"}]},
		 	  /* 发票地址国家代码 */
		 	  {"field_name": "InvoiceAddress_countryISO2",	"json_path":"InvoiceAddress.CountryISO2",
											"field_type": "VARCHAR(20)",		"targets": [{"table_name": "RBBE_ORDER_ORDER_P1_L10_SAT"}]},

		 	  /* 以下字段映射到订单状态信息卫星表(P2) */
		 	  /* 订单状态代码 */
		 	  {"field_name": "State",		"json_path":"State",
											"field_type": "NUMBER(36,0)",	"targets": [{"table_name": "RBBE_ORDER_ORDER_P2_L10_SAT"}]},
		 	  /* 订单创建时间 */
		 	  {"field_name": "CreatedAt",	"json_path":"CreatedAt",
											"field_type": "TIMESTAMP",		"targets": [{"table_name": "RBBE_ORDER_ORDER_P2_L10_SAT"}]},
		 	  /* 订单发货时间 */
			  {"field_name": "ShippedAt",	"json_path":"ShippedAt",
											"field_type": "TIMESTAMP",		"targets": [{"table_name": "RBBE_ORDER_ORDER_P2_L10_SAT"}]},
		 	  /* 订单确认时间 */
		 	  {"field_name": "ConfirmedAt",	"json_path":"ConfirmedAt",
											"field_type": "TIMESTAMP",		"targets": [{"table_name": "RBBE_ORDER_ORDER_P2_L10_SAT"}]},
		 	  /* 发票日期 */
		 	  {"field_name": "InvoiceDate",	"json_path":"InvoiceDate",
											"field_type": "TIMESTAMP",		"targets": [{"table_name": "RBBE_ORDER_ORDER_P2_L10_SAT"}]},
		 	  /* 订单支付时间 */
		 	  {"field_name": "PayedAt",		"json_path":"PayedAt",
											"field_type": "TIMESTAMP",		"targets": [{"table_name": "RBBE_ORDER_ORDER_P2_L10_SAT"}]},
											
		 	  /* 以下是元数据字段，记录在多个卫星表中但排除在变更检测之外 */
		 	  /* 订单更新时间 - 用于审计跟踪，不触发变更检测 */
		 	  {"field_name": "UpdatedAt",	"json_path":"UpdatedAt",
											"field_type": "TIMESTAMP",		"targets": [{"table_name": "RBBE_ORDER_ORDER_P1_L10_SAT"
																						,"exclude_from_change_detection":"true"},
																						{"table_name": "RBBE_ORDER_ORDER_P2_L10_SAT"
																						,"exclude_from_change_detection":"true"}]},
		 	  /* 最后修改时间 - 用于审计跟踪，不触发变更检测 */
		 	  {"field_name": "LastModifiedAt","json_path":"LastModifiedAt",
											"field_type": "TIMESTAMP",		"targets": [{"table_name": "RBBE_ORDER_ORDER_P1_L10_SAT"
																						,"exclude_from_change_detection":"true"},
																						{"table_name": "RBBE_ORDER_ORDER_P2_L10_SAT"
																						,"exclude_from_change_detection":"true"}]},

		 	  /* 商店名称字段，映射到订单-商店关系的卫星表 */
		 	  {"field_name": "Seller_BillbeeShopName",	"json_path":"Seller.BillbeeShopName",
											"field_type": "VARCHAR(200)",	"targets": [{"table_name": "RBBE_ORDER_SHOP_P1_L10_SAT"}]}


			 ],

	/* ============================================================
	 * Data Vault模型定义部分
	 * 此部分定义了目标数据仓库中的表结构，包括Hub、Link和Satellite表
	 * 遵循Data Vault 2.0建模方法论，实现高度可扩展和灵活的数据模型
	 * ============================================================ */
	"data_vault_model": [
		{"schema_name": "rvlt_billbee", /* BillBee数据在Data Vault中的模式名 */
		 "tables": [
				/* ========== Hub表定义 ========== */
				/* 订单Hub表：存储订单业务键及其哈希值 */
				/* 作为订单实体的核心表，所有订单相关卫星表和链接表都引用此表 */
				{"table_name": "RBBE_ORDER_HUB",
				"table_stereotype": "hub",
				"hub_key_column_name": "HK_RBBE_ORDER"},

				/* ========== 订单相关卫星表 ========== */
				/* 订单基本信息卫星表：存储订单的基本属性，如订单号、金额、货币等 */
				/* P1表示第一组属性，L10表示第10级别的加载优先级 */
				{"table_name": "RBBE_ORDER_ORDER_P1_L10_SAT",	
				"table_stereotype": "sat",
				"satellite_parent_table": "RBBE_ORDER_HUB",
				"diff_hash_column_name": "RH_RBBE_ORDER_ORDER_P1_L10_SAT"},

				/* 订单状态信息卫星表：存储订单的状态相关属性，如创建时间、发货时间等 */
				/* 与P1卫星表分离，实现属性的逻辑分组，提高性能和可维护性 */
				{"table_name": "RBBE_ORDER_ORDER_P2_L10_SAT",	
				"table_stereotype": "sat",
				"satellite_parent_table": "RBBE_ORDER_HUB",
				"diff_hash_column_name": "RH_RBBE_ORDER_ORDER_P2_L10_SAT"},

				/* ========== 客户相关表 ========== */
				/* 客户Hub表：存储客户业务键及其哈希值 */
				/* 此表可能被多个数据管道引用，实现客户信息的统一管理 */
				{"table_name": "RBBE_CUSTOMER_HUB",
				"table_stereotype": "hub",
				"hub_key_column_name": "HK_RBBE_CUSTOMER"},

				/* 订单-客户链接表：存储订单与客户之间的关系 */
				/* 实现多对多关系模型，支持历史关系变更跟踪 */
				{"table_name": "RBBE_ORDER_CUSTOMER_LNK",		
				"table_stereotype": "lnk",
				"link_key_column_name": "LK_RBBE_ORDER_CUSTOMER",
				"link_parent_tables": ["RBBE_ORDER_HUB","RBBE_CUSTOMER_HUB"]},

				/* 订单-客户关系事件卫星表：跟踪订单与客户关系的生命周期 */
				/* ESAT表示事件卫星表，用于记录关系的有效性 */
				/* driving_keys指定订单作为驱动键，表示订单变更会触发关系更新 */
				{"table_name": "RBBE_ORDER_CUSTOMER_ESAT",		
				"table_stereotype": "sat",
				"satellite_parent_table": "RBBE_ORDER_CUSTOMER_LNK",
				"tracked_relation_name":"/",
				"driving_keys": ["HK_RBBE_ORDER"]},

				/* ========== 商店相关表 ========== */
				/* 商店Hub表：存储商店业务键及其哈希值 */
				/* 表示销售订单的商店实体 */
				{"table_name": "RBBE_SHOP_HUB",
				"table_stereotype": "hub",
				"hub_key_column_name": "HK_RBBE_SHOP"},

				/* 订单-商店链接表：存储订单与商店之间的关系 */
				/* 实现订单与销售商店的关联 */
				{"table_name": "RBBE_ORDER_SHOP_LNK",		
				"table_stereotype": "lnk",
				"link_key_column_name": "LK_RBBE_ORDER_SHOP",
				"link_parent_tables": ["RBBE_ORDER_HUB","RBBE_SHOP_HUB"]},

				/* 订单-商店关系卫星表：存储订单与商店关系的属性 */
				/* 如商店名称等附加信息 */
				{"table_name": "RBBE_ORDER_SHOP_P1_L10_SAT",
				"table_stereotype": "sat",
				"satellite_parent_table": "RBBE_ORDER_SHOP_LNK",
				"driving_keys": ["HK_RBBE_ORDER"]},
				
				/* ========== 订单父子关系表 ========== */
				/* 订单-父订单链接表：存储订单之间的父子关系 */
				/* 特殊的自引用关系，同一个Hub表在链接中出现两次，通过relation_name区分角色 */
				{"table_name": "RBBE_ORDER_PARENT_ORDER_LNK",		
				"table_stereotype": "lnk",
				"link_key_column_name": "LK_RBBE_ORDER_PARENT_ORDER",
				"link_parent_tables": ["RBBE_ORDER_HUB"
									,{"table_name":"RBBE_ORDER_HUB","relation_name":"parent"}]},

				/* 订单-父订单关系事件卫星表：跟踪订单父子关系的生命周期 */
				/* 支持订单层次结构的历史变更跟踪 */
				{"table_name": "RBBE_ORDER_PARENT_ORDER_ESAT",		
				"table_stereotype": "sat",
				"satellite_parent_table": "RBBE_ORDER_PARENT_ORDER_LNK",
				"driving_keys": ["HK_RBBE_ORDER"]}

				]
		}
	]
}