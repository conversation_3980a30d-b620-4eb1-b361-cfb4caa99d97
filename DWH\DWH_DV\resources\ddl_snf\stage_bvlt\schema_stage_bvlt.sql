/*
 * Schema: stage_bvlt
 * 
 * 描述：
 * 此模式包含业务数据仓库的暂存区表（Staging Tables）。
 * 它是数据从原始数据仓库(Raw Vault)到业务数据仓库(Business Vault)的中间处理区域。
 * 这些表用于存储经过转换和业务规则处理的数据，为后续加载到业务数据仓库做准备。
 * 
 * 包含的表类型：
 * - 业务暂存表：按业务领域和数据类型组织
 * - 每个暂存表通常对应一个业务处理过程
 * - 表名通常以业务领域前缀命名
 */

-- DROP SCHEMA stage_bvlt;

CREATE SCHEMA if not EXISTS stage_bvlt;

COMMENT ON SCHEMA stage_bvlt
  IS 'Contains staging tables for business vault processing, transforming raw data into business context';

GRANT USAGE ON SCHEMA stage_bvlt to DV_D_ACCESS_WRITE;