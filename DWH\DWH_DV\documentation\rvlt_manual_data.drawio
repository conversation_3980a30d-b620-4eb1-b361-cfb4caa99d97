<mxfile host="Electron" agent="Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) draw.io/24.7.17 Chrome/128.0.6613.36 Electron/32.0.1 Safari/537.36" version="24.7.17">
  <diagram name="Seite-1" id="AUpRBTa56OUczgRDtuuW">
    <mxGraphModel dx="1150" dy="711" grid="1" gridSize="10" guides="1" tooltips="1" connect="1" arrows="1" fold="1" page="1" pageScale="1" pageWidth="1169" pageHeight="827" math="0" shadow="0">
      <root>
        <mxCell id="0" />
        <mxCell id="0MrAooonQZi-3dDYej8w-10" value="Schema" style="" parent="0" />
        <mxCell id="0MrAooonQZi-3dDYej8w-12" value="rvlt_general (rgnr)" style="rounded=0;whiteSpace=wrap;html=1;align=left;verticalAlign=top;fillColor=none;" parent="0MrAooonQZi-3dDYej8w-10" vertex="1">
          <mxGeometry x="30" y="130" width="1120" height="110" as="geometry" />
        </mxCell>
        <mxCell id="0MrAooonQZi-3dDYej8w-13" value="rvlt_manual_data (rmud)" style="rounded=0;whiteSpace=wrap;html=1;align=left;verticalAlign=top;fillColor=none;" parent="0MrAooonQZi-3dDYej8w-10" vertex="1">
          <mxGeometry x="30" y="260" width="1120" height="320" as="geometry" />
        </mxCell>
        <mxCell id="0MrAooonQZi-3dDYej8w-26" value="bvlt_general (bgnr)" style="rounded=0;whiteSpace=wrap;strokeColor=none;strokeWidth=2;fillColor=#E6E6E6;fontFamily=Helvetica;fontSize=11;fontColor=default;labelBackgroundColor=none;verticalAlign=bottom;align=left;" parent="0MrAooonQZi-3dDYej8w-10" vertex="1">
          <mxGeometry x="180" y="600" width="930" height="210" as="geometry" />
        </mxCell>
        <mxCell id="YhLwq31L8ulUJbl3qCQ0-1" value="&lt;div&gt;&lt;div style=&quot;text-wrap: nowrap;&quot;&gt;rgnr_gesellschaft&lt;/div&gt;&lt;span style=&quot;text-wrap: nowrap; background-color: initial;&quot;&gt;_jahresplanzahl&lt;/span&gt;_&lt;span style=&quot;background-color: initial;&quot;&gt;p1_l20&lt;/span&gt;&lt;/div&gt;" style="rounded=1;whiteSpace=wrap;html=1;strokeColor=#36393d;fillColor=#FFCF5E;strokeWidth=2;align=center;verticalAlign=middle;fontFamily=Helvetica;fontSize=12;fontColor=default;dashed=1;" parent="0MrAooonQZi-3dDYej8w-10" vertex="1">
          <mxGeometry x="230" y="375" width="160" height="50" as="geometry" />
        </mxCell>
        <mxCell id="YhLwq31L8ulUJbl3qCQ0-4" style="edgeStyle=orthogonalEdgeStyle;shape=connector;rounded=1;jumpStyle=none;orthogonalLoop=1;jettySize=auto;html=1;opacity=40;strokeColor=default;strokeWidth=2;align=center;verticalAlign=middle;fontFamily=Helvetica;fontSize=11;fontColor=default;labelBackgroundColor=default;endArrow=none;endFill=0;exitX=1;exitY=0.5;exitDx=0;exitDy=0;" parent="0MrAooonQZi-3dDYej8w-10" source="YhLwq31L8ulUJbl3qCQ0-1" edge="1">
          <mxGeometry relative="1" as="geometry">
            <mxPoint x="460" y="325" as="targetPoint" />
            <mxPoint x="400" y="355" as="sourcePoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="YhLwq31L8ulUJbl3qCQ0-5" value="&lt;div&gt;&lt;div style=&quot;text-wrap: nowrap;&quot;&gt;rgnr_gesellschaft&lt;/div&gt;&lt;span style=&quot;text-wrap: nowrap; background-color: initial;&quot;&gt;_monatsplanzahl_&lt;/span&gt;&lt;span style=&quot;background-color: initial;&quot;&gt;p1_l20&lt;/span&gt;&lt;/div&gt;" style="rounded=1;whiteSpace=wrap;html=1;strokeColor=#36393d;fillColor=#FFCF5E;strokeWidth=2;align=center;verticalAlign=middle;fontFamily=Helvetica;fontSize=12;fontColor=default;dashed=1;" parent="0MrAooonQZi-3dDYej8w-10" vertex="1">
          <mxGeometry x="230" y="510" width="160" height="50" as="geometry" />
        </mxCell>
        <mxCell id="YhLwq31L8ulUJbl3qCQ0-8" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=1;orthogonalLoop=1;jettySize=auto;html=1;endArrow=classicThin;endFill=1;jumpStyle=none;fontColor=#C2C2C2;strokeWidth=2;strokeColor=#969696;curved=1;labelBackgroundColor=default;fontFamily=Helvetica;fontSize=11;shape=connector;dashed=1;dashPattern=1 1;opacity=40;align=center;verticalAlign=middle;entryX=0.598;entryY=0.057;entryDx=0;entryDy=0;exitX=0;exitY=0.5;exitDx=0;exitDy=0;entryPerimeter=0;" parent="0MrAooonQZi-3dDYej8w-10" source="YhLwq31L8ulUJbl3qCQ0-5" target="-zTepOR5F1LtlWgNjQnl-7" edge="1">
          <mxGeometry relative="1" as="geometry">
            <mxPoint x="240" y="398" as="sourcePoint" />
            <mxPoint x="600" y="620" as="targetPoint" />
            <Array as="points">
              <mxPoint x="220" y="535" />
              <mxPoint x="220" y="600" />
              <mxPoint x="600" y="600" />
              <mxPoint x="600" y="613" />
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="0MrAooonQZi-3dDYej8w-11" value="Quelle" parent="0" />
        <mxCell id="0MrAooonQZi-3dDYej8w-24" value="Planzahlen_pro_jahr.xls" style="rounded=0;whiteSpace=wrap;html=1;strokeWidth=2;dashed=1;strokeColor=#B3B3B3;fillColor=none;verticalAlign=top;align=left;fontColor=#B3B3B3;fontFamily=Helvetica;fontSize=11;labelBackgroundColor=default;" parent="0MrAooonQZi-3dDYej8w-11" vertex="1">
          <mxGeometry x="50" y="310" width="360" height="120" as="geometry" />
        </mxCell>
        <mxCell id="1" value="Modell" parent="0" />
        <mxCell id="0MrAooonQZi-3dDYej8w-2" value="gesellschaft&#xa;(company)" style="strokeWidth=2;shape=mxgraph.flowchart.start_1;whiteSpace=wrap;strokeColor=none;fontColor=#ffffff;fillColor=#002C82;fontFamily=Helvetica;fontSize=11;" parent="1" vertex="1">
          <mxGeometry x="720" y="160" width="110" height="50" as="geometry" />
        </mxCell>
        <mxCell id="0MrAooonQZi-3dDYej8w-4" value="&lt;div&gt;rgnr_gesellschaft&lt;/div&gt;&lt;div&gt;_jahresplanzahl&lt;/div&gt;&lt;div&gt;(jahr)&lt;/div&gt;" style="verticalLabelPosition=middle;verticalAlign=middle;html=1;shape=hexagon;perimeter=hexagonPerimeter2;arcSize=6;size=0.16666666666666666;fillColor=#cce5ff;strokeColor=#36393d;strokeWidth=2;labelPosition=center;align=center;" parent="1" vertex="1">
          <mxGeometry x="450" y="285" width="160" height="60" as="geometry" />
        </mxCell>
        <mxCell id="0MrAooonQZi-3dDYej8w-5" value="&lt;div&gt;rgnr_gesellschaft_&lt;/div&gt;&lt;div&gt;monatsplanzahl&lt;/div&gt;&lt;div&gt;(jahr,monat)&lt;/div&gt;" style="verticalLabelPosition=middle;verticalAlign=middle;html=1;shape=hexagon;perimeter=hexagonPerimeter2;arcSize=6;size=0.16666666666666666;fillColor=#cce5ff;strokeColor=#36393d;strokeWidth=2;labelPosition=center;align=center;" parent="1" vertex="1">
          <mxGeometry x="450" y="400" width="160" height="60" as="geometry" />
        </mxCell>
        <mxCell id="0MrAooonQZi-3dDYej8w-19" style="edgeStyle=orthogonalEdgeStyle;shape=connector;rounded=1;jumpStyle=none;orthogonalLoop=1;jettySize=auto;html=1;entryX=0.5;entryY=1;entryDx=0;entryDy=0;opacity=40;strokeColor=default;strokeWidth=2;align=center;verticalAlign=middle;fontFamily=Helvetica;fontSize=11;fontColor=default;labelBackgroundColor=default;endArrow=none;endFill=0;" parent="1" source="0MrAooonQZi-3dDYej8w-6" target="0MrAooonQZi-3dDYej8w-5" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="0MrAooonQZi-3dDYej8w-6" value="&lt;div&gt;&lt;div style=&quot;text-wrap: nowrap;&quot;&gt;rgnr_gesellschaft&lt;/div&gt;&lt;span style=&quot;text-wrap: nowrap; background-color: initial;&quot;&gt;_monatsplanzahl_&lt;/span&gt;&lt;span style=&quot;background-color: initial;&quot;&gt;p1_l10&lt;/span&gt;&lt;/div&gt;" style="rounded=1;whiteSpace=wrap;html=1;strokeColor=#36393d;fillColor=#FFCF5E;strokeWidth=2;align=center;verticalAlign=middle;fontFamily=Helvetica;fontSize=12;fontColor=default;" parent="1" vertex="1">
          <mxGeometry x="230" y="450" width="160" height="50" as="geometry" />
        </mxCell>
        <mxCell id="0MrAooonQZi-3dDYej8w-20" style="edgeStyle=orthogonalEdgeStyle;shape=connector;rounded=1;jumpStyle=none;orthogonalLoop=1;jettySize=auto;html=1;opacity=40;strokeColor=default;strokeWidth=2;align=center;verticalAlign=middle;fontFamily=Helvetica;fontSize=11;fontColor=default;labelBackgroundColor=default;endArrow=none;endFill=0;" parent="1" source="0MrAooonQZi-3dDYej8w-9" target="0MrAooonQZi-3dDYej8w-4" edge="1">
          <mxGeometry relative="1" as="geometry">
            <mxPoint x="530" y="350" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="0MrAooonQZi-3dDYej8w-9" value="&lt;div&gt;&lt;div style=&quot;text-wrap: nowrap;&quot;&gt;rgnr_gesellschaft&lt;/div&gt;&lt;span style=&quot;text-wrap: nowrap; background-color: initial;&quot;&gt;_jahresplanzahl&lt;/span&gt;_&lt;span style=&quot;background-color: initial;&quot;&gt;p1_l10&lt;/span&gt;&lt;/div&gt;" style="rounded=1;whiteSpace=wrap;html=1;strokeColor=#36393d;fillColor=#FFCF5E;strokeWidth=2;align=center;verticalAlign=middle;fontFamily=Helvetica;fontSize=12;fontColor=default;" parent="1" vertex="1">
          <mxGeometry x="230" y="320" width="160" height="50" as="geometry" />
        </mxCell>
        <mxCell id="0MrAooonQZi-3dDYej8w-15" style="edgeStyle=orthogonalEdgeStyle;shape=connector;rounded=1;jumpStyle=none;orthogonalLoop=1;jettySize=auto;html=1;entryX=0;entryY=0.5;entryDx=0;entryDy=0;entryPerimeter=0;opacity=40;strokeColor=default;strokeWidth=2;align=center;verticalAlign=middle;fontFamily=Helvetica;fontSize=11;fontColor=default;labelBackgroundColor=default;endArrow=none;endFill=0;" parent="1" source="0MrAooonQZi-3dDYej8w-4" target="0MrAooonQZi-3dDYej8w-2" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="0MrAooonQZi-3dDYej8w-16" style="edgeStyle=orthogonalEdgeStyle;shape=connector;rounded=1;jumpStyle=none;orthogonalLoop=1;jettySize=auto;html=1;entryX=0;entryY=0.5;entryDx=0;entryDy=0;entryPerimeter=0;opacity=40;strokeColor=default;strokeWidth=2;align=center;verticalAlign=middle;fontFamily=Helvetica;fontSize=11;fontColor=default;labelBackgroundColor=default;endArrow=none;endFill=0;" parent="1" source="0MrAooonQZi-3dDYej8w-5" target="0MrAooonQZi-3dDYej8w-2" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="0MrAooonQZi-3dDYej8w-43" style="edgeStyle=orthogonalEdgeStyle;shape=connector;rounded=1;jumpStyle=none;orthogonalLoop=1;jettySize=auto;html=1;opacity=40;strokeColor=default;strokeWidth=2;align=center;verticalAlign=middle;fontFamily=Helvetica;fontSize=11;fontColor=default;labelBackgroundColor=default;endArrow=none;endFill=0;" parent="1" source="0MrAooonQZi-3dDYej8w-44" target="0MrAooonQZi-3dDYej8w-45" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="0MrAooonQZi-3dDYej8w-44" value="gesellschaft_kalender&lt;div&gt;(tagesdatum)&lt;/div&gt;" style="verticalLabelPosition=middle;verticalAlign=middle;html=1;shape=hexagon;perimeter=hexagonPerimeter2;arcSize=6;size=0.16666666666666666;fillColor=#cce5ff;strokeColor=#36393d;strokeWidth=2;labelPosition=center;align=center;fontFamily=Helvetica;fontSize=11;fontColor=default;labelBackgroundColor=none;" parent="1" vertex="1">
          <mxGeometry x="860" y="160" width="160" height="50" as="geometry" />
        </mxCell>
        <mxCell id="0MrAooonQZi-3dDYej8w-45" value="gesellschaft_kalender_p1_b10" style="rounded=1;whiteSpace=wrap;html=1;strokeColor=#36393d;fillColor=#FFF3A8;strokeWidth=2;" parent="1" vertex="1">
          <mxGeometry x="880" y="620" width="180" height="30" as="geometry" />
        </mxCell>
        <mxCell id="0MrAooonQZi-3dDYej8w-46" style="edgeStyle=orthogonalEdgeStyle;shape=connector;rounded=1;jumpStyle=none;orthogonalLoop=1;jettySize=auto;html=1;opacity=40;strokeColor=default;strokeWidth=2;align=center;verticalAlign=middle;fontFamily=Helvetica;fontSize=11;fontColor=default;labelBackgroundColor=default;endArrow=none;endFill=0;" parent="1" source="0MrAooonQZi-3dDYej8w-2" target="0MrAooonQZi-3dDYej8w-44" edge="1">
          <mxGeometry relative="1" as="geometry">
            <mxPoint x="880" y="185" as="sourcePoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="-zTepOR5F1LtlWgNjQnl-5" value="&lt;div style=&quot;text-wrap: nowrap;&quot;&gt;gesellschaft&lt;span style=&quot;background-color: initial;&quot;&gt;_&lt;/span&gt;&lt;/div&gt;&lt;div style=&quot;text-wrap: nowrap;&quot;&gt;&lt;span style=&quot;background-color: initial;&quot;&gt;tagesplanzahl_&lt;/span&gt;&lt;/div&gt;&lt;div style=&quot;text-wrap: nowrap;&quot;&gt;&lt;span style=&quot;background-color: initial;&quot;&gt;_v1_b10&lt;/span&gt;&lt;/div&gt;" style="rounded=1;whiteSpace=wrap;html=1;strokeColor=#36393d;fillColor=#FFCF5E;strokeWidth=2;align=center;verticalAlign=middle;fontFamily=Helvetica;fontSize=12;fontColor=default;" parent="1" vertex="1">
          <mxGeometry x="580" y="750" width="160" height="50" as="geometry" />
        </mxCell>
        <mxCell id="-zTepOR5F1LtlWgNjQnl-6" style="edgeStyle=orthogonalEdgeStyle;shape=connector;rounded=1;jumpStyle=none;orthogonalLoop=1;jettySize=auto;html=1;entryX=0.5;entryY=1;entryDx=0;entryDy=0;opacity=40;strokeColor=default;strokeWidth=2;align=center;verticalAlign=middle;fontFamily=Helvetica;fontSize=11;fontColor=default;labelBackgroundColor=default;endArrow=none;endFill=0;exitX=1;exitY=0.5;exitDx=0;exitDy=0;" parent="1" source="-zTepOR5F1LtlWgNjQnl-5" target="0MrAooonQZi-3dDYej8w-44" edge="1">
          <mxGeometry relative="1" as="geometry">
            <Array as="points">
              <mxPoint x="825" y="775" />
              <mxPoint x="825" y="420" />
              <mxPoint x="940" y="420" />
            </Array>
            <mxPoint x="690" y="660" as="sourcePoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="0MrAooonQZi-3dDYej8w-30" value="datenfluss" parent="0" />
        <mxCell id="0MrAooonQZi-3dDYej8w-40" style="rounded=0;orthogonalLoop=1;jettySize=auto;html=1;endArrow=classicThin;endFill=1;exitX=0;exitY=0.5;exitDx=0;exitDy=0;jumpStyle=none;fontColor=#C2C2C2;strokeWidth=2;strokeColor=#969696;labelBackgroundColor=default;fontFamily=Helvetica;fontSize=11;shape=connector;dashed=1;dashPattern=1 1;opacity=40;align=center;verticalAlign=middle;entryX=1;entryY=0.5;entryDx=0;entryDy=0;curved=1;" parent="0MrAooonQZi-3dDYej8w-30" source="0MrAooonQZi-3dDYej8w-45" target="-zTepOR5F1LtlWgNjQnl-7" edge="1">
          <mxGeometry relative="1" as="geometry">
            <mxPoint x="880" y="725" as="sourcePoint" />
            <mxPoint x="920" y="840" as="targetPoint" />
            <Array as="points">
              <mxPoint x="750" y="620" />
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="-zTepOR5F1LtlWgNjQnl-10" style="edgeStyle=none;shape=connector;curved=1;rounded=0;jumpStyle=none;orthogonalLoop=1;jettySize=auto;html=1;dashed=1;dashPattern=1 1;opacity=40;strokeColor=#969696;strokeWidth=2;align=center;verticalAlign=middle;fontFamily=Helvetica;fontSize=11;fontColor=#C2C2C2;labelBackgroundColor=default;endArrow=classicThin;endFill=1;" parent="0MrAooonQZi-3dDYej8w-30" source="-zTepOR5F1LtlWgNjQnl-7" target="-zTepOR5F1LtlWgNjQnl-5" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="-zTepOR5F1LtlWgNjQnl-7" value="" style="shape=parallelogram;perimeter=parallelogramPerimeter;whiteSpace=wrap;html=1;fixedSize=1;fontColor=#C2C2C2;strokeColor=#000000;strokeWidth=1;fillColor=#E0E0E0;fontFamily=Helvetica;fontSize=11;labelBackgroundColor=default;" parent="0MrAooonQZi-3dDYej8w-30" vertex="1">
          <mxGeometry x="550" y="610" width="80" height="50" as="geometry" />
        </mxCell>
        <mxCell id="YhLwq31L8ulUJbl3qCQ0-6" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=1;orthogonalLoop=1;jettySize=auto;html=1;endArrow=classicThin;endFill=1;jumpStyle=none;fontColor=#C2C2C2;strokeWidth=2;strokeColor=#969696;curved=1;labelBackgroundColor=default;fontFamily=Helvetica;fontSize=11;shape=connector;dashed=1;dashPattern=1 1;opacity=40;align=center;verticalAlign=middle;exitX=0;exitY=0.25;exitDx=0;exitDy=0;entryX=0;entryY=0.5;entryDx=0;entryDy=0;" parent="0MrAooonQZi-3dDYej8w-30" source="YhLwq31L8ulUJbl3qCQ0-1" target="-zTepOR5F1LtlWgNjQnl-7" edge="1">
          <mxGeometry relative="1" as="geometry">
            <mxPoint x="230" y="388" as="sourcePoint" />
            <mxPoint x="590" y="610" as="targetPoint" />
            <Array as="points">
              <mxPoint x="210" y="388" />
              <mxPoint x="210" y="590" />
              <mxPoint x="490" y="590" />
              <mxPoint x="490" y="635" />
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="0MrAooonQZi-3dDYej8w-23" value="Planzahlen_pro_monat.xls" style="rounded=0;whiteSpace=wrap;html=1;strokeWidth=2;dashed=1;strokeColor=#B3B3B3;fillColor=none;verticalAlign=top;align=left;fontColor=#B3B3B3;fontFamily=Helvetica;fontSize=11;labelBackgroundColor=default;" parent="0MrAooonQZi-3dDYej8w-30" vertex="1">
          <mxGeometry x="50" y="440" width="360" height="130" as="geometry" />
        </mxCell>
      </root>
    </mxGraphModel>
  </diagram>
</mxfile>
