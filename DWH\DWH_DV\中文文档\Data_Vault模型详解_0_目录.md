# Data Vault模型详解：目录

## 文档概述

本系列文档旨在为没有IT背景的业务人员详细解释Data Vault模型、ETL流程以及DVPD/DVPI工具。我们以项目中的实际图示为例，深入浅出地解释每个组件和概念，帮助您理解数据仓库的结构和工作原理。

## 文档目录

### [1. 完整图示说明](Data_Vault模型详解_完整图示说明.md)

本文档提供对完整Data Vault模型图的详细解释，包括：
- [图中各组件的含义和作用](Data_Vault模型详解_完整图示说明.md#data-vault图示符号说明)
- [主要业务实体及其关系](Data_Vault模型详解_完整图示说明.md#图中主要组件详解)
- [数据结构的组织方式](Data_Vault模型详解_完整图示说明.md#命名约定解释)
- [虚拟数据示例](Data_Vault模型详解_完整图示说明.md#虚拟数据示例)

### [2. 符号和连接线说明](Data_Vault模型详解_符号和连接线说明.md)

本文档专门解释Data Vault模型图中使用的各种符号和连接线：
- [表类型符号（Hub、Link、Satellite等）](Data_Vault模型详解_符号和连接线说明.md#1-表类型符号)
- [连接线类型（实线、虚线等）](Data_Vault模型详解_符号和连接线说明.md#2-连接线类型)
- [分组和区域划分](Data_Vault模型详解_符号和连接线说明.md#图中的区域划分)
- [命名约定解释](Data_Vault模型详解_符号和连接线说明.md#命名约定解释)
- [如何阅读Data Vault模型图](Data_Vault模型详解_符号和连接线说明.md#如何阅读这种图表)

### [3. 数据流程和业务场景](Data_Vault模型详解_数据流程和业务场景.md)

本文档详细解释数据在Data Vault模型中的流动过程：
- [数据流程概述（从提取到集成）](Data_Vault模型详解_数据流程和业务场景.md#数据流程概述)
- [详细数据流程说明](Data_Vault模型详解_数据流程和业务场景.md#详细数据流程说明)
- 业务场景示例
  - [处理新的SAP发票](Data_Vault模型详解_数据流程和业务场景.md#场景1处理新的sap发票)
  - [分析特定客户的销售趋势](Data_Vault模型详解_数据流程和业务场景.md#场景2分析特定客户的销售趋势)
  - [产品盈利能力分析](Data_Vault模型详解_数据流程和业务场景.md#场景3产品盈利能力分析)

### [4. DVPD和DVPI说明](Data_Vault模型详解_DVPD和DVPI说明.md)

本文档解释DVPD（Data Vault Pipeline Definition）和DVPI（Data Vault Pipeline Implementation）的作用和工作原理：
- [DVPD的主要组成部分](Data_Vault模型详解_DVPD和DVPI说明.md#dvpd的主要组成部分)
- [DVPD示例解析](Data_Vault模型详解_DVPD和DVPI说明.md#dvpd示例解析)
- [DVPI的主要功能](Data_Vault模型详解_DVPD和DVPI说明.md#dvpi的主要功能)
- [DVPI生成的开发人员备忘单](Data_Vault模型详解_DVPD和DVPI说明.md#dvpi生成的开发人员备忘单)
- [DVPD和DVPI如何支持模型](Data_Vault模型详解_DVPD和DVPI说明.md#dvpd和dvpi如何支持图中的模型)
- [使用DVPD和DVPI的优势](Data_Vault模型详解_DVPD和DVPI说明.md#为什么使用dvpd和dvpi)

## 如何使用本文档

1. **初次接触Data Vault**：建议从[第1部分"完整图示说明"](Data_Vault模型详解_完整图示说明.md)开始，了解整体概念
2. **想了解图表符号**：查看[第2部分"符号和连接线说明"](Data_Vault模型详解_符号和连接线说明.md)
3. **关注数据如何流动**：阅读[第3部分"数据流程和业务场景"](Data_Vault模型详解_数据流程和业务场景.md)
4. **了解技术实现**：参考[第4部分"DVPD和DVPI说明"](Data_Vault模型详解_DVPD和DVPI说明.md)

每个文档都可以独立阅读，但它们共同构成了对Data Vault模型的全面解释。

## 相关资源

除了本系列文档外，您还可以参考以下资源：

1. **项目文档**：
   - [README.md](../README.md)：项目概述和基本信息
   - 各个管道的开发备忘单，例如：
     - [rbbe_order_p1.devsheet.txt](../resources/documentation/rbbe_order_p1.devsheet.txt)
     - [rsap_oinv_j1.devsheet.txt](../resources/documentation/rsap_oinv_j1.devsheet.txt)

2. **模型图表**：
   - 位于documentation文件夹中的.drawio和.png文件，例如：
     - [rvlt_sap.drawio.png](../documentation/rvlt_sap.drawio.png)：SAP原始层模型
     - [bvlt_sap_invoice_credit_memo.drawio.png](../documentation/bvlt_sap_invoice_credit_memo.drawio.png)：SAP发票业务层模型
     - [mart_umsatzreport.drawio.png](../documentation/mart_umsatzreport.drawio.png)：销售报告数据集市模型

3. **配置文件**：
   - 位于resources/dvpd文件夹中的DVPD文件，例如：
     - [rsap_inv1_j1.dvpd.json](../resources/dvpd/rsap_inv1_j1.dvpd.json)
     - [rbbe_order_p1.dvpd.json](../resources/dvpd/rbbe_order_p1.dvpd.json)

## 术语表

为了帮助您理解文档中使用的术语，我们提供了以下简要术语表：

- **Data Vault**：一种数据建模方法，将数据分解为Hub、Link和Satellite
- **Hub**：代表业务核心实体的表，如客户、产品、订单等
- **Link**：表示实体之间关系的表
- **Satellite**：存储与Hub或Link相关描述性信息的表，并跟踪历史变化
- **ETL**：提取(Extract)、转换(Transform)、加载(Load)的缩写，指数据处理流程
- **DVPD**：Data Vault Pipeline Definition，定义如何将源数据映射到Data Vault模型
- **DVPI**：Data Vault Pipeline Implementation，DVPD的实现细节
- **Raw Vault**：存储未经处理的原始数据的层
- **Business Vault**：应用业务规则和转换的层
- **Data Mart**：面向特定业务领域的数据视图层

更详细的术语解释请参考[非技术人员数据仓库指南_第5部分_术语表和常见问题.md](非技术人员数据仓库指南_第5部分_术语表和常见问题.md)。

## 图示示例

以下是本文档中引用的主要图示：

[![SAP原始层模型](../documentation/rvlt_sap.drawio.png)](../documentation/rvlt_sap.drawio.png)
*点击图片查看完整大图：SAP原始层数据模型*

[![SAP发票业务层模型](../documentation/bvlt_sap_invoice_credit_memo.drawio.png)](../documentation/bvlt_sap_invoice_credit_memo.drawio.png)
*点击图片查看完整大图：SAP发票业务层数据模型*

[![销售报告数据集市模型](../documentation/mart_umsatzreport.drawio.png)](../documentation/mart_umsatzreport.drawio.png)
*点击图片查看完整大图：销售报告数据集市模型*
