-- generated script for stage_rvlt.rbbe_ORDER_P1_stage

-- DROP TABLE stage_rvlt.rbbe_ORDER_P1_stage;

CREATE TABLE stage_rvlt.rbbe_ORDER_P1_stage (
--metadata,
MD_INSERTED_AT TIMESTAMP NOT NULL,
MD_RUN_ID INT NOT NULL,
MD_RECORD_SOURCE VARCHAR(255) NOT NULL,
MD_IS_DELETED BOOLEAN NOT NULL,
--hash keys,
HK_RBBE_CUSTOMER CHAR(28) NOT NULL,
HK_RBBE_ORDER CHAR(28) NOT NULL,
HK_RBBE_ORDER_PARENT CHAR(28) NOT NULL,
HK_RBBE_SHOP CHAR(28) NOT NULL,
LK_RBBE_ORDER_CUSTOMER CHAR(28) NOT NULL,
LK_RBBE_ORDER_PARENT_ORDER CHAR(28) NOT NULL,
LK_RBBE_ORDER_SHOP CHAR(28) NOT NULL,
RH_RBBE_ORDER_ORDER_P1_L10_SAT CHAR(28) NOT NULL,
RH_RBBE_ORDER_ORDER_P2_L10_SAT CHAR(28) NOT NULL,
RH_RBBE_ORDER_SHOP_P1_L10_SAT CHAR(28) NOT NULL,
--business keys,
BILLBEEORDERID NUMBER(36,0) NULL,
BILLBEEPARENTORDERID NUMBER(36,0) NULL,
BILLBEESHOPID NUMBER(36,0) NULL,
CUSTOMER_ID NUMBER(36,0) NULL,
--content untracked,
LASTMODIFIEDAT TIMESTAMP NULL,
UPDATEDAT TIMESTAMP NULL,
--content,
ADJUSTMENTCOST NUMBER(20,2) NULL,
CONFIRMEDAT TIMESTAMP NULL,
CREATEDAT TIMESTAMP NULL,
CURRENCY VARCHAR(200) NULL,
INVOICEADDRESS_COUNTRYISO2 VARCHAR(20) NULL,
INVOICEDATE TIMESTAMP NULL,
ORDERNUMBER VARCHAR(200) NULL,
PAYEDAT TIMESTAMP NULL,
SELLER_BILLBEESHOPNAME VARCHAR(200) NULL,
SHIPPEDAT TIMESTAMP NULL,
SHIPPINGCOST NUMBER(20,2) NULL,
STATE NUMBER(36,0) NULL,
TOTALCOST NUMBER(20,2) NULL
);
-- end of script --