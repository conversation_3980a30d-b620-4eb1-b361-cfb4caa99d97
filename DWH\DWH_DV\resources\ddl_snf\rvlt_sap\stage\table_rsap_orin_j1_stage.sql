-- generated script for stage_rvlt.rsap_orin_j1_stage

-- DROP TABLE stage_rvlt.rsap_orin_j1_stage;

CREATE TABLE stage_rvlt.rsap_orin_j1_stage (
--metadata,
MD_INSERTED_AT TIMESTAMP NOT NULL,
MD_RUN_ID INT NOT NULL,
MD_RECORD_SOURCE VARCHAR(255) NOT NULL,
MD_IS_DELETED BOOLEAN NOT NULL,
--hash keys,
HK_RSAP_BUSINESS_PARTNER_OCRD CHAR(28) NOT NULL,
HK_RSAP_CREDIT_MEMO_ORIN CHAR(28) NOT NULL,
HK_RSAP_SALES_EMPLOYEE_OSLP CHAR(28) NOT NULL,
LK_RSAP_CREDIT_MEMO_ORIN_BUSINESS_PARTNER_OCRD_SALES_EMPLOYEE_OSLP CHAR(28) NOT NULL,
RH_RSAP_CREDIT_MEMO_ORIN_J1_L10_SAT CHAR(28) NOT NULL,
--business keys,
CARDCODE VARCHAR(20) NULL,
CO<PERSON>ANY Varchar(50) NULL,
<PERSON><PERSON><PERSON><PERSON><PERSON> INTEGER NULL,
<PERSON>PCODE VARCHAR(20) NULL,
--content,
JSON_TEXT VARCHAR NULL
);
-- end of script --