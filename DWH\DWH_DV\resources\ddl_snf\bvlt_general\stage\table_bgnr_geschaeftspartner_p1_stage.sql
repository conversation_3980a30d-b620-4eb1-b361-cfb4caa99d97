-- generated script for stage_bvlt.bgnr_geschaeftspartner_p1_stage

-- DROP TABLE stage_bvlt.bgnr_geschaeftspartner_p1_stage;

CREATE TABLE stage_bvlt.bgnr_geschaeftspartner_p1_stage (
--metadata,
MD_INSERTED_AT TIMESTAMP NOT NULL,
MD_RUN_ID INT NOT NULL,
MD_RECORD_SOURCE VARCHAR(255) NOT NULL,
MD_IS_DELETED BOOLEAN NOT NULL,
--hash keys,
HK_BGNR_GESCHAEFTSPARTNER CHAR(28) NOT NULL,
HK_RBBE_SHOP CHAR(28) NOT NULL,
HK_RSAP_BUSINESS_PARTNER_OCRD CHAR(28) NOT NULL,
LK_BGNR_GESCHAEFTSPARTNER_RBBE_SHOP CHAR(28) NOT NULL,
LK_BGNR_GESCHAEFTSPARTNER_RSAP_BUSINESS_PARTNER CHAR(28) NOT NULL,
RH_BGNR_GESCHAEFTSPARTNER_P1_B10_SAT CHAR(28) NOT NULL,
--business keys,
<PERSON><PERSON><PERSON><PERSON><PERSON>HOPID NUMBER(36,0) NULL,
CARDCODE VARCHAR(255) NULL,
COMPANY VARCHAR(255) NULL,
XD_GESCHAEFTSPARTNER_ID VARCHAR(255) NULL,
--content,
GESELLSCHAFT VARCHAR(255) NULL,
KUNDENGRUPPE  VARCHAR(255) NULL,
NAME VARCHAR(255) NULL,
VERKAEUFERNUMMER  VARCHAR(255) NULL,
VERKAUFSGEBIET  VARCHAR(255) NULL
);
-- end of script --