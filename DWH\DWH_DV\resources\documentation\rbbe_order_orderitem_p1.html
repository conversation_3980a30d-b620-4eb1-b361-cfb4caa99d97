<!DOCTYPE html>
<html>
<head>
<style>
table, th, td {border: 1px solid;}
table {border-collapse: collapse;}
body { font-family: Verdana, Geneva, Tahoma, sans-serif;}</style>
   <title>rbbe_order_orderitem_p1</title>
</head>
<body>
<h1>Pipeline: rbbe_order_orderitem_p1
</h1><p>Record source: billbee.order.orderItem
</p><p>Json loop path: $.Data.[*].orderItems
</p>   <table>
       <tr>
           <th>field</th>
           <th>type</th>
           <th>mapping</th>
       </tr>
       <tr>
           <td>-1. Bill<PERSON>eeOrderId</td>
           <td>NUMBER(36,0)</td>
           <td>RBBE_ORDER_HUB.[BILLBEEORDERID]</td>
       </tr>
       <tr>
           <td>BillBeeId</td>
           <td>NUMBER(36,0)</td>
           <td>RBBE_ORDER_ITEM_HUB</td>
       </tr>
       <tr>
           <td>Product.BillbeeId</td>
           <td>NUMBER(36,0)</td>
           <td>RBBE_PRODUCT_HUB.[BillbeeId]</td>
       </tr>
       <tr>
           <td>Quantity</td>
           <td>NUMBER(20,2)</td>
           <td>RBBE_ORDER_ITEM_ORDER_P1_L10_SAT</td>
       </tr>
       <tr>
           <td>TotalPrice</td>
           <td>NUMBER(20,2)</td>
           <td>RBBE_ORDER_ITEM_ORDER_P1_L10_SAT</td>
       </tr>
       <tr>
           <td>TaxAmount</td>
           <td>NUMBER(20,2)</td>
           <td>RBBE_ORDER_ITEM_ORDER_P1_L10_SAT</td>
       </tr>
       <tr>
           <td>TaxIndex</td>
           <td>NUMBER(20,2)</td>
           <td>RBBE_ORDER_ITEM_ORDER_P1_L10_SAT</td>
       </tr>
       <tr>
           <td>Discount</td>
           <td>NUMBER(20,2)</td>
           <td>RBBE_ORDER_ITEM_ORDER_P1_L10_SAT</td>
       </tr>
       <tr>
           <td>UnrebatedTotalPrice</td>
           <td>NUMBER(20,2)</td>
           <td>RBBE_ORDER_ITEM_ORDER_P1_L10_SAT</td>
       </tr>
       <tr>
           <td>InvoiceSKU</td>
           <td>VARCHAR(200)</td>
           <td>RBBE_ORDER_ITEM_ORDER_P1_L10_SAT</td>
       </tr>
       <tr>
           <td>Product.Title</td>
           <td>VARCHAR(1000)</td>
           <td>RBBE_ORDER_ITEM_ORDER_P1_L10_SAT.[PRODUCT_TITLE]</td>
       </tr>
       <tr>
           <td>Product.SKU</td>
           <td>VARCHAR(200)</td>
           <td>RBBE_ORDER_ITEM_ORDER_P1_L10_SAT.[PRODUCT_SKU]</td>
       </tr>
       <tr>
           <td>Product.EAN</td>
           <td>VARCHAR(200)</td>
           <td>RBBE_ORDER_ITEM_ORDER_P1_L10_SAT.[PRODUCT_EAN]</td>
       </tr>
       <tr>
           <td>Product.CountryOfOrigin</td>
           <td>VARCHAR(200)</td>
           <td>RBBE_ORDER_ITEM_ORDER_P1_L10_SAT.[PRODUCT_COUNTRYOFORIGIN]</td>
       </tr>
       <tr>
           <td>Product.TARICCode</td>
           <td>VARCHAR(200)</td>
           <td>RBBE_ORDER_ITEM_ORDER_P1_L10_SAT.[PRODUCT_TARICCODE]</td>
       </tr>
       <tr>
           <td>-1. LastModifiedAt</td>
           <td>TIMESTAMP</td>
           <td>RBBE_ORDER_ITEM_ORDER_P1_L10_SAT.[LASTMODIFIEDAT] (not in comparison)</td>
       </tr>
   </table>
</body>
</html>