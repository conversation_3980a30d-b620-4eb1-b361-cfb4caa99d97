import json

from lib.json_utils import get_json_text_content_column, sort_json_content_column
from lib.blobstorage_utils import fetch_json_source_file_from_blob_container
from lib.cimtjobinstance import CimtJobInstance, cimtjobinstance_job
from lib.connection_snf import  connection_snf_for_dwh_connection_type

from lib.dvf_basics import dvf_assemble_datavault_hash, Dvf_dwh_connection_type
from lib.dvf_sqlByConvention_snf import dvf_get_datavault_hub_elt_sql, \
    dvf_execute_elt_statement_list, dvf_get_check_hash_collision_hub_elt_sql, dvf_get_check_singularity_sat_elt_sql, \
    dvf_get_datavault_lnk_elt_sql, dvf_get_check_hash_collision_lnk_elt_sql, dvf_get_datavault_esat_elt_sql,dvf_get_datavault_sat_elt_sql

from lib.dbutils_snf import get_snf_dict_insert_sql, execute_snf_dict_bulk_insert





@cimtjobinstance_job
def stage_data(data, parent_job_instance, dwh_connection, **kwargs):

    my_job_instance = CimtJobInstance(kwargs['instance_job_name'], parent_job_instance)
    my_job_instance.start_instance()

    try:
        # ### FRAMEWORK PHASE: do processing here

        # prepare stage insert operation
        dwh_cursor = dwh_connection.cursor()
        dwh_cursor.execute("TRUNCATE TABLE stage_rvlt.rbbe_order_p1_stage")
        insert_statement = get_snf_dict_insert_sql(dwh_connection, "stage_rvlt", "rbbe_order_p1_stage")
        stage_data_row = {'md_inserted_at': my_job_instance.get_job_started_at().isoformat(),
                    'md_record_source': 'billbee.order',
                    'md_run_id': my_job_instance.get_job_instance_id(),
                    'md_is_deleted': False
                   }

        # transform  and stage the source rows into stage
        source_rows = data

        stage_data_rows = []


        for source_row in source_rows:
            stage_data_row_temp = dict(stage_data_row)

            stage_data_row_temp['billbeeorderid'] = source_row.get('BillBeeOrderId')
            stage_data_row_temp['billbeeparentorderid'] = source_row.get('BillBeeParentOrderId')
            stage_data_row_temp['customer_id'] = source_row.get('Customer').get('Id')
            stage_data_row_temp['ordernumber'] = source_row.get('OrderNumber')
            stage_data_row_temp['lastmodifiedat'] = source_row.get('LastModifiedAt')
            stage_data_row_temp['totalcost'] = source_row.get('TotalCost')
            stage_data_row_temp['adjustmentcost'] = source_row.get('AdjustmentCost')
            stage_data_row_temp['currency'] = source_row.get('Currency')
            stage_data_row_temp['state'] = source_row.get('State')
            stage_data_row_temp['createdat'] = source_row.get('CreatedAt')
            stage_data_row_temp['shippedat'] = source_row.get('ShippedAt')
            stage_data_row_temp['confirmedat'] = source_row.get('ConfirmedAt')
            stage_data_row_temp['payedat'] = source_row.get('PayedAt')
            stage_data_row_temp['updatedat'] = source_row.get('UpdatedAt')
            stage_data_row_temp['invoicedate'] = source_row.get('InvoiceDate')
            stage_data_row_temp['shippingcost'] = source_row.get('ShippingCost')
            stage_data_row_temp['invoiceaddress_countryiso2'] = source_row.get('InvoiceAddress').get('CountryISO2')
            stage_data_row_temp['billbeeshopid'] = source_row.get('Seller').get('BillbeeShopId')
            stage_data_row_temp['seller_billbeeshopname'] = source_row.get('Seller').get('BillbeeShopName')


            ### hashes
            stage_data_row_temp['hk_rbbe_order'] = dvf_assemble_datavault_hash([stage_data_row_temp['billbeeorderid']])
            stage_data_row_temp['hk_rbbe_order_parent'] = dvf_assemble_datavault_hash([stage_data_row_temp['billbeeparentorderid']])
            stage_data_row_temp['hk_rbbe_customer'] = dvf_assemble_datavault_hash([stage_data_row_temp['customer_id']])
            stage_data_row_temp['hk_rbbe_shop'] = dvf_assemble_datavault_hash([stage_data_row_temp['billbeeshopid']])
            stage_data_row_temp['lk_rbbe_order_customer'] = dvf_assemble_datavault_hash([stage_data_row_temp['billbeeorderid'],stage_data_row_temp['customer_id']])
            stage_data_row_temp['lk_rbbe_order_parent_order'] = dvf_assemble_datavault_hash([stage_data_row_temp['billbeeorderid'],stage_data_row_temp['billbeeparentorderid']])
            stage_data_row_temp['lk_rbbe_order_shop'] = dvf_assemble_datavault_hash([stage_data_row_temp['billbeeorderid'], stage_data_row_temp['billbeeshopid']])
            #stage_data_row_temp['lk_rbbe_order_shop_parent'] = dvf_assemble_datavault_hash([stage_data_row_temp['billbeeparentorderid'], stage_data_row_temp['billbeeshopid']])
            stage_data_row_temp['rh_rbbe_order_order_p1_l10_sat'] = dvf_assemble_datavault_hash([stage_data_row_temp['adjustmentcost'],
                                                                                            stage_data_row_temp['currency'],
                                                                                            stage_data_row_temp['invoiceaddress_countryiso2'],
                                                                                            stage_data_row_temp['ordernumber'],
                                                                                            stage_data_row_temp['shippingcost'],
                                                                                            stage_data_row_temp['totalcost']])
            stage_data_row_temp['rh_rbbe_order_order_p2_l10_sat'] = dvf_assemble_datavault_hash([stage_data_row_temp['confirmedat'],
                                                                                            stage_data_row_temp['createdat'],
                                                                                            stage_data_row_temp['invoicedate'],
                                                                                            stage_data_row_temp['payedat'],
                                                                                            stage_data_row_temp['shippedat'],
                                                                                            stage_data_row_temp['state']])

            stage_data_row_temp['rh_rbbe_order_shop_p1_l10_sat'] = dvf_assemble_datavault_hash([stage_data_row_temp['seller_billbeeshopname']])


            stage_data_rows.append(stage_data_row_temp)
            my_job_instance.count_input(1)

        execute_snf_dict_bulk_insert(dwh_cursor, insert_statement, stage_data_rows)
        dwh_connection.commit()

    # ### FRAMEWORK PHASE: Log any exception to instance
    except Exception:
        my_job_instance.end_instance_with_error(1, 'something went wrong')
        raise

    # # ### FRAMEWORK PHASE: End the instance normally
    my_job_instance.end_instance()


@cimtjobinstance_job
def load_data_to_vault(parent_job_instance, dwh_connection, file_name=None, **kwargs):


    # ### FRAMEWORK PHASE: setup your job_instance
    my_job_instance = CimtJobInstance(kwargs['instance_job_name'], parent_job_instance)
    my_job_instance.set_work_item(file_name)
    my_job_instance.start_instance()
    try:

        # ### FRAMEWORK PHASE: do processing here
        # BEGIN LOAD DATA TO VLT PART
        # general constants
        stage_schema = 'stage_rvlt'
        stage_table = 'rbbe_order_p1_stage'


        dwh_cursor = dwh_connection.cursor()


        hash_collision_check_statement_list = dvf_get_check_hash_collision_hub_elt_sql(vault_table='rbbe_order_hub',
        vault_schema='rvlt_billbee',
        stage_hk_column='HK_RBBE_ORDER',
        stage_bk_column_list=['BILLBEEORDERID'],
        db_connection=dwh_connection,
        stage_schema=stage_schema,
        stage_table=stage_table)

        dvf_execute_elt_statement_list(dwh_cursor, hash_collision_check_statement_list)

        statement_list = dvf_get_datavault_hub_elt_sql(vault_table='rbbe_order_hub',
        vault_schema='rvlt_billbee',
        stage_hk_column='HK_RBBE_ORDER',
        stage_bk_column_list=['BILLBEEORDERID'],
        db_connection=dwh_connection,
        stage_schema=stage_schema,
        stage_table=stage_table,
        meta_job_instance_id = my_job_instance.get_job_instance_id(),
        meta_inserted_at = my_job_instance.get_job_started_at() )

        dvf_execute_elt_statement_list(dwh_cursor, statement_list)

        # ----------

        hash_collision_check_statement_list = dvf_get_check_hash_collision_hub_elt_sql(vault_table='rbbe_order_hub',
        vault_schema='rvlt_billbee',
        stage_hk_column='HK_RBBE_ORDER_PARENT',
        stage_bk_column_list=['BILLBEEPARENTORDERID'],
        db_connection=dwh_connection,
        stage_schema=stage_schema,
        stage_table=stage_table)

        dvf_execute_elt_statement_list(dwh_cursor, hash_collision_check_statement_list)

        statement_list = dvf_get_datavault_hub_elt_sql(vault_table='rbbe_order_hub',
        vault_schema='rvlt_billbee',
        stage_hk_column='HK_RBBE_ORDER_PARENT',
        stage_bk_column_list=['BILLBEEPARENTORDERID'],
        db_connection=dwh_connection,
        stage_schema=stage_schema,
        stage_table=stage_table,
        meta_job_instance_id = my_job_instance.get_job_instance_id(),
        meta_inserted_at = my_job_instance.get_job_started_at() )

        dvf_execute_elt_statement_list(dwh_cursor, statement_list)

        # ----------


        hash_collision_check_statement_list = dvf_get_check_hash_collision_hub_elt_sql(vault_table='rbbe_customer_hub',
        vault_schema='rvlt_billbee',
        stage_hk_column='HK_RBBE_CUSTOMER',
        stage_bk_column_list=['CUSTOMER_ID'],
        db_connection=dwh_connection,
        stage_schema=stage_schema,
        stage_table=stage_table)

        dvf_execute_elt_statement_list(dwh_cursor, hash_collision_check_statement_list)

        statement_list = dvf_get_datavault_hub_elt_sql(vault_table='rbbe_customer_hub',
        vault_schema='rvlt_billbee',
        stage_hk_column='HK_RBBE_CUSTOMER',
        stage_bk_column_list=['CUSTOMER_ID'],
        db_connection=dwh_connection,
        stage_schema=stage_schema,
        stage_table=stage_table,
        meta_job_instance_id = my_job_instance.get_job_instance_id(),
        meta_inserted_at = my_job_instance.get_job_started_at() )

        dvf_execute_elt_statement_list(dwh_cursor, statement_list)

        # ----------


        hash_collision_check_statement_list = dvf_get_check_hash_collision_hub_elt_sql(vault_table='rbbe_shop_hub',
        vault_schema='rvlt_billbee',
        stage_hk_column='HK_RBBE_SHOP',
        stage_bk_column_list=['BILLBEESHOPID'],
        db_connection=dwh_connection,
        stage_schema=stage_schema,
        stage_table=stage_table)

        dvf_execute_elt_statement_list(dwh_cursor, hash_collision_check_statement_list)

        statement_list = dvf_get_datavault_hub_elt_sql(vault_table='rbbe_shop_hub',
        vault_schema='rvlt_billbee',
        stage_hk_column='HK_RBBE_SHOP',
        stage_bk_column_list=['BILLBEESHOPID'],
        db_connection=dwh_connection,
        stage_schema=stage_schema,
        stage_table=stage_table,
        meta_job_instance_id = my_job_instance.get_job_instance_id(),
        meta_inserted_at = my_job_instance.get_job_started_at() )

        dvf_execute_elt_statement_list(dwh_cursor, statement_list)

        # ----------


        hash_collision_check_statement_list = dvf_get_check_hash_collision_lnk_elt_sql(vault_table='rbbe_order_customer_lnk',
        vault_schema='rvlt_billbee',
        stage_lk_column='LK_RBBE_ORDER_CUSTOMER',
        stage_hk_column_list=['HK_RBBE_CUSTOMER', 'HK_RBBE_ORDER'],
        db_connection=dwh_connection,
        stage_schema=stage_schema,
        stage_table=stage_table)

        dvf_execute_elt_statement_list(dwh_cursor, hash_collision_check_statement_list)

        statement_list = dvf_get_datavault_lnk_elt_sql(vault_table='rbbe_order_customer_lnk',
        vault_schema='rvlt_billbee',
        stage_lk_column='LK_RBBE_ORDER_CUSTOMER',
        stage_hk_column_list=['HK_RBBE_CUSTOMER', 'HK_RBBE_ORDER'],
        db_connection=dwh_connection,
        stage_schema=stage_schema,
        stage_table=stage_table,
        meta_job_instance_id = my_job_instance.get_job_instance_id(),
        meta_inserted_at = my_job_instance.get_job_started_at() )

        dvf_execute_elt_statement_list(dwh_cursor, statement_list)

        # ----------


        hash_collision_check_statement_list = dvf_get_check_hash_collision_lnk_elt_sql(vault_table='rbbe_order_shop_lnk',
        vault_schema='rvlt_billbee',
        stage_lk_column='LK_RBBE_ORDER_SHOP',
        stage_hk_column_list=['HK_RBBE_ORDER', 'HK_RBBE_SHOP'],
        db_connection=dwh_connection,
        stage_schema=stage_schema,
        stage_table=stage_table)

        dvf_execute_elt_statement_list(dwh_cursor, hash_collision_check_statement_list)

        statement_list = dvf_get_datavault_lnk_elt_sql(vault_table='rbbe_order_shop_lnk',
        vault_schema='rvlt_billbee',
        stage_lk_column='LK_RBBE_ORDER_SHOP',
        stage_hk_column_list=['HK_RBBE_ORDER', 'HK_RBBE_SHOP'],
        db_connection=dwh_connection,
        stage_schema=stage_schema,
        stage_table=stage_table,
        meta_job_instance_id = my_job_instance.get_job_instance_id(),
        meta_inserted_at = my_job_instance.get_job_started_at() )

        dvf_execute_elt_statement_list(dwh_cursor, statement_list)

        # ----------


        hash_collision_check_statement_list = dvf_get_check_hash_collision_lnk_elt_sql(vault_table='rbbe_order_parent_order_lnk',
        vault_schema='rvlt_billbee',
        stage_lk_column='LK_RBBE_ORDER_PARENT_ORDER',
        stage_hk_column_list=['HK_RBBE_ORDER', 'HK_RBBE_ORDER_PARENT'],
        db_connection=dwh_connection,
        stage_schema=stage_schema,
        stage_table=stage_table)

        dvf_execute_elt_statement_list(dwh_cursor, hash_collision_check_statement_list)

        statement_list = dvf_get_datavault_lnk_elt_sql(vault_table='rbbe_order_parent_order_lnk',
        vault_schema='rvlt_billbee',
        stage_lk_column='LK_RBBE_ORDER_PARENT_ORDER',
        stage_hk_column_list=['HK_RBBE_ORDER', 'HK_RBBE_ORDER_PARENT'],
        db_connection=dwh_connection,
        stage_schema=stage_schema,
        stage_table=stage_table,
        meta_job_instance_id = my_job_instance.get_job_instance_id(),
        meta_inserted_at = my_job_instance.get_job_started_at() )

        dvf_execute_elt_statement_list(dwh_cursor, statement_list)

        # ----------


        statement_list = dvf_get_datavault_esat_elt_sql(vault_esat_table='rbbe_order_customer_esat',
        vault_schema='rvlt_billbee',
        stage_lk_column='LK_RBBE_ORDER_CUSTOMER',
        vault_lnk_table='rbbe_order_customer_lnk',
        vault_driving_key_column_list=['HK_RBBE_ORDER'],
        stage_driving_key_column_list=['HK_RBBE_ORDER'],
        with_deletion_detection=False,
        db_connection=dwh_connection,
        stage_schema=stage_schema,
        stage_table=stage_table,
        meta_job_instance_id = my_job_instance.get_job_instance_id(),
        meta_inserted_at = my_job_instance.get_job_started_at() )

        dvf_execute_elt_statement_list(dwh_cursor, statement_list)

        # ----------


        statement_list = dvf_get_datavault_esat_elt_sql(vault_esat_table='rbbe_order_parent_order_esat',
        vault_schema='rvlt_billbee',
        stage_lk_column='LK_RBBE_ORDER_PARENT_ORDER',
        vault_lnk_table='rbbe_order_parent_order_lnk',
        vault_driving_key_column_list=['HK_RBBE_ORDER'],
        stage_driving_key_column_list=['HK_RBBE_ORDER'],
        with_deletion_detection=False,
        db_connection=dwh_connection,
        stage_schema=stage_schema,
        stage_table=stage_table,
        meta_job_instance_id = my_job_instance.get_job_instance_id(),
        meta_inserted_at = my_job_instance.get_job_started_at() )

        dvf_execute_elt_statement_list(dwh_cursor, statement_list)

        # ----------

        singularity_check_statement_list = dvf_get_check_singularity_sat_elt_sql(
            vault_table='rbbe_order_order_p1_l10_sat',
            stage_hk_column='HK_RBBE_ORDER',
            stage_rh_column='RH_RBBE_ORDER_ORDER_P1_L10_SAT',
            stage_schema=stage_schema,
            stage_table=stage_table)

        dvf_execute_elt_statement_list(dwh_cursor, singularity_check_statement_list)

        statement_list = dvf_get_datavault_sat_elt_sql(vault_table='rbbe_order_order_p1_l10_sat',
                                                       vault_schema='rvlt_billbee',
                                                       stage_hk_column='HK_RBBE_ORDER',
                                                       stage_rh_column='RH_RBBE_ORDER_ORDER_P1_L10_SAT',
                                                       stage_content_column_list=['ADJUSTMENTCOST', 'CURRENCY',
                                                                                  'INVOICEADDRESS_COUNTRYISO2','LASTMODIFIEDAT',
                                                                                  'ORDERNUMBER', 'SHIPPINGCOST',
                                                                                  'TOTALCOST', 'UPDATEDAT'],
                                                       with_deletion_detection=False,
                                                       db_connection=dwh_connection,
                                                       stage_schema=stage_schema,
                                                       stage_table=stage_table,
                                                       meta_job_instance_id=my_job_instance.get_job_instance_id(),
                                                       meta_inserted_at=my_job_instance.get_job_started_at())

        dvf_execute_elt_statement_list(dwh_cursor, statement_list)

        # ----------

        singularity_check_statement_list = dvf_get_check_singularity_sat_elt_sql(
            vault_table='rbbe_order_order_p2_l10_sat',
            stage_hk_column='HK_RBBE_ORDER',
            stage_rh_column='RH_RBBE_ORDER_ORDER_P2_L10_SAT',
            stage_schema=stage_schema,
            stage_table=stage_table)

        dvf_execute_elt_statement_list(dwh_cursor, singularity_check_statement_list)

        statement_list = dvf_get_datavault_sat_elt_sql(vault_table='rbbe_order_order_p2_l10_sat',
                                                       vault_schema='rvlt_billbee',
                                                       stage_hk_column='HK_RBBE_ORDER',
                                                       stage_rh_column='RH_RBBE_ORDER_ORDER_P2_L10_SAT',
                                                       stage_content_column_list=['CONFIRMEDAT', 'CREATEDAT',
                                                                                  'INVOICEDATE', 'LASTMODIFIEDAT', 'PAYEDAT', 'SHIPPEDAT',
                                                                                  'STATE', 'UPDATEDAT'],
                                                       with_deletion_detection=False,
                                                       db_connection=dwh_connection,
                                                       stage_schema=stage_schema,
                                                       stage_table=stage_table,
                                                       meta_job_instance_id=my_job_instance.get_job_instance_id(),
                                                       meta_inserted_at=my_job_instance.get_job_started_at())

        dvf_execute_elt_statement_list(dwh_cursor, statement_list)

        # ----------


        singularity_check_statement_list = dvf_get_check_singularity_sat_elt_sql(vault_table='rbbe_order_shop_p1_l10_sat',
        stage_hk_column='LK_RBBE_ORDER_SHOP',
        stage_rh_column='RH_RBBE_ORDER_SHOP_P1_L10_SAT',
        stage_schema=stage_schema,
        stage_table=stage_table )

        dvf_execute_elt_statement_list(dwh_cursor, singularity_check_statement_list)

        statement_list = dvf_get_datavault_sat_elt_sql(vault_table='rbbe_order_shop_p1_l10_sat',
        vault_schema='rvlt_billbee',
        stage_hk_column='LK_RBBE_ORDER_SHOP',
        stage_rh_column='RH_RBBE_ORDER_SHOP_P1_L10_SAT',
        stage_content_column_list=['SELLER_BILLBEESHOPNAME'],
        with_deletion_detection=False,
        db_connection=dwh_connection,
        stage_schema=stage_schema,
        stage_table=stage_table,
        meta_job_instance_id = my_job_instance.get_job_instance_id(),
        meta_inserted_at = my_job_instance.get_job_started_at() )

        dvf_execute_elt_statement_list(dwh_cursor, statement_list)

        # ----------


        # END LOAD DATA TO VLT PART

        dwh_connection.commit()
    # ### FRAMEWORK PHASE: Log any exception to instance
    except Exception:
        my_job_instance.end_instance_with_error(1, 'something went wrong')
        raise
    # # ### FRAMEWORK PHASE: End the instance normally
    my_job_instance.end_instance()



@cimtjobinstance_job
def load_vault_1(data, parent_job_instance=None, **kwargs):

    # ### FRAMEWORK PHASE: setup your job_instance
    my_job_instance = CimtJobInstance(kwargs['instance_job_name'], parent_job_instance)
    my_job_instance.start_instance()

    try:
        # ### FRAMEWORK PHASE: do processing here
        dwh_connection = connection_snf_for_dwh_connection_type(Dvf_dwh_connection_type.raw_vault)
        stage_data(data,my_job_instance, dwh_connection)
        load_data_to_vault(my_job_instance, dwh_connection)
        dwh_connection.close()

    # ### FRAMEWORK PHASE: Log any exception to instance
    except Exception:
        my_job_instance.end_instance_with_error(1, 'some processing failed')
        raise

    # # ### FRAMEWORK PHASE: End the instance normally
    my_job_instance.end_instance()


if __name__ == '__main__':
    # local execution
    load_vault_1()