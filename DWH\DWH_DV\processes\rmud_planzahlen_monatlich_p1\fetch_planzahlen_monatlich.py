from lib.blobstorage_utils import get_source_files_from_blob_storage,fetch_excel_source_file_from_blob_container
from lib.excel_utils import get_plan_numbers_from_excel
from lib.connection_azrblob import connection_azrb<PERSON>b


def fetch_planzahlen_monatlich(blob_service_client, container_name, sap_source_object):
    source_files = get_source_files_from_blob_storage(blob_service_client, sap_source_object, container_name)
    sorted_source_file_names, sorted_source_file_names_modified_date = zip(*sorted([(file.name, file.last_modified) for file in source_files]))
    source_rows = []
    processed_files = []
    for file_index, file_name in enumerate(sorted_source_file_names):
        blob_client = blob_service_client.get_blob_client(container=container_name, blob=file_name)
        excel = fetch_excel_source_file_from_blob_container(blob_client)
        excel_rows, headers = get_plan_numbers_from_excel(excel)
        if 'Monat' in headers:
            source_rows.extend(excel_rows)
            processed_files.append(file_name)
    highest_last_modified = max(sorted_source_file_names_modified_date)
    return source_rows, processed_files, highest_last_modified


if __name__ == '__main__':
    sap_source_object = "_Planzahlen"
    container_name = "manualplandata"
    blob_service_client = connection_azrblob('blob_storage')
    fetch_planzahlen_monatlich(blob_service_client, container_name, sap_source_object)