/*
 * Schema: mart_auftraege
 * 
 * 描述：
 * 此模式包含订单相关的数据集市表（Data Mart Tables）。
 * 它是面向业务用户的数据层，提供优化的数据结构用于报表和分析。
 * 数据已经从业务数据仓库(Business Vault)转换为面向主题的结构。
 * 
 * 包含的表类型：
 * - 事实表：存储订单相关的度量和指标
 * - 维度表：存储与订单相关的维度信息
 * - 聚合表：存储预计算的聚合数据
 * - 报表视图：为特定报表优化的数据视图
 */

-- DROP SCHEMA mart_auftraege;

CREATE SCHEMA if not EXISTS mart_auftraege;

COMMENT ON SCHEMA mart_auftraege
  IS 'Contains data mart tables for orders analysis and reporting';
 
 Grant usage on schema mart_auftraege to role DV_D_ACCESS_MART_READ;

