-- generated script for stage_bvlt.bgnr_kalender_p1_stage

-- DROP TABLE stage_bvlt.bgnr_kalender_p1_stage;

CREATE TABLE stage_bvlt.bgnr_kalender_p1_stage (
--metadata,
MD_INSERTED_AT TIMESTAMP NOT NULL,
MD_<PERSON>UN_ID INT NOT NULL,
MD_RECORD_SOURCE VARCHAR(255) NOT NULL,
MD_IS_DELETED BOOLEAN NOT NULL,
--hash keys,
HK_RGNR_GESELLSCHAFT CHAR(28) NOT NULL,
LK_RGNR_GESELLSCHAFT_KALENDER CHAR(28) NOT NULL,
RH_BGNR_GESELLSCHAFT_KALENDER_P1_B10_SAT CHAR(28) NOT NULL,
--business keys,
COMPANY Varchar(50) NULL,
TAGESDATUM DATE NULL,
--content,
IST_WERKTAG BOOLEAN NULL,
<PERSON><PERSON><PERSON> varchar(2000) NULL
);
-- end of script --