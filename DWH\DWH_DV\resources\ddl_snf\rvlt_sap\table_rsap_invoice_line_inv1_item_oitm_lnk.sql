/*
 * 表名: rvlt_sap.rsap_invoice_line_inv1_item_oitm_lnk
 * 
 * 描述：
 * 此表是SAP发票行项目与物料之间的Link表，建立发票行与物料之间的关系。
 * 作为Data Vault模型中的Link表，它连接两个不同的业务实体，实现多对多关系。
 * 每条记录代表一个发票行与一个物料之间的关联关系。
 * 
 * 字段说明：
 * - MD_INSERTED_AT: 记录插入时间戳，表示关联关系何时被记录
 * - MD_RUN_ID: 加载过程的运行ID，用于跟踪ETL过程
 * - MD_RECORD_SOURCE: 数据来源系统，标识数据的来源
 * - HK_RSAP_INVOICE_LINE_INV1: 发票行的哈希键，关联到发票行Hub表
 * - HK_RSAP_ITEM_OITM: 物料的哈希键，关联到物料Hub表
 * - LK_RSAP_INVOICE_LINE_INV1_ITEM_OITM: 关联关系的哈希键，由两个实体的哈希键计算得出
 * 
 * 相关表：
 * - rsap_invoice_line_inv1_hub: 发票行的Hub表
 * - rsap_item_oitm_hub: 物料的Hub表
 * - rsap_invoice_line_inv1_item_oitm_esat: 存储发票行与物料关系的扩展属性
 * 
 * 在Data Vault模型中的作用：
 * 此Link表是连接发票行与物料的桥梁，使系统能够追踪哪些物料出现在哪些发票行中。
 * 通过这种关联，可以进行物料销售分析、库存管理和收入分析等业务应用。
 */

-- DROP TABLE rvlt_sap.rsap_invoice_line_inv1_item_oitm_lnk;

CREATE TABLE rvlt_sap.rsap_invoice_line_inv1_item_oitm_lnk (
MD_INSERTED_AT TIMESTAMP NOT NULL,
MD_RUN_ID INT NOT NULL,
MD_RECORD_SOURCE VARCHAR(255) NOT NULL,
HK_RSAP_INVOICE_LINE_INV1 CHAR(28) NOT NULL,
HK_RSAP_ITEM_OITM CHAR(28) NOT NULL,
LK_RSAP_INVOICE_LINE_INV1_ITEM_OITM CHAR(28) NOT NULL
);

--COMMENT STATEMENTS

-- end of script --