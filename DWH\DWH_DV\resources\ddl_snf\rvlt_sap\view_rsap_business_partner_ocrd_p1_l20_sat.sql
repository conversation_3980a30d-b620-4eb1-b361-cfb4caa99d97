CREATE VIEW rvlt_sap.rsap_business_partner_ocrd_p1_l20_sat (
    MD_INSERTED_AT,
	MD_RUN_ID,
	MD_RECORD_SOURCE,
    MD_IS_DELETED,
    MD_VALID_BEFORE,
    HK_RSAP_BUSINESS_PARTNER_OCRD,
    RH_RSAP_BUSINESS_PARTNER_OCRD_J1_L10_SAT,
    CARDNAME,
    U_DIM1 COMMENT 'Verkaufsgebiet',
    U_DIM2 COMMENT 'Verkäufernummer',
    UPDATEDATE,
    GROUPCODE
)
AS
SELECT MD_INSERTED_AT
    , MD_RUN_ID
    , MD_RECORD_SOURCE
    , MD_IS_DELETED
    , MD_VALID_BEFORE
    , HK_RSAP_BUSINESS_PARTNER_OCRD
    , RH_RSAP_BUSINESS_PARTNER_OCRD_J1_L10_SAT
    , JSON_EXTRACT_PATH_TEXT(json_text, '<PERSON>Name') as CARDNAME
    , JSO<PERSON>_EXTRACT_PATH_TEXT(json_text, 'U_Dim1') as U_DIM1
    , JSON_EXTRACT_PATH_TEXT(json_text, 'U_Dim2') as U_DIM2
    , TRY_TO_DATE(JSON_EXTRACT_PATH_TEXT(json_text, 'UpdateDate')) as UPDATEDATE
    , JSON_EXTRACT_PATH_TEXT(json_text, 'GroupCode') as GROUPCODE
FROM rvlt_sap.rsap_business_partner_ocrd_j1_l10_sat;

