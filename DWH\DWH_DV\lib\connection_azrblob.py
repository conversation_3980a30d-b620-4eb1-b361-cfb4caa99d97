"""  
Azure Blob Storage 连接模块

本模块提供了与Azure Blob Storage建立连接的功能，是数据仓库ETL流程中访问Blob存储的基础。
主要功能：
- 读取配置信息并建立Azure Blob Storage连接
- 提供错误处理和日志记录功能

与其他模块的关系：
- 依赖于lib.configuration模块读取配置信息
- 被lib.blobstorage_utils模块使用，为其提供Blob存储连接
- 被processes目录下的各个处理模块直接使用，如rsap_oocr_j1等

典型使用场景：
1. 在ETL流程开始时建立Blob存储连接
2. 通过连接对象访问和操作Blob存储中的文件

作者：SUND DWH团队
"""

from azure.storage.blob import BlobServiceClient
from azure.identity import DefaultAzureCredential
import os
import sys
from lib.configuration import configuration_read


def error_print(*args, **kwargs):
    """
    将错误消息输出到标准错误流
    
    该函数用于错误日志记录，主要在connection_azrblob函数中被调用，
    用于记录连接过程中的错误信息。
    
    参数:
        *args: 要打印的参数
        **kwargs: 关键字参数，传递给print函数
    
    返回:
        无返回值
    """
    print(*args, file=sys.stderr, **kwargs)

def connection_azrblob(object="blob_storage"):
    """
    读取配置并创建Azure Blob Storage连接
    
    该函数是本模块的核心功能，被lib.blobstorage_utils模块和processes目录下的
    各个处理模块广泛使用，如rsap_oocr_j1、rsap_oinv_j1等SAP数据处理模块。
    它通过读取配置文件中的连接信息，创建并返回Azure Blob Storage连接对象。
    
    参数:
        object: 配置对象名称，默认为'blob_storage'，对应配置文件中的节
    
    返回:
        BlobServiceClient对象，用于操作Azure Blob Storage
        失败时抛出异常
    
    使用示例:
        blob_service_client = connection_azrblob('blob_storage')
        containers = blob_service_client.list_containers()
    """
    system = 'dwh_azrblob'
    try:
        credential = DefaultAzureCredential(managed_identity_client_id=os.getenv('UMI_CLIENT_ID')) #UMI_CLIENT_ID need to be explicit for cloud execution
        params = configuration_read(system, object,['url'])
        bs_url = params['url']
        connection = BlobServiceClient(account_url=bs_url, credential=credential)
    except Exception as error:
        error_print('Connection Error:', error)
        error_print('Object used:', object)
        raise
    return connection


if __name__ == '__main__':
    # small test
    blob_service_client = connection_azrblob()
    containers = blob_service_client.list_containers(include_metadata=True)
    for container in containers:
        print(container['name'], container['metadata'])