"""  
Excel数据处理工具模块

本模块提供了一系列用于处理Excel数据的工具函数，主要用于数据仓库ETL流程中的Excel数据处理。
主要功能包括：
- 查找Excel中的特定单元格
- 提取表头和数据
- 处理计划数据（Planzahlen）
- 将Excel数据转换为结构化格式

与其他模块的关系：
- 与lib.blobstorage_utils模块配合使用，处理从Blob存储获取的Excel数据
- 被processes目录下的计划数据处理模块使用，如rmud_planzahlen_jaehrlich_p1、rmud_planzahlen_monatlich_p1等

典型使用场景：
1. ETL流程中处理手工上传的Excel格式计划数据
2. 提取Excel中的结构化数据并转换为适合数据仓库的格式

作者：SUND DWH团队
"""

from openpyxl import load_workbook
import json
import os

def find_cell_by_value(sheet, search_value):
    """
    在Excel工作表中查找特定值的单元格位置
    
    该函数在处理Excel数据时被广泛使用，特别是在定位表头或关键字段时。
    通常与blobstorage_utils.py中的fetch_excel_source_file_from_blob_container函数配合使用。
    
    参数:
        sheet: Excel工作表对象
        search_value: 要查找的值
        
    返回:
        (row, column): 找到的单元格行号和列号，未找到则返回(None, None)
    """
    for row in sheet.iter_rows(values_only=False):  # iter_rows returns cell objects if values_only=False
        for cell in row:
            if cell.value == search_value:
                return cell.row, cell.column
    return None, None  # Return None if header not found

def find_position_of_first_value_in_first_row(worksheet):
    """
    查找工作表第一行中第一个非空值的列位置
    
    该函数在处理Excel数据时被使用，特别是在确定数据起始列时。
    通常在处理计划数据（Planzahlen）时与其他Excel处理函数配合使用。
    
    参数:
        worksheet: Excel工作表对象
        
    返回:
        column: 找到的第一个非空值的列号
    """
    for cell in worksheet[1]:  # worksheet[1] accesses the first row
        if cell.value is not None:
            return cell.column

def get_headers(worksheet, min_row=None, max_row=None, min_col=None, max_col=None):
    """
    获取工作表中指定范围的表头数据
    
    该函数在ETL流程中被使用，特别是在处理计划数据（Planzahlen）时，
    用于提取表头信息。通常与rmud_planzahlen_jaehrlich_p1和rmud_planzahlen_monatlich_p1
    等处理模块配合使用。
    
    参数:
        worksheet: Excel工作表对象
        min_row: 起始行号
        max_row: 结束行号
        min_col: 起始列号
        max_col: 结束列号
        
    返回:
        headers: 包含表头数据的列表
    """
    headers = []
    for row in worksheet.iter_rows(min_row=min_row, max_row=max_row, min_col=min_col, max_col=max_col):
        for cell in row:
            row_data = cell.value
            headers.append(row_data)
    return headers

def get_subgrouping_data(worksheet, min_row=None, max_row=None, min_col=None, max_col=None):
    """
    获取工作表中指定范围的子分组数据
    
    该函数在处理计划数据（Planzahlen）时被使用，用于提取数据的子分组信息，
    如公司、部门等分类数据。通常与rmud_planzahlen_jaehrlich_p1和rmud_planzahlen_monatlich_p1
    等处理模块配合使用。
    
    参数:
        worksheet: Excel工作表对象
        min_row: 起始行号
        max_row: 结束行号
        min_col: 起始列号
        max_col: 结束列号
        
    返回:
        subgrouping_data: 包含子分组数据的二维列表
    """
    subgrouping_data = []
    for row in worksheet.iter_rows(min_row=min_row, max_row=max_row, min_col=min_col, max_col=max_col):
        row_data = [cell.value for cell in row]
        subgrouping_data.append(row_data)
    return subgrouping_data

def get_plan_numbers_data(worksheet, min_row=None, max_row=None, min_col=None, max_col=None):
    """
    获取工作表中指定范围的计划数据
    
    该函数在处理计划数据（Planzahlen）时被使用，用于提取实际的计划数值。
    通常与rmud_planzahlen_jaehrlich_p1和rmud_planzahlen_monatlich_p1等处理模块配合使用，
    是ETL流程中处理Excel格式计划数据的核心函数之一。
    
    参数:
        worksheet: Excel工作表对象
        min_row: 起始行号
        max_row: 结束行号
        min_col: 起始列号
        max_col: 结束列号
        
    返回:
        plan_numbers: 包含计划数据的二维列表
    """
    plan_numbers = []
    for row in worksheet.iter_rows(min_row=min_row, max_row=max_row, min_col=min_col, max_col=max_col):
        row_data = [cell.value for cell in row]
        plan_numbers.append(row_data)
    return plan_numbers

def get_dimensions(worksheet, min_row=None, max_row=None, min_col=None, max_col=None):
    """
    获取工作表中指定范围的维度数据
    
    该函数在处理计划数据（Planzahlen）时被使用，用于提取数据的维度信息，
    如时间维度、产品维度等。通常与rmud_planzahlen_jaehrlich_p1和rmud_planzahlen_monatlich_p1
    等处理模块配合使用。
    
    参数:
        worksheet: Excel工作表对象
        min_row: 起始行号
        max_row: 结束行号
        min_col: 起始列号
        max_col: 结束列号
        
    返回:
        dimensions: 包含维度数据的二维列表
    """
    dimensions = []
    for row in worksheet.iter_rows(min_row=min_row, max_row=max_row, min_col=min_col, max_col=max_col):
        row_data = [cell.value for cell in row]
        dimensions.append(row_data)
    return dimensions

def get_yearly_plan_numbers_dict(dimensions, plan_numbers, subgrouping_data):
    """
    将维度数据、计划数据和子分组数据组合成年度计划数据字典
    
    该函数是处理年度计划数据（Planzahlen）的核心函数，将从Excel中提取的各类数据
    组合成结构化的字典格式，便于后续处理和存储。主要被rmud_planzahlen_jaehrlich_p1
    处理模块使用。
    
    参数:
        dimensions: 维度数据列表
        plan_numbers: 计划数据列表
        subgrouping_data: 子分组数据列表
        
    返回:
        output_rows: 包含结构化年度计划数据的字典列表
    """
    output_rows = []
    for company in range(len(dimensions)):
        for plan_group in range(len(plan_numbers[company])):
            output_row = dict()
            output_row['jahr'] = dimensions[company][0]
            output_row['company'] = dimensions[company][1]
            output_row['planwert'] = plan_numbers[company][plan_group]
            if len(subgrouping_data) != 0:
                output_row['untergruppierung '] = json.dumps({g[0].lower(): g[plan_group + 1] for g in subgrouping_data})
            output_rows.append(output_row)
    return output_rows

def get_monthly_plan_numbers_dict(dimensions, plan_numbers, subgrouping_data):
    output_rows = []
    for month in range(len(dimensions)):
        for plan_group in range(len(plan_numbers[month])):
            output_row = dict()
            output_row['jahr'] = dimensions[month][0]
            output_row['monat'] = dimensions[month][1]
            output_row['company'] = dimensions[month][2]
            output_row['planwert'] = plan_numbers[month][plan_group]
            if len(subgrouping_data) != 0:
                output_row['untergruppierung'] = json.dumps({g[0].lower(): g[plan_group + 1] for g in subgrouping_data})
            output_rows.append(output_row)
    return output_rows

def get_plan_numbers_dict(headers, dimensions, plan_numbers, subgrouping_data):
    output_rows = []
    #for dimension in range(len(dimensions)):
    for i_dim, dimension in enumerate(dimensions):
        #for plan_number_index in range(len(plan_numbers[i_dim])):
        for i_plan, plan_number in enumerate(plan_numbers[i_dim]):
            output_row = dict()
            for i, h in enumerate(headers):
                output_row[h] = dimension[i]
            output_row['planwert'] = plan_number
            #plan_numbers[i_dim][i_plan]
            if len(subgrouping_data) != 0:
                output_row['untergruppe_json'] = json.dumps({g[0].lower(): g[i_plan + 1] for g in subgrouping_data})
            output_rows.append(output_row)
    return output_rows

def get_plan_numbers_from_excel(excel_file):
    """
    从Excel文件中提取计划数据
    
    该函数是处理Excel格式计划数据的核心函数，被rmud_planzahlen_jaehrlich_p1和
    rmud_planzahlen_monatlich_p1等处理模块直接调用。它与blobstorage_utils.py中的
    fetch_excel_source_file_from_blob_container函数配合使用，完成从Blob存储获取
    Excel文件并提取计划数据的完整流程。
    
    函数处理流程：
    1. 加载Excel工作簿并获取第一个工作表
    2. 定位关键字段（Jahr、Planwert等）
    3. 提取维度数据、计划数值和表头
    4. 组合成结构化的字典格式返回
    
    参数:
        excel_file: Excel文件对象（通常是BytesIO对象）
        
    返回:
        (output_rows, headers): 包含结构化计划数据的字典列表和表头列表
    """
    workbook = load_workbook(excel_file)
    worksheet = workbook.worksheets[0] #access the first sheet

    # find the position of "Jahr" to know where data starts
    first_row, first_col = find_cell_by_value(worksheet, "Jahr")
    # find the position of the planned numbers
    plan_number_row, plan_number_col = find_cell_by_value(worksheet, "Planwert")
    # find which first column in the first row has a value, important for "untergruppe_json"
    first_value_first_row_col = find_position_of_first_value_in_first_row(worksheet)

    try:
        #get dimensions (jahr,(monat), firma) data
        dimensions = get_dimensions(worksheet, min_row=first_row + 1, max_row=worksheet.max_row, min_col=first_col, max_col=plan_number_col-1)
        #get planned numbers data
        plan_numbers = get_plan_numbers_data(worksheet, min_row=plan_number_row + 1, max_row=worksheet.max_row, min_col=plan_number_col)
        #get headers names (jahr,(monat), firma)
        headers = get_headers(worksheet, min_row=first_row, max_row=first_row, min_col=first_col, max_col=plan_number_col-1)
        #get data for "untergruppe_json"
        subgrouping_data = get_subgrouping_data(worksheet, min_row=1, max_row=first_row-1, min_col=first_value_first_row_col, max_col=first_col-1) if first_row != 1 else []

        output_rows = get_plan_numbers_dict(headers, dimensions, plan_numbers, subgrouping_data)
    except Exception as e:
        raise e

    return output_rows, headers



if __name__ == '__main__':
    file = None # add path
    source_rows = get_plan_numbers_from_excel(file)
