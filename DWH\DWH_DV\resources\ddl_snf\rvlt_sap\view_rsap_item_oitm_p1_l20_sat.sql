CREATE VIEW rvlt_sap.rsap_item_oitm_p1_l20_sat(
    MD_INSERTED_AT,
	MD_RUN_ID,
	MD_RECORD_SOURCE,
    MD_IS_DELETED,
    MD_VALID_BEFORE,
    HK_RSAP_ITEM_OITM,
    RH_RSAP_ITEM_OITM_J1_L10_SAT,
    ITEMNAME,
    FRGNNAME,
    ITMGRPCOD,
    UPDATEDATE
)
AS
SELECT
    MD_INSERTED_AT,
	MD_RUN_ID,
	MD_RECORD_SOURCE,
    MD_IS_DELETED,
    MD_VALID_BEFORE,
    HK_RSAP_ITEM_OITM,
    RH_RSAP_ITEM_OITM_J1_L10_SAT,
    JSON_EXTRACT_PATH_TEXT(JSON_TEXT, 'ItemName') as ITEMNAME,
    JSON_EXTRACT_PATH_TEXT(JSON_TEXT, 'FrgnName') as FRGNNAME,
    JSON_EXTRACT_PATH_TEXT(JSO<PERSON>_TEXT, 'ItmsGrpCod') as ITMGRPCOD,
    TRY_TO_DATE(JSON_EXTRACT_PATH_TEXT(json_text, 'UpdateDate')) as UPDATEDATE
FROM rvlt_sap.rsap_item_oitm_j1_l10_sat;
