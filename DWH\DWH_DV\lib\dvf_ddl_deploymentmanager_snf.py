import os
import re
import json

from snowflake.connector.errors import Error

from lib.configuration import configuration_load_ini, configuration_read
from lib.dvf_basics import Dvf_dwh_connection_type, HASHKEY_FOR_MISSING_DATA, FAR_FUTURE_DATE_SQL_STRING, \
    HASHKEY_FOR_DELIVERED_NULL
from lib.dvf_sqlByConvention_snf import dvf_get_datavault_column_lists_from_snf, \
    dvf_get_dvpi_like_column_lists_by_convention_from_snf


# define a function that handles and parses psycopg2 exceptions
def print_snowflake_exception(err):
    print("\nsnowflake Diagnostic:")
    print("\nextensions.Diagnostics:", err.msg)
    print("error:", err.raw_msg)
    print("code:", err.errno, "\n")

def get_missing_number_for_digits(digits: int):
    result = '9' * digits
    return result

def get_missing_for_string_length(length: int = 13, must_be_fixed_length=False):
    result = '!#!missing!#!'
    if length < 13:
        result = '#' * length
    else:
        if must_be_fixed_length:
            result += ' ' * (length - 13)
    return "'" + result + "'"


# # ------------------- Constants ----------------------------

PROCESS_ROLE   = "DV_D_ACCESS_WRITE"
READ_ROLE = "DV_D_ACCESS_VAULT_READ"

# # ------------------- Constants ----------------------------

class Dvf_ddl_deploymentmanager_snf:
    """Object to setup datamodel objects in a target database"""

    def __init__(self, db_connection,load_process_id=-1,load_date='1970-01-01'):
        self._db_connection = db_connection
        self._ddl_root_path = ""
        print('var E_CONF_AVAILABLE:', os.getenv('E_CONF_AVAILABLE'))

        params = configuration_read('dvf', 'ddl_deployment')
        self.ddl_root_path = params['ddl_root_path']
        self.ddl_root_path = self.ddl_root_path.replace('$PYTHONPATH', os.getenv('PYTHONPATH'))
        self.load_process_id=load_process_id
        self.load_date=load_date

    def deploy_schema(self, schema_name):
        """Deploy create schema script from DDL repository if necessary (will deploy schema if missing)"""
        check_cursor = self._db_connection.cursor()
        check_cursor.execute(f"""select count(1) from information_schema.schemata
                                where lower(schema_name)=lower(%s)""", (schema_name,))
        result_row = check_cursor.fetchone()
        if result_row[0] == 0:  # schema is not present in DB
            self._deploy_normal_script(schema_name, f"schema_{schema_name.lower()}.sql")
            self._apply_default_grants()

    def deploy_table(self, schema_name: str, table_name: str):
        """Deploy create table script from DDL repository if necessary (will deploy schema if missing)"""
        check_cursor = self._db_connection.cursor()
        check_cursor.execute("""select count(1) from information_schema.tables
                                where lower(table_schema)=lower(%s)
                                and lower(table_name)=lower(%s)""", (schema_name, table_name))
        result_row = check_cursor.fetchone()
        if result_row[0] == 0:  # table is not present in DB
            self.deploy_schema(schema_name)
            self._deploy_normal_script(schema_name.lower(), f"table_{table_name.lower()}.sql")
            if table_name[-3:] in ('hub','sat','lnk'):
                self._add_ghost_records(schema_name,table_name)
            self._apply_table_grants_for_process_role(schema_name, table_name)
            self._apply_table_grants_for_read_role(schema_name, table_name)
        else:
            print("DDL Deployment Manager: Table already deployed:", table_name)

    def deploy_stage_table(self, schema_name, table_name):
        """Deploy create table script from DDL repository if necessary (will deploy schema if missing)"""
        if schema_name.lower().find('rvlt_') == 0:
            stage_schema = 'stage_rvlt'
        elif schema_name.lower().find('bvlt_') == 0:
            stage_schema = 'stage_bvlt'
        else:
            raise Exception("Could not determine stage schema. Target schema does not contain 'rvlt' or 'bvlt'")

        check_cursor = self._db_connection.cursor()
        check_cursor.execute("""select count(1) from information_schema.tables
                                where lower(table_schema)=lower(%s)
                                and lower(table_name)=lower(%s)""", (stage_schema, table_name))
        result_row = check_cursor.fetchone()
        if result_row[0] == 0:  # table is not present in DB
            self.deploy_schema(schema_name.lower())
            self._deploy_stage_script(schema_name.lower(), stage_schema, f"table_{table_name.lower()}.sql")
            self._apply_table_grants_for_process_role(stage_schema, table_name, True)
            self._apply_table_grants_for_read_role(stage_schema, table_name)
        else:
            print("DDL Deployment Manager: Stage Table already deployed:", table_name)

    def deploy_stage_view(self, schema_name, view_name, force_deploy=False):
        """Deploy view script from DDL repository if necessary (will deploy schema if missing). Script must include
           "drop view if exists ... cascade;" statement"""
        if schema_name[0].lower() == 'r' or schema_name.lower() == 'template':
            stage_schema = 'stage_rvlt'
        elif schema_name[0].lower() == 'b':
            stage_schema = 'stage_bvlt'
        else:
            raise Exception("Could not determine stage schema. Unknown prefix")

        check_cursor = self._db_connection.cursor()
        check_cursor.execute("""select count(1) from information_schema.views
                                where lower(table_schema)=lower(%s)
                                and lower(table_name)=lower(%s)""", (schema_name, view_name))
        result_row = check_cursor.fetchone()
        if result_row[0] == 0 or force_deploy:  # table is not present in DB
            self.deploy_schema(schema_name)
            self._deploy_stage_script(schema_name.lower(), stage_schema, f"view_{view_name.lower()}.sql")
            self._apply_view_grants_for_process_role(schema_name, view_name)
            self._apply_view_grants_for_read_role(schema_name, view_name)
        else:
            print("DDL Deployment Manager: View already deployed:", view_name)

    def deploy_view(self, schema_name, view_name, force_deploy=False):
        """Deploy view script from DDL repository if necessary (will deploy schema if missing). Script must include
           "drop view if exists ... cascade;" statement"""
        try:
            check_cursor = self._db_connection.cursor()
            check_cursor.execute("""select count(1) from information_schema.views
                                    where lower(table_schema)=lower(%s)
                                    and lower(table_name)=lower(%s)""", (schema_name, view_name))
            result_row = check_cursor.fetchone()
        except Error as err:
            print('ERROR during dictionary check')
            print("snf_err_msg:", err.msg)
            print("snf_errno:", err.errno, "\n")
            raise
        if result_row[0] == 0 or force_deploy:  # table is not present in DB
            self.deploy_schema(schema_name)
            self._deploy_normal_script(schema_name, f"view_{view_name.lower()}.sql")
            self._apply_view_grants_for_process_role(schema_name, view_name)
            self._apply_view_grants_for_read_role(schema_name, view_name)
        else:
            print("DDL Deployment Manager: View already deployed:", view_name)

    def deploy_matview(self, schema_name, matview_name, force_deploy=False):
        """Deploy materialized 'matview' script from DDL repository if necessary (will deploy schema if missing).
            Script must include "drop materialized view if exists ... cascade;" statement"""
        check_cursor = self._db_connection.cursor()
        # --for mat_views
        # --select * from pg_matviews;
        check_cursor.execute("""select count(1) from pg_matviews
                                where lower(schemaname)=lower(%s)
                                and lower(matviewname)=lower(%s);""", (schema_name, matview_name))
        result_row = check_cursor.fetchone()
        if result_row[0] == 0 or force_deploy:  # table is not present in DB
            self.deploy_schema(schema_name)
            self._deploy_normal_script(schema_name, f"matview_{matview_name.lower()}.sql")
        else:
            print("DDL Deployment Manager: Materialized View already deployed:", matview_name)

    def deploy_function(self, schema_name, function_name):
        """Deploy function script from DDL repository if necessary"""
        self.deploy_schema(schema_name)
        self._deploy_normal_script(schema_name, f"function_{function_name.lower()}.sql")
        self._apply_function_grants_for_process_role(schema_name, function_name)
        self._apply_function_grants_for_read_role(schema_name, function_name)

    def execute_testdata_insert(self, schema_name, test_name):
        """Deploy function script from DDL repository if necessary"""
        self._execute_testdata_insert_script(schema_name, f"insert_{test_name.lower()}.sql")

    def _deploy_normal_script(self, schema_name, file_name):
        full_path = f"{self.ddl_root_path}/{schema_name.lower()}/{file_name}"
        print("Deploying:", schema_name, file_name)
        self._deploy_script(schema_name, full_path)

    def _deploy_stage_script(self, schema_name, stage_schema, file_name):
        full_path = f"{self.ddl_root_path}/{schema_name}/stage/{file_name}"
        print("Deploying Stage :", schema_name, file_name)
        self._deploy_script(stage_schema, full_path)

    def _execute_testdata_insert_script(self, schema_name, file_name):
        full_path = f"{self.ddl_root_path}/{schema_name}/tests_and_demos/{file_name}"
        print("Executing testdata insert script :", schema_name, file_name)
        self._deploy_script(schema_name, full_path)

    def _deploy_script(self, schema_name, file_path):
        print(os.path.abspath(file_path))
        print(os.path.exists(file_path))

        if not os.path.exists(file_path):
            raise Exception("Could not find script:" + file_path)
        current_statement = ""
        in_dollar_quote = False
        deploy_cursor = self._db_connection.cursor()
        with open(file_path) as file:
            script_line = file.readline()
            while script_line:
                if not script_line.strip().startswith("--"):
                    current_statement += script_line
                    if script_line.strip().startswith("$$"):
                        in_dollar_quote = not in_dollar_quote
                    if script_line.rfind(";") >= 0 and not in_dollar_quote:  # One Statement complete
                        print("Script Execute: vvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvv")
                        print(current_statement)
                        print("^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^")
                        try:
                            deploy_cursor.execute(current_statement)
                        except Error as err:
                            # pass exception to function
                            print_snowflake_exception(err)
                            raise
                        current_statement = ""
                script_line = file.readline()

        if len(current_statement.strip()) > 0:  # still some letters in the buffer
            print("Script Execute: vvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvv")
            print(current_statement)
            print("^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^")
            try:
                deploy_cursor.execute(current_statement)
            except Error as err:
                # pass exception to function
                print_snowflake_exception(err)
            raise

        self._db_connection.commit()



    def _apply_default_grants(self):
        full_path = f"{self.ddl_root_path}/database_creation/users/grant_schema_defaults_basics.sql"
        if not os.path.exists(full_path):
            raise Exception("Could not find DDL script:" + full_path)
        self._deploy_script('', full_path)

        full_path = f"{self.ddl_root_path}/database_creation/users/grant_schema_defaults_generic.sql"
        if not os.path.exists(full_path):
            raise Exception("Could not find DDL script:" + full_path)

        print("adding default grants")
        generator_statement = ""
        with open(full_path) as file:
            script_line = file.readline()
            while script_line:
                if not script_line.strip().startswith("--"):
                    generator_statement += script_line
                script_line = file.readline()
        deploy_cursor = self._db_connection.cursor()
        deploy_cursor.execute(generator_statement)
        statement_array = deploy_cursor.fetchall()
        print("--- Schema deploy-Generated the following grants:")
        for grant_statement in statement_array:
            print(grant_statement[0])
            deploy_cursor.execute(grant_statement[0])

        self._db_connection.commit()


    def _add_ghost_records(self, schema_name, table_name):
        null_record_column_class_map = {'meta_load_process_id': str(self.load_process_id),
                                        'meta_load_date': f"'{self.load_date}'",
                                        'meta_record_source': "'SYSTEM'",
                                        'meta_deletion_flag': 'false',
                                        'meta_load_enddate': "'"+FAR_FUTURE_DATE_SQL_STRING+"'",
                                        'key': "'"+HASHKEY_FOR_DELIVERED_NULL+"'",
                                        'parent_key':"'"+HASHKEY_FOR_DELIVERED_NULL+"'",
                                        'diff_hash': "'"+HASHKEY_FOR_DELIVERED_NULL+"'",
                                        'business_key': 'null',
                                        'dependent_child_key': 'null',
                                        'content': 'null',
                                        'content_untracked': 'null'}

        missing_record_column_class_map = {'meta_load_process_id': str(self.load_process_id),
                                           'meta_load_date': f"'{self.load_date}'",
                                           'meta_record_source': "'SYSTEM'",
                                           'meta_deletion_flag': 'true',
                                           'meta_load_enddate': "'"+FAR_FUTURE_DATE_SQL_STRING+"'",
                                           'key': "'"+HASHKEY_FOR_MISSING_DATA+"'",
                                           'parent_key': "'"+HASHKEY_FOR_MISSING_DATA+"'",
                                           'diff_hash': "'"+HASHKEY_FOR_MISSING_DATA+"'",
                                           'business_key': 'const_missing_data',
                                           'dependent_child_key': 'const_missing_data',
                                           'content': 'const_missing_data',
                                           'content_untracked': 'const_missing_data'}
        const_for_missing_map = {
            'VARCHAR': '!#!missing!#!',
            'CHAR': 'use_get_missing_for_string_length',
            'TEXT': "'!#!missing!#!'",
            'INT': '999999999',
            'INTEGER': '999999999',
            'SMALLINT': '9999',
            'BIGINT': '999999999999999999',
            'DECIMAL': 'use_get_missing_number_for_digits_function',
            'NUMERIC': 'use_get_missing_number_for_digits_function',
            'NUMBER': 'use_get_missing_number_for_digits_function',
            'FLOAT': 'NaN',
            'REAL': 'NaN',
            'DOUBLE': 'NaN',
            'BOOLEAN': 'null',
            'DATE': "'2998-11-30'",
            'DATETIME': "'2998-11-30 00:00:00.000'",
            'TIMESTAMP': ""'2998-11-30 00:00:00.000'"",
            'TIMESTAMP_NTZ': "'2998-11-30 00:00:00.000'",
            'TIME': "'00:00'",
            'BYTE': '-99'
        }



        columns=dvf_get_dvpi_like_column_lists_by_convention_from_snf(self._db_connection, schema_name, table_name)

        column_names = []
        values_for_null = []
        values_for_missing = []
        table_stereotype=table_name[-3:]

        for column in columns:
            column_names.append(column['column_name'])
            column_class = column['column_class']
            if table_stereotype=='sat' and column_class=='parent_key':
                table_key_column=column['column_name']
            elif column_class=='key':
                table_key_column = column['column_name']
            values_for_null.append(null_record_column_class_map[column_class])
            value_for_missing = missing_record_column_class_map[column_class]
            if value_for_missing == 'const_missing_data':
                if 'json' in column['column_name'].lower():
                    value_for_missing = "'{\"missing_value\": \"!#!missing!#!\"}'"
                else:
                    column_type = column['column_type']
                    # Extract the base SQL type and optional parameters
                    match = re.match(r'(\w+)(?:\((\d+),?\s*(\d*)\))?', column_type)
                    base_type, length, scale = match.groups()
                    if scale == '':
                        scale = None
                    if length != None:
                        length = int(length)
                    if scale != None:
                        scale = int(scale)
                    base_type = base_type.upper()
                    nullable = True if 'is_nullable' in column and column['is_nullable'] == True else False
                    value_for_missing = const_for_missing_map[base_type]
                    # print(f'base_type: {base_type} | nullable: {nullable} | const_for_missing: {value_for_missing}\n')
                    if nullable and base_type not in ['VARCHAR', 'TEXT', 'CHAR']:
                        value_for_missing = 'NULL'
                    else:  # use const_for_missing_data
                        if base_type == 'VARCHAR':
                            if length == None:
                                value_for_missing = get_missing_for_string_length()
                            else:
                                value_for_missing = get_missing_for_string_length(length)
                        if base_type == 'CHAR':
                            if length == None:
                                value_for_missing = get_missing_for_string_length(1, True)
                            else:
                                value_for_missing = get_missing_for_string_length(length, True)
                        if value_for_missing == 'use_get_missing_number_for_digits_function':
                            if length == None:
                                value_for_missing = get_missing_number_for_digits(18)
                            else:
                                if scale == None or scale == '':
                                    value_for_missing = get_missing_number_for_digits(length)
                                else:
                                    value_for_missing = get_missing_number_for_digits(length - scale)

            values_for_missing.append(value_for_missing)

        insert_beginning = "INSERT INTO {}.{} (\n".format(schema_name,table_name)
        insert_beginning += ',\n'.join(column_names) + '\n) VALUES (\n'
        ghost_insert_null = '/* Ghost Record for delivered NULL Data */\n' + insert_beginning + ',\n'.join(values_for_null) + '\n);'
        ghost_insert_missing = '/* Ghost Record for missing data due to business rule */\n' + insert_beginning + ',\n'.join(
            values_for_missing) + '\n);'
        #print(values_for_missing) #debugging
        #print(ghost_insert_missing) #debugging

        sql_check=f"""select {table_key_column} 
                      from {schema_name}.{table_name}
                        where  {table_key_column}  in ('{HASHKEY_FOR_MISSING_DATA}','{HASHKEY_FOR_DELIVERED_NULL}')"""

        check_cursor=self._db_connection.cursor()
        check_cursor.execute(sql_check)
        check_row=check_cursor.fetchone()
        hks_found=[]
        while check_row:
            hks_found.append(check_row[0])
            check_row=check_cursor.fetchone()

        try:
            if HASHKEY_FOR_DELIVERED_NULL not in hks_found:
                print("\n\nddl_null:", ghost_insert_null)
                check_cursor.execute(ghost_insert_null)
            if HASHKEY_FOR_MISSING_DATA not in hks_found:
                print("\n\nddl_missing:", ghost_insert_missing)
                check_cursor.execute(ghost_insert_missing)
            self._db_connection.commit()
        except Error as err:
            print_snowflake_exception(err)
            raise



        return


    def _apply_table_grants_for_process_role(self, schema_name, table_name, is_stage=False):
        deploy_cursor = self._db_connection.cursor()
        grant_statement = None
        if is_stage:
            grant_statement = f"""GRANT SELECT, INSERT, DELETE, UPDATE, TRUNCATE ON TABLE {schema_name.lower()}.{table_name.lower()} TO {PROCESS_ROLE}"""
        else:
            grant_statement = f"""GRANT SELECT, INSERT, UPDATE ON TABLE {schema_name.lower()}.{table_name.lower()} TO {PROCESS_ROLE}"""
        print(grant_statement)
        deploy_cursor.execute(grant_statement)
        self._db_connection.commit()

    def _apply_table_grants_for_read_role(self, schema_name, table_name):
        deploy_cursor = self._db_connection.cursor()
        grant_statement = f"""GRANT SELECT ON TABLE {schema_name.lower()}.{table_name.lower()} TO {READ_ROLE}"""
        print(grant_statement)
        deploy_cursor.execute(grant_statement)
        self._db_connection.commit()


    def _apply_function_grants_for_process_role(self, schema_name, function_name):
        deploy_cursor = self._db_connection.cursor()
        grant_statement =  f"""GRANT USAGE ON FUNCTION {schema_name.lower()}.{function_name.lower()}() TO ROLE {PROCESS_ROLE}"""
        print(grant_statement)
        deploy_cursor.execute(grant_statement)
        self._db_connection.commit()

    def _apply_function_grants_for_read_role(self, schema_name, function_name):
        deploy_cursor = self._db_connection.cursor()
        grant_statement =  f"""GRANT USAGE ON FUNCTION {schema_name.lower()}.{function_name.lower()}() TO ROLE {READ_ROLE}"""
        print(grant_statement)
        deploy_cursor.execute(grant_statement)
        self._db_connection.commit()

    def _apply_view_grants_for_process_role(self, schema_name, view_name):
        deploy_cursor = self._db_connection.cursor()
        grant_statement = f"""GRANT SELECT ON VIEW {schema_name.lower()}.{view_name.lower()} TO {PROCESS_ROLE}"""
        print(grant_statement)
        deploy_cursor.execute(grant_statement)
        self._db_connection.commit()

    def _apply_view_grants_for_read_role(self, schema_name, view_name):
        deploy_cursor = self._db_connection.cursor()
        grant_statement = f"""GRANT SELECT ON VIEW {schema_name.lower()}.{view_name.lower()} TO {READ_ROLE}"""
        print(grant_statement)
        deploy_cursor.execute(grant_statement)
        self._db_connection.commit()


def demonstration_and_test_of_datamodeldeploymentmanager():
    from lib.connection_snf import connection_snf_for_dwh_connection_type

    my_connection = connection_snf_for_dwh_connection_type(Dvf_dwh_connection_type.owner)
    my_deployment_manager = Dvf_ddl_deploymentmanager_snf(my_connection)

    my_deployment_manager.deploy_stage_table("zz_rvlt_demo", "szz_source_demo_aaa_p1")
    my_deployment_manager.deploy_table("zz_rvlt_demo", "aaa_hub")
    my_deployment_manager.deploy_table("zz_rvlt_demo", "aaa_aaa_p1_sat")


if __name__ == '__main__':
    print('Standalone')
    demonstration_and_test_of_datamodeldeploymentmanager()
