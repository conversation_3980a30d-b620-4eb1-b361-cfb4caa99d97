Data vault pipeline developer cheat sheet 
rendered from  rsap_ocrd_j1.dvpi

pipeline name:  rsap_ocrd_j1

------------------------------------------------------
record source:  sap.ocrd

Source fields:
       COMPANY    Varchar(50)
       CARDCODE   VARCHAR(20)
       JSON_TEXT  VARCHAR


------------------------------------------------------
Table List:
stage_table.rvlt_sap.rsap_ocrd_j1_stage
table.rvlt_sap.rsap_business_partner_ocrd_hub
table.rvlt_sap.rsap_business_partner_ocrd_j1_l10_sat

------------------------------------------------------
stage table:  stage_rvlt.rsap_ocrd_j1_stage
Field to Stage mapping:
	--business keys,
		CARDCODE   >  CARDCODE,
		COMPANY    >  COMPANY,

	--content,
		JSON_TEXT  >  JSON_TEXT

------------------------------------------------------
Hash value composition

HK_RSAP_BUSINESS_PARTNER_OCRD (key)
		CARDCODE 
		COMPANY 

RH_RSAP_BUSINESS_PARTNER_OCRD_J1_L10_SAT (diff_hash)
		JSON_TEXT 


------------------------------------------------------
Table load method
(STAGE >  VAULT)

rsap_business_partner_ocrd_hub (/) can be loaded by convention
		  key: HK_RSAP_BUSINESS_PARTNER_OCRD  >  HK_RSAP_BUSINESS_PARTNER_OCRD
		  business_key: COMPANY  >  COMPANY 
		  business_key: CARDCODE  >  CARDCODE 

rsap_business_partner_ocrd_j1_l10_sat (/) can be loaded by convention
		  parent_key: HK_RSAP_BUSINESS_PARTNER_OCRD  >  HK_RSAP_BUSINESS_PARTNER_OCRD
		  diff_hash: RH_RSAP_BUSINESS_PARTNER_OCRD_J1_L10_SAT  >  RH_RSAP_BUSINESS_PARTNER_OCRD_J1_L10_SAT
		  content: JSON_TEXT  >  JSON_TEXT 

