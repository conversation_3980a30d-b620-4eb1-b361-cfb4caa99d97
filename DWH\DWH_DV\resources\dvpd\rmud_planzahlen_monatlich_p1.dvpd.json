{
	/* DVPD版本号，用于确保配置文件与处理框架的兼容性 */
	"dvpd_version": "0.6.2",
	/* 定义数据加载的暂存区属性，包括模式名和表名 */
	/* 此处指定了专用的暂存表，用于临时存储月度计划数据，便于后续处理和转换 */
	"stage_properties" : [{"stage_schema":"stage_rvlt","stage_table_name":"rmud_planzahlen_monatlich_p1_stage"}],
	/* 定义数据管道名称，用于月度计划数据处理 */
	/* 此管道专门处理月度计划数据，是财务规划系统的重要组成部分 */
	"pipeline_name": "rmud_planzahlen_monatlich_p1",
	/* 定义数据来源标识，表明这是手工输入的月度计划数据 */
	/* 数据来源为手动输入的月度计划数据，通常由财务部门提供 */
	"record_source_name_expression": "manuell.planzahlen.monatlich",
	/* 数据提取配置，这里仅用于DDL生成，不执行实际提取 */
	/* 实际的数据提取逻辑在相应的Python模块中实现，此处仅用于生成数据结构 */
	"data_extraction": {
		"fetch_module_name":"none - ddl generation only"
	},

	/* 字段映射部分：定义源系统字段如何映射到Data Vault模型中的表 */
	/* 此部分定义了月度计划数据的字段结构及其在Data Vault模型中的映射关系 */
	"fields": [
		      /* 年份字段，作为链接表的业务键之一 */
		      /* 年份是时间维度的重要组成部分，与月份一起构成月度计划的时间标识 */
		      {"field_name": "jahr", 	"field_type": "NUMBER(20,0)",	"targets": [{"table_name": "rmud_rgnr_gesellschaft_monatsplanzahl_dlnk"}]},
		      /* 月份字段，作为链接表的业务键之一，与年份一起确定时间维度 */
		      /* 月份值范围为1-12，与年份共同构成月度计划的完整时间标识 */
		      {"field_name": "monat", 	"field_type": "NUMBER(20,0)",	"targets": [{"table_name": "rmud_rgnr_gesellschaft_monatsplanzahl_dlnk"}]},
		      /* 公司字段，映射到公司Hub表，是识别公司实体的业务键 */
		      /* 此字段关联到通用的公司Hub表，实现了与公司维度的集成，便于跨部门数据分析 */
		      {"field_name": "firma", 	"field_type": "Varchar(50)",	"targets": [{"table_name": "rgnr_gesellschaft_hub","column_name":"company"}]},
		 	  /* 计划数值字段，存储在多活动卫星表中 */
		 	  /* 计划数值字段，存储实际的月度计划金额，是核心业务指标 */
		 	  {"field_name": "planzahl",	"field_type": "NUMBER(20,2)",	"targets": [{"table_name": "rmud_rgnr_gesellschaft_monatsplanzahl_p1_l10_msat"}]},
			  /* 子分组JSON字段，存储额外的分类信息，如部门、产品线等 */
			  /* 使用JSON格式存储灵活的分类数据，支持多维度分析而无需更改表结构 */
			  {"field_name": "untergruppe_json",	"field_type": "VARCHAR",	"targets": [{"table_name": "rmud_rgnr_gesellschaft_monatsplanzahl_p1_l10_msat"}]}
			 ],
	/* Data Vault模型定义部分：定义目标数据库中的表结构和关系 */
	/* 此模型实现了月度计划数据的存储结构，与年度计划数据模型相互补充，共同构成完整的财务规划体系 */
	"data_vault_model": [
		/* 通用模式下的表定义 - 这些表存储在通用数据仓库模式中，可被多个业务领域共享使用 */
		{"schema_name": "rvlt_general", 
		 "tables": [
				/* 公司Hub表定义：存储公司实体的业务键和哈希键 */
				/* 这是一个核心Hub表，被多个业务流程共用，包括计划数据、日历数据等 */
				{"table_name": "rgnr_gesellschaft_hub",
				"table_stereotype": "hub",
				"hub_key_column_name": "HK_RGNR_GESELLSCHAFT"}
				]
		},
		/* 手工数据模式下的表定义 - 这些表专门存储手工输入的数据，如计划数据、预算等 */
		{"schema_name": "rvlt_manual_data", 
		 "tables": [
				/* 月度计划数据链接表：连接公司、年份和月份 */
				/* 这是一个依赖链接表(Dependent Link)，包含年份和月份作为业务键的一部分 */
				/* 使用依赖链接表而非标准链接表，是因为年月不是独立的业务实体，而是计划数据的属性 */
				{"table_name": "rmud_rgnr_gesellschaft_monatsplanzahl_dlnk",	
				"table_stereotype": "lnk",
				"link_parent_tables": ["rgnr_gesellschaft_hub"]
				},
				/* 月度计划数据多活动卫星表：存储计划数值和子分组信息 */
				/* 使用多活动卫星表(Multi-Active Satellite)存储计划值，支持同一公司在同一年月有多个计划值 */
				/* 与rmud_planzahlen_jaehrlich_p1.dvpd.json中的年度计划表结构类似，但增加了月份维度 */
				{"table_name": "rmud_rgnr_gesellschaft_monatsplanzahl_p1_l10_msat",
				"table_stereotype": "sat",
				/* 多活动标志表示一个父记录可以有多个有效的卫星记录，适用于计划数据的多版本管理 */
				/* 例如，同一公司在同一年月可能有初始计划、修订计划等多个版本 */
				"is_multiactive":"true",
				"satellite_parent_table": "rmud_rgnr_gesellschaft_monatsplanzahl_dlnk",
				/* 差异哈希列用于检测记录变化，支持历史数据追踪和变更管理 */
				/* 命名前缀GH表示这是一个全局哈希值，用于在整个数据仓库中唯一标识此记录的内容 */
				"diff_hash_column_name": "GH_RMUD_RGNR_GESELLSCHAFT_MONATSPLANZAHL_P1_L10_MSAT"}

				]
		}
	]
}