create or replace view bvlt_billbee.bbbe_order_item_product_korrigiert_v1_b10_current_sat as 

with order_summierte_orderpositionen as (
		select item_order_l.hk_rbbe_order,
			count_if( item_prod_l.hk_rbbe_product<>'0000000000000000000000000000') 	    as product_item_count,
			count(1) 																	as order_item_count,
			sum(case when item_prod_l.hk_rbbe_product='0000000000000000000000000000' 
					then item_s.totalprice
					else 0
					end) 																as  zusatz_totalprice_sum,
			sum(case when item_prod_l.hk_rbbe_product='0000000000000000000000000000' 
					then item_s.taxamount 
					else 0
					end) 																as  zusatz_taxamount_sum,			
			sum(case when item_prod_l.hk_rbbe_product<>'0000000000000000000000000000' 
					then item_s.totalprice
					else 0
					end) 																as  product_totalprice_sum,
			sum(case when item_prod_l.hk_rbbe_product<>'0000000000000000000000000000' 
					then item_s.taxamount 
					else 0
					end) 																as product_taxamount_sum			
		from RVLT_BILLBEE.RBBE_ORDER_ITEM_ORDER_LNK item_order_l
		join RVLT_BILLBEE.RBBE_ORDER_ITEM_ORDER_ESAT item_order_e
			on item_order_e.lk_rbbe_order_item_order =item_order_l.lk_rbbe_order_item_order 
			and item_order_e.md_valid_before =lib.dwh_far_future_date()
			and not item_order_e.md_is_deleted		
		join RVLT_BILLBEE.RBBE_ORDER_ITEM_ORDER_P1_L10_SAT	item_s
			on item_s.hk_rbbe_order_item = item_order_l.hk_rbbe_order_item 
			and item_s.md_valid_before =lib.dwh_far_future_date()
			and not item_s.md_is_deleted
		join RVLT_BILLBEE.RBBE_ORDER_ITEM_PRODUCT_LNK item_prod_l
			on item_prod_l.hk_rbbe_order_item = item_order_l.hk_rbbe_order_item 
		join RVLT_BILLBEE.RBBE_ORDER_ITEM_PRODUCT_ESAT item_prod_e
		  on item_prod_e.lk_rbbe_order_item_product = item_prod_l.lk_rbbe_order_item_product 
			and item_prod_e.md_valid_before =lib.dwh_far_future_date()
			and not item_prod_e.md_is_deleted	
		group by 1
		)		
,order_mit_zusatzkosten as (
select order_s.hk_rbbe_order
		,order_sumpos.zusatz_totalprice_sum+order_s.shippingcost   											as zu_verteilender_wert_brutto
		,order_sumpos.zusatz_totalprice_sum-order_sumpos.zusatz_taxamount_sum+order_s.shippingcost/1.19   	as zu_verteilender_wert_netto
		,order_sumpos.product_totalprice_sum 	 															
		,order_s.shippingcost
		,order_sumpos.product_item_count
		,order_sumpos.order_item_count
from order_summierte_orderpositionen order_sumpos
		join RVLT_BILLBEE.RBBE_ORDER_ORDER_P1_L10_SAT   order_s
			   on order_s.hk_rbbe_order =order_sumpos.hk_rbbe_order 
			   and order_s.md_valid_before =lib.dwh_far_future_date()
			   and not order_s.md_is_deleted 
  ) 		
-- ------------ Finales Dataset ------------------
-- Order items mit produkten und einem preis>0
select 	item_order_l.hk_rbbe_order_item 
		,item_s.totalprice-item_s.taxamount totalprice_netto
		,omzk.zu_verteilender_wert_netto
		,item_s.totalprice/omzk.product_totalprice_sum							as product_verteilschluessel     
		,item_s.totalprice - item_s.taxamount + omzk.zu_verteilender_wert_netto* (item_s.totalprice/omzk.product_totalprice_sum) as totalprice_netto_korrigiert
		,item_order_l.hk_rbbe_order as hk_rbbe_order___FOR_DEBUG_ONLY
from order_mit_zusatzkosten omzk
	join DV_D_MAIN_DATABASE.RVLT_BILLBEE.RBBE_ORDER_ITEM_ORDER_LNK item_order_l
		on item_order_l.hk_rbbe_order = omzk.hk_rbbe_order
	join DV_D_MAIN_DATABASE.RVLT_BILLBEE.RBBE_ORDER_ITEM_ORDER_ESAT item_order_e
		on item_order_e.lk_rbbe_order_item_order =item_order_l.lk_rbbe_order_item_order 
		and item_order_e.md_valid_before =lib.dwh_far_future_date()
		and not item_order_e.md_is_deleted
	join DV_D_MAIN_DATABASE.RVLT_BILLBEE.RBBE_ORDER_ITEM_ORDER_P1_L10_SAT	item_s
		on item_s.hk_rbbe_order_item = item_order_l.hk_rbbe_order_item 
		and item_s.md_valid_before =lib.dwh_far_future_date()
		and not item_s.md_is_deleted
	join RVLT_BILLBEE.RBBE_ORDER_ITEM_PRODUCT_LNK item_prod_l
		on item_prod_l.hk_rbbe_order_item = item_order_l.hk_rbbe_order_item 
	join RVLT_BILLBEE.RBBE_ORDER_ITEM_PRODUCT_ESAT item_prod_e
	  on item_prod_e.lk_rbbe_order_item_product = item_prod_l.lk_rbbe_order_item_product 
		and item_prod_e.md_valid_before =lib.dwh_far_future_date()
		and not item_prod_e.md_is_deleted	
where omzk.product_item_count>0 -- es müssen items mit product im order  sein
	   and omzk.product_totalprice_sum<>0	-- Produkte müssen insgesamt geld gekostet haben
       and item_prod_l.hk_rbbe_product<>'0000000000000000000000000000' -- items die kein Produkt sind, werden rausgehalten
union all
-- Order items mit produkten und einem preis=0
select 	item_order_l.hk_rbbe_order_item
		,item_s.totalprice-item_s.taxamount totalprice_netto
		,omzk.zu_verteilender_wert_netto
		,1/omzk.product_item_count									as product_verteilschluessel     
		,item_s.totalprice - item_s.taxamount + omzk.zu_verteilender_wert_netto/omzk.product_item_count as totalprice_netto_korrigiert
		,item_order_l.hk_rbbe_order as hk_rbbe_order___FOR_DEBUG_ONLY
from order_mit_zusatzkosten omzk
	join DV_D_MAIN_DATABASE.RVLT_BILLBEE.RBBE_ORDER_ITEM_ORDER_LNK item_order_l
		on item_order_l.hk_rbbe_order = omzk.hk_rbbe_order
	join DV_D_MAIN_DATABASE.RVLT_BILLBEE.RBBE_ORDER_ITEM_ORDER_ESAT item_order_e
		on item_order_e.lk_rbbe_order_item_order =item_order_l.lk_rbbe_order_item_order 
		and item_order_e.md_valid_before =lib.dwh_far_future_date()
		and not item_order_e.md_is_deleted
	join DV_D_MAIN_DATABASE.RVLT_BILLBEE.RBBE_ORDER_ITEM_ORDER_P1_L10_SAT	item_s
		on item_s.hk_rbbe_order_item = item_order_l.hk_rbbe_order_item 
		and item_s.md_valid_before =lib.dwh_far_future_date()
		and not item_s.md_is_deleted
	join RVLT_BILLBEE.RBBE_ORDER_ITEM_PRODUCT_LNK item_prod_l
		on item_prod_l.hk_rbbe_order_item = item_order_l.hk_rbbe_order_item 
	join RVLT_BILLBEE.RBBE_ORDER_ITEM_PRODUCT_ESAT item_prod_e
	  on item_prod_e.lk_rbbe_order_item_product = item_prod_l.lk_rbbe_order_item_product 
		and item_prod_e.md_valid_before =lib.dwh_far_future_date()
		and not item_prod_e.md_is_deleted	
where omzk.product_item_count>0 -- es müssen items mit product im order  sein
	   and omzk.product_totalprice_sum=0	-- Produkte haben kein geld gekostet
       and item_prod_l.hk_rbbe_product<>'0000000000000000000000000000' -- items die kein Produkt sind, werden rausgehalten 
order by 1,2