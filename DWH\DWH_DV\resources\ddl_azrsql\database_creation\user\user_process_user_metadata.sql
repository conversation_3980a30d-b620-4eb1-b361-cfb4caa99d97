-- Ben<PERSON>er
-- Auf master

CREATE LOGIN process_user_metadata
WITH PASSWORD = 'cimt!ObjectS:2023'; -- Nur Ein Beispiel für ein Passwort

create user process_user_metadata for login process_user_metadata with default_schema=[metadata];
alter user process_user_metadata with default_schema=[metadata]; -- falls der User schon erstellt wurde

-- Auf jobinstanceframework

CREATE USER process_user_metadata
FOR LOGIN process_user_metadata;

ALTER USER process_user_metadata
WITH DEFAULT_SCHEMA = metadata;

EXEC sp_addrolemember N'db_datawriter ', N'process_user_metadata'
EXEC sp_addrolemember N'db_datareader ', N'process_user_metadata'
-- EXEC sp_addrolemember N'db_ddladmin ', N'process_user_metadata' -- nur wenn aktiv Tabellen erzeugt werden sollen, was in der Regel nicht der Fall ist


 




 