CREATE VIEW bvlt_sap.bsap_item_group_oitb_current_ref AS
SELECT oitb_s.* exclude(MD_RUN_ID, MD_IS_DELETED,MD_VALID_BEFORE,HK_RSAP_ITEM_GROUP_OITB,RH_RSAP_ITEM_GROUP_OITB_J1_L10_SAT),
        oitb_h.company,
        oitb_h.itmsgrpcod
FROM RVLT_SAP.rsap_item_group_oitb_hub oitb_h
JOIN RVLT_SAP.rsap_item_group_oitb_p1_l20_sat oitb_s ON oitb_h.HK_RSAP_ITEM_GROUP_OITB = oitb_s.HK_RSAP_ITEM_GROUP_OITB
    AND oitb_s.md_valid_before = lib.dwh_far_future_date()
    AND NOT oitb_s.md_is_deleted
    AND oitb_s.MD_RECORD_SOURCE <> 'SYSTEM';