# 数据仓库项目指南：非技术人员版（第2部分：Data Vault建模）

## Data Vault建模详解

在第一部分中，我们简要介绍了Data Vault的三个基本组件：Hub、Link和Satellite。现在，让我们更详细地了解这些组件，并通过实例来说明它们如何工作。

### Hub（中心）：业务核心实体

Hub表示业务中的核心实体，如客户、产品、订单等。每个Hub包含：
- 一个唯一标识符（哈希键）
- 业务键（如订单号、客户ID等）
- 元数据（如记录来源、加载时间等）

**示例**：在我们的项目中，`RSAP_INVOICE_OINV_HUB`是一个Hub表，代表SAP系统中的发票实体。它包含以下字段：
- `HK_RSAP_INVOICE_OINV`：唯一标识符（哈希键）
- `COMPANY`：公司代码（业务键的一部分）
- `DOCENTRY`：文档编号（业务键的一部分）
- `MD_RECORD_SOURCE`：数据来源
- `MD_INSERTED_AT`：数据加载时间

### Link（链接）：实体之间的关系

Link表示不同实体之间的关系。每个Link包含：
- 一个唯一标识符（哈希键）
- 指向相关Hub的引用（外键）
- 元数据（如记录来源、加载时间等）

**示例**：在我们的项目中，`RSAP_INVOICE_LINE_INV1_ITEM_OITM_LNK`是一个Link表，表示发票行与物料之间的关系。它包含以下字段：
- `LK_RSAP_INVOICE_LINE_INV1_ITEM_OITM`：唯一标识符（哈希键）
- `HK_RSAP_INVOICE_LINE_INV1`：指向发票行Hub的引用
- `HK_RSAP_ITEM_OITM`：指向物料Hub的引用
- `MD_RECORD_SOURCE`：数据来源
- `MD_INSERTED_AT`：数据加载时间

### Satellite（卫星）：描述性信息和历史变化

Satellite存储与Hub或Link相关的描述性信息，并跟踪这些信息随时间的变化。每个Satellite包含：
- 指向相关Hub或Link的引用（外键）
- 描述性属性（如名称、金额、日期等）
- 差异哈希值（用于检测变化）
- 有效期（开始和结束时间）
- 元数据（如记录来源、加载时间等）

**示例**：在我们的项目中，`RSAP_INVOICE_LINE_INV1_J1_L10_SAT`是一个Satellite表，存储发票行的详细信息。它包含以下字段：
- `HK_RSAP_INVOICE_LINE_INV1`：指向发票行Hub的引用
- `JSON_TEXT`：包含发票行详细信息的JSON文本
- `RH_RSAP_INVOICE_LINE_INV1_J1_L10_SAT`：差异哈希值
- `MD_VALID_BEFORE`：有效期结束时间
- `MD_RECORD_SOURCE`：数据来源
- `MD_INSERTED_AT`：数据加载时间

## 以发票数据为例解释Data Vault模型

让我们以SAP发票数据为例，说明Data Vault模型如何组织数据。下图显示了发票相关的部分Data Vault模型：

![发票数据模型](../documentation/bvlt_sap_invoice_credit_memo.drawio.png)

### 模型解释

在这个模型中：

1. **Hub表**：
   - `RSAP_INVOICE_LINE_INV1_HUB`：代表发票行
   - `RSAP_ITEM_OITM_HUB`：代表物料

2. **Link表**：
   - `RSAP_INVOICE_LINE_INV1_INV2_LNK`：连接发票行与其扩展信息
   - `RSAP_INVOICE_LINE_INV1_ITEM_OITM_LNK`：连接发票行与物料
   - `RSAP_INVOICE_LINE_INV1_INVOICE_OINV_LNK`：连接发票行与发票头

3. **Satellite表**：
   - `RSAP_INVOICE_LINE_INV1_J1_L10_SAT`：存储发票行的原始JSON数据
   - `RSAP_INVOICE_LINE_INV1_P1_L20_SAT`：存储发票行的处理后数据

### 虚拟数据示例

为了更好地理解，让我们看一些虚拟数据示例：

**RSAP_INVOICE_LINE_INV1_HUB（发票行Hub）**：
| HK_RSAP_INVOICE_LINE_INV1 | COMPANY | DOCENTRY | LINENUM | MD_RECORD_SOURCE | MD_INSERTED_AT |
|---------------------------|---------|----------|---------|------------------|----------------|
| a1b2c3d4e5f6g7h8i9j0      | DEISS_DE| 1001     | 1       | sap.inv1         | 2024-05-19 10:00:00 |
| b2c3d4e5f6g7h8i9j0k1      | DEISS_DE| 1001     | 2       | sap.inv1         | 2024-05-19 10:00:00 |
| c3d4e5f6g7h8i9j0k1l2      | BINGOLD_DE| 2001   | 1       | sap.inv1         | 2024-05-19 10:05:00 |

**RSAP_INVOICE_LINE_INV1_J1_L10_SAT（发票行Satellite）**：
| HK_RSAP_INVOICE_LINE_INV1 | JSON_TEXT | RH_RSAP_INVOICE_LINE_INV1_J1_L10_SAT | MD_VALID_BEFORE | MD_RECORD_SOURCE | MD_INSERTED_AT |
|---------------------------|-----------|--------------------------------------|-----------------|------------------|----------------|
| a1b2c3d4e5f6g7h8i9j0      | {"LineTotal": 100.00, "Currency": "EUR", "OcrCode": "SALES"} | d4e5f6g7h8i9 | 9999-12-31 | sap.inv1 | 2024-05-19 10:00:00 |
| b2c3d4e5f6g7h8i9j0k1      | {"LineTotal": 50.00, "Currency": "EUR", "OcrCode": "SALES"} | e5f6g7h8i9j0 | 9999-12-31 | sap.inv1 | 2024-05-19 10:00:00 |
| c3d4e5f6g7h8i9j0k1l2      | {"LineTotal": 75.00, "Currency": "EUR", "OcrCode": "MARKETING"} | f6g7h8i9j0k1 | 9999-12-31 | sap.inv1 | 2024-05-19 10:05:00 |

通过这种方式，Data Vault模型可以：
1. 保持业务实体的完整性（通过Hub）
2. 跟踪实体之间的关系（通过Link）
3. 存储详细信息并保留历史变化（通过Satellite）

在下一部分中，我们将探讨如何使用DVPD（Data Vault Pipeline Definition）和DVPI（Data Vault Pipeline Implementation）来自动化数据加载过程。
