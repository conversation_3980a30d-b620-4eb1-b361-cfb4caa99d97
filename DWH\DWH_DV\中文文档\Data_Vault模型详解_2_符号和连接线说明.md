# Data Vault模型详解：符号和连接线说明

## 引言

本文档专门解释Data Vault模型图中使用的各种符号和连接线的含义，帮助非技术人员理解图表中的视觉元素代表什么。我们将以提供的完整图示为例，详细说明每种符号和连接线的用途。

## 图形符号详解

### 1. 表类型符号

在Data Vault模型中，不同类型的表使用不同的形状和颜色表示：

#### 1.1 Hub表（深蓝色椭圆/六边形）

[![Hub表示例](../documentation/rvlt_sap.drawio.png)](../documentation/rvlt_sap.drawio.png#:~:text=invoice_line_inv1)

- **含义**：代表业务核心实体，如客户、产品、订单等
- **特点**：
  - 深蓝色填充
  - 椭圆形或六边形形状
  - 通常位于模型的中心位置
- **示例**：图中的[`invoice_line_inv1`](../documentation/rvlt_sap.drawio.png#:~:text=invoice_line_inv1)、[`invoice_oinv`](../documentation/rvlt_sap.drawio.png#:~:text=invoice_oinv)、[`item_oitm`](../documentation/rvlt_sap.drawio.png#:~:text=item_oitm)、[`business_partner_ocrd`](../documentation/rvlt_sap.drawio.png#:~:text=business_partner_ocrd)
- **作用**：存储业务实体的唯一标识符（业务键）

#### 1.2 Link表（浅蓝色椭圆/六边形）

[![Link表示例](../documentation/rvlt_sap.drawio.png)](../documentation/rvlt_sap.drawio.png#:~:text=invoice_line_inv1_invoice)

- **含义**：代表实体之间的关系
- **特点**：
  - 浅蓝色填充
  - 椭圆形或六边形形状
  - 通常连接两个或多个Hub表
- **示例**：图中的[`invoice_line_inv1_invoice`](../documentation/rvlt_sap.drawio.png#:~:text=invoice_line_inv1_invoice)、[`invoice_line_inv1_inv2`](../documentation/rvlt_sap.drawio.png#:~:text=invoice_line_inv1_inv2)、[`invoice_line_inv1_item_oitm`](../documentation/rvlt_sap.drawio.png#:~:text=invoice_line_inv1_item_oitm)
- **作用**：记录不同业务实体之间的关联关系

#### 1.3 Satellite表（黄色矩形）

[![Satellite表示例](../documentation/rvlt_sap.drawio.png)](../documentation/rvlt_sap.drawio.png#:~:text=invoice_line_inv1_j1_l10)

- **含义**：代表描述性信息和历史变化
- **特点**：
  - 黄色填充
  - 矩形形状
  - 通常通过虚线连接到Hub或Link表
- **示例**：图中的[`invoice_line_inv1_j1_l10`](../documentation/rvlt_sap.drawio.png#:~:text=invoice_line_inv1_j1_l10)、[`invoice_line_inv1_p1_l20`](../documentation/rvlt_sap.drawio.png#:~:text=invoice_line_inv1_p1_l20)、[`invoice_oinv_j1_l10`](../documentation/rvlt_sap.drawio.png#:~:text=invoice_oinv_j1_l10)
- **作用**：存储实体的属性和描述性信息，并跟踪这些信息随时间的变化

#### 1.4 参考表/辅助表（绿色矩形）

[![参考表示例](../documentation/rvlt_sap.drawio.png)](../documentation/rvlt_sap.drawio.png#:~:text=green)

- **含义**：代表参考数据或辅助信息
- **特点**：
  - 绿色填充
  - 矩形形状
  - 通常位于模型的边缘
- **示例**：图底部的绿色矩形区域
- **作用**：提供静态数据或代码值，支持主要业务实体

### 2. 连接线类型

Data Vault模型中使用不同类型的连接线表示表之间的关系：

#### 2.1 实线连接

[![实线连接示例](../documentation/rvlt_sap.drawio.png)](../documentation/rvlt_sap.drawio.png#:~:text=invoice_line_inv1_invoice)

- **含义**：表示强关系，必须的业务关系
- **特点**：
  - 实线
  - 通常连接Hub和Link表
- **示例**：[`invoice_line_inv1`与`invoice_line_inv1_invoice`之间的连接](../documentation/rvlt_sap.drawio.png#:~:text=invoice_line_inv1_invoice)
- **作用**：表示一个实体必须与另一个实体相关联

#### 2.2 虚线连接

[![虚线连接示例](../documentation/rvlt_sap.drawio.png)](../documentation/rvlt_sap.drawio.png#:~:text=invoice_line_inv1_j1_l10)

- **含义**：表示包含关系，描述性信息
- **特点**：
  - 虚线
  - 通常连接Hub/Link与其Satellite表
- **示例**：[`invoice_line_inv1`与`invoice_line_inv1_j1_l10`之间的连接](../documentation/rvlt_sap.drawio.png#:~:text=invoice_line_inv1_j1_l10)
- **作用**：表示Satellite表包含了Hub/Link表的描述性信息

#### 2.3 虚线框/分组

[![虚线框示例](../documentation/rvlt_sap.drawio.png)](../documentation/rvlt_sap.drawio.png)

- **含义**：表示逻辑分组，相关表的集合
- **特点**：
  - 虚线矩形框
  - 包含多个相关的表
- **示例**：左侧包含[`invoice_line_inv1_inv2`](../documentation/rvlt_sap.drawio.png#:~:text=invoice_line_inv1_inv2)相关表的虚线框
- **作用**：将相关的表组合在一起，表示它们属于同一业务领域或数据源

## 命名约定解释

Data Vault模型中的表名遵循特定的命名约定，帮助识别表的类型和用途：

### 1. 表名前缀（在实际数据库中使用）

- **RVLT_**：Raw Vault，原始保管库
- **BVLT_**：Business Vault，业务保管库
- **MART_**：Data Mart，数据集市

### 2. 表名中的系统标识

- **RSAP_**：来自SAP系统的数据
- **RBBE_**：来自Billbee系统的数据
- **RMUD_**：手动输入的数据
- **RGNR_**：通用数据

### 3. 表名后缀

- **_HUB**：Hub表
- **_LNK**：Link表
- **_SAT**：Satellite表
- **_ESAT**：Effectivity Satellite表（记录关系的有效性）
- **_MSAT**：Multi-active Satellite表（允许多个活动记录）

### 4. Satellite表的特殊后缀

- **_j1_l10**：Load Layer 10的JSON数据（原始数据）
- **_p1_l20**：Load Layer 20的处理后数据（结构化数据）
- **_v1_b10**：Business Vault中的转换数据

## 图中的区域划分

提供的完整图示可以划分为几个主要区域，每个区域代表不同的业务领域：

### 1. 发票行及其扩展区域（左侧）

- **核心实体**：`invoice_line_inv1`
- **相关表**：`invoice_line_inv1_inv2`及其Satellite表
- **业务含义**：表示发票行及其扩展信息（如折扣、费用等）

### 2. 发票行与发票头关系区域（中央）

- **核心关系**：`invoice_line_inv1_invoice`
- **相关表**：`invoice_oinv`及其Satellite表
- **业务含义**：表示发票头包含多个发票行的关系

### 3. 发票行与物料关系区域（右下）

- **核心关系**：`invoice_line_inv1_item_oitm`
- **相关表**：`item_oitm`及其Satellite表
- **业务含义**：表示发票行引用特定物料的关系

### 4. 发票头与业务伙伴关系区域（右上）

- **核心关系**：`invoice_oinv_business_partner_ocrd`
- **相关表**：`business_partner_ocrd`及其Satellite表
- **业务含义**：表示发票头关联到特定业务伙伴的关系

## 为什么使用这种表示方法？

Data Vault模型使用这种图形表示法有几个重要原因：

### 1. 视觉区分

- 不同颜色和形状帮助快速识别表的类型
- 连接线类型清晰表示关系的性质

### 2. 结构清晰

- 虚线框分组相关表，减少视觉复杂性
- Hub表位于中心位置，突出核心业务实体

### 3. 关系表达

- 实线表示强关系，虚线表示描述性关系
- 这种区分帮助理解数据结构的逻辑

### 4. 业务视角

- 图形布局反映业务实体之间的自然关系
- 便于业务人员理解数据模型

## 如何阅读这种图表

对于非技术人员，以下是阅读Data Vault模型图的建议步骤：

1. **识别核心实体**：首先关注深蓝色的Hub表，这些代表核心业务实体
2. **了解实体关系**：查看浅蓝色的Link表，了解实体之间如何关联
3. **探索描述性信息**：查看黄色的Satellite表，了解每个实体的详细属性
4. **注意分组**：观察虚线框分组，理解相关表的业务领域
5. **跟随数据流**：想象数据如何从一个表流向另一个表，形成完整的业务流程

## 总结

Data Vault模型图使用丰富的视觉元素来表示复杂的数据结构和关系。通过理解这些符号和连接线的含义，非技术人员可以更好地理解数据仓库的结构，从而更有效地利用数据进行业务分析和决策。
