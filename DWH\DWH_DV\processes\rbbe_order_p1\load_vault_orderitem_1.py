import json

from lib.json_utils import get_json_text_content_column, sort_json_content_column
from lib.blobstorage_utils import fetch_json_source_file_from_blob_container
from lib.cimtjobinstance import CimtJobInstance, cimtjobinstance_job
from lib.connection_snf import  connection_snf_for_dwh_connection_type

from lib.dvf_basics import dvf_assemble_datavault_hash, Dvf_dwh_connection_type
from lib.dvf_sqlByConvention_snf import dvf_get_datavault_hub_elt_sql, \
    dvf_execute_elt_statement_list, dvf_get_check_hash_collision_hub_elt_sql, dvf_get_check_singularity_sat_elt_sql, \
    dvf_get_datavault_lnk_elt_sql, dvf_get_check_hash_collision_lnk_elt_sql, dvf_get_datavault_esat_elt_sql,dvf_get_datavault_sat_elt_sql

from lib.dbutils_snf import get_snf_dict_insert_sql, execute_snf_dict_bulk_insert





@cimtjobinstance_job
def stage_data(data, parent_job_instance, dwh_connection, **kwargs):

    my_job_instance = CimtJobInstance(kwargs['instance_job_name'], parent_job_instance)
    my_job_instance.start_instance()

    try:
        # ### FRAMEWORK PHASE: do processing here

        # prepare stage insert operation
        dwh_cursor = dwh_connection.cursor()
        dwh_cursor.execute("TRUNCATE TABLE stage_rvlt.rbbe_order_orderitem_p1_stage")
        insert_statement = get_snf_dict_insert_sql(dwh_connection, "stage_rvlt", "rbbe_order_orderitem_p1_stage")
        stage_data_row = {'md_inserted_at': my_job_instance.get_job_started_at().isoformat(),
                    'md_record_source': 'billbee.order.orderItem',
                    'md_run_id': my_job_instance.get_job_instance_id(),
                    'md_is_deleted': False
                   }

        # transform  and stage the source rows into stage
        source_rows = data

        stage_data_rows = []


        for source_row in source_rows:
            source_row_order_items = source_row.get('OrderItems')
            for source_row_order_item in source_row_order_items:
                stage_data_row_temp = dict(stage_data_row)
                stage_data_row_temp['billbeeorderid'] = source_row.get('BillBeeOrderId')
                stage_data_row_temp['billbeeid'] = source_row_order_item.get('BillbeeId')
                stage_data_row_temp['product_billbeeid'] = source_row_order_item.get('Product').get('BillbeeId')
                stage_data_row_temp['quantity'] = source_row_order_item.get('Quantity')
                stage_data_row_temp['totalprice'] = source_row_order_item.get('TotalPrice')
                stage_data_row_temp['taxamount'] = source_row_order_item.get('TaxAmount')
                stage_data_row_temp['taxindex'] = source_row_order_item.get('TaxIndex')
                stage_data_row_temp['discount'] = source_row_order_item.get('Discount')
                stage_data_row_temp['unrebatedtotalprice'] = source_row_order_item.get('UnrebatedTotalPrice')
                stage_data_row_temp['invoicesku'] = source_row_order_item.get('InvoiceSKU')
                stage_data_row_temp['product_title'] = source_row_order_item.get('Product').get('Title')
                stage_data_row_temp['product_sku'] = source_row_order_item.get('Product').get('SKU')
                stage_data_row_temp['product_ean'] = source_row_order_item.get('Product').get('EAN')
                stage_data_row_temp['product_countryoforigin'] = source_row_order_item.get('Product').get('CountryOfOrigin')
                stage_data_row_temp['product_tariccode'] = source_row_order_item.get('Product').get('TARICCode')
                stage_data_row_temp['lastmodifiedat'] = source_row.get('LastModifiedAt')


                ### hashes
                stage_data_row_temp['hk_rbbe_order'] = dvf_assemble_datavault_hash([stage_data_row_temp['billbeeorderid']])
                stage_data_row_temp['hk_rbbe_order_item'] = dvf_assemble_datavault_hash([stage_data_row_temp['billbeeid']])
                stage_data_row_temp['hk_rbbe_product'] = dvf_assemble_datavault_hash([stage_data_row_temp['product_billbeeid']])
                stage_data_row_temp['lk_rbbe_order_item_order'] = dvf_assemble_datavault_hash([stage_data_row_temp['billbeeid'],stage_data_row_temp['billbeeorderid']])
                stage_data_row_temp['lk_rbbe_order_item_product'] = dvf_assemble_datavault_hash([stage_data_row_temp['billbeeid'],stage_data_row_temp['product_billbeeid']])
                stage_data_row_temp['rh_rbbe_order_item_order_p1_l10_sat'] = dvf_assemble_datavault_hash([stage_data_row_temp['discount'],
                                                                                                            stage_data_row_temp['invoicesku'],
                                                                                                            stage_data_row_temp['product_countryoforigin'],
                                                                                                            stage_data_row_temp['product_ean'],
                                                                                                            stage_data_row_temp['product_sku'],
                                                                                                            stage_data_row_temp['product_tariccode'],
                                                                                                            stage_data_row_temp['product_title'],
                                                                                                            stage_data_row_temp['quantity'],
                                                                                                            stage_data_row_temp['taxamount'],
                                                                                                            stage_data_row_temp['taxindex'],
                                                                                                            stage_data_row_temp['totalprice'],
                                                                                                            stage_data_row_temp['unrebatedtotalprice']])

                stage_data_rows.append(stage_data_row_temp)
                my_job_instance.count_input(1)

        execute_snf_dict_bulk_insert(dwh_cursor, insert_statement, stage_data_rows)
        dwh_connection.commit()

    # ### FRAMEWORK PHASE: Log any exception to instance
    except Exception:
        my_job_instance.end_instance_with_error(1, 'something went wrong')
        raise

    # # ### FRAMEWORK PHASE: End the instance normally
    my_job_instance.end_instance()

@cimtjobinstance_job
def load_data_to_vault(parent_job_instance, dwh_connection, file_name=None, **kwargs):

    # ### FRAMEWORK PHASE: setup your job_instance
    my_job_instance = CimtJobInstance(kwargs['instance_job_name'], parent_job_instance)
    my_job_instance.set_work_item(file_name)
    my_job_instance.start_instance()
    try:

        # ### FRAMEWORK PHASE: do processing here
# BEGIN LOAD DATA TO VLT PART
        # general constants
        stage_schema = 'stage_rvlt'
        stage_table = 'rbbe_order_orderitem_p1_stage'


        dwh_cursor = dwh_connection.cursor()


        hash_collision_check_statement_list = dvf_get_check_hash_collision_hub_elt_sql(vault_table='rbbe_order_item_hub',
        vault_schema='rvlt_billbee',
        stage_hk_column='HK_RBBE_ORDER_ITEM',
        stage_bk_column_list=['BILLBEEID'],
        db_connection=dwh_connection,
        stage_schema=stage_schema,
        stage_table=stage_table)

        dvf_execute_elt_statement_list(dwh_cursor, hash_collision_check_statement_list)

        statement_list = dvf_get_datavault_hub_elt_sql(vault_table='rbbe_order_item_hub',
        vault_schema='rvlt_billbee',
        stage_hk_column='HK_RBBE_ORDER_ITEM',
        stage_bk_column_list=['BILLBEEID'],
        db_connection=dwh_connection,
        stage_schema=stage_schema,
        stage_table=stage_table,
        meta_job_instance_id = my_job_instance.get_job_instance_id(),
        meta_inserted_at = my_job_instance.get_job_started_at() )

        dvf_execute_elt_statement_list(dwh_cursor, statement_list)

        # ----------


        hash_collision_check_statement_list = dvf_get_check_hash_collision_hub_elt_sql(vault_table='rbbe_order_hub',
        vault_schema='rvlt_billbee',
        stage_hk_column='HK_RBBE_ORDER',
        stage_bk_column_list=['BILLBEEORDERID'],
        db_connection=dwh_connection,
        stage_schema=stage_schema,
        stage_table=stage_table)

        dvf_execute_elt_statement_list(dwh_cursor, hash_collision_check_statement_list)

        statement_list = dvf_get_datavault_hub_elt_sql(vault_table='rbbe_order_hub',
        vault_schema='rvlt_billbee',
        stage_hk_column='HK_RBBE_ORDER',
        stage_bk_column_list=['BILLBEEORDERID'],
        db_connection=dwh_connection,
        stage_schema=stage_schema,
        stage_table=stage_table,
        meta_job_instance_id = my_job_instance.get_job_instance_id(),
        meta_inserted_at = my_job_instance.get_job_started_at() )

        dvf_execute_elt_statement_list(dwh_cursor, statement_list)

        # ----------


        hash_collision_check_statement_list = dvf_get_check_hash_collision_hub_elt_sql(vault_table='rbbe_product_hub',
        vault_schema='rvlt_billbee',
        stage_hk_column='HK_RBBE_PRODUCT',
        stage_bk_column_list=['PRODUCT_BILLBEEID'],
        db_connection=dwh_connection,
        stage_schema=stage_schema,
        stage_table=stage_table)

        dvf_execute_elt_statement_list(dwh_cursor, hash_collision_check_statement_list)

        statement_list = dvf_get_datavault_hub_elt_sql(vault_table='rbbe_product_hub',
        vault_schema='rvlt_billbee',
        stage_hk_column='HK_RBBE_PRODUCT',
        stage_bk_column_list=['PRODUCT_BILLBEEID'],
        db_connection=dwh_connection,
        stage_schema=stage_schema,
        stage_table=stage_table,
        meta_job_instance_id = my_job_instance.get_job_instance_id(),
        meta_inserted_at = my_job_instance.get_job_started_at() )

        dvf_execute_elt_statement_list(dwh_cursor, statement_list)

        # ----------


        hash_collision_check_statement_list = dvf_get_check_hash_collision_lnk_elt_sql(vault_table='rbbe_order_item_order_lnk',
        vault_schema='rvlt_billbee',
        stage_lk_column='LK_RBBE_ORDER_ITEM_ORDER',
        stage_hk_column_list=['HK_RBBE_ORDER', 'HK_RBBE_ORDER_ITEM'],
        db_connection=dwh_connection,
        stage_schema=stage_schema,
        stage_table=stage_table)

        dvf_execute_elt_statement_list(dwh_cursor, hash_collision_check_statement_list)

        statement_list = dvf_get_datavault_lnk_elt_sql(vault_table='rbbe_order_item_order_lnk',
        vault_schema='rvlt_billbee',
        stage_lk_column='LK_RBBE_ORDER_ITEM_ORDER',
        stage_hk_column_list=['HK_RBBE_ORDER', 'HK_RBBE_ORDER_ITEM'],
        db_connection=dwh_connection,
        stage_schema=stage_schema,
        stage_table=stage_table,
        meta_job_instance_id = my_job_instance.get_job_instance_id(),
        meta_inserted_at = my_job_instance.get_job_started_at() )

        dvf_execute_elt_statement_list(dwh_cursor, statement_list)

        # ----------


        hash_collision_check_statement_list = dvf_get_check_hash_collision_lnk_elt_sql(vault_table='rbbe_order_item_product_lnk',
        vault_schema='rvlt_billbee',
        stage_lk_column='LK_RBBE_ORDER_ITEM_PRODUCT',
        stage_hk_column_list=['HK_RBBE_ORDER_ITEM', 'HK_RBBE_PRODUCT'],
        db_connection=dwh_connection,
        stage_schema=stage_schema,
        stage_table=stage_table)

        dvf_execute_elt_statement_list(dwh_cursor, hash_collision_check_statement_list)

        statement_list = dvf_get_datavault_lnk_elt_sql(vault_table='rbbe_order_item_product_lnk',
        vault_schema='rvlt_billbee',
        stage_lk_column='LK_RBBE_ORDER_ITEM_PRODUCT',
        stage_hk_column_list=['HK_RBBE_ORDER_ITEM', 'HK_RBBE_PRODUCT'],
        db_connection=dwh_connection,
        stage_schema=stage_schema,
        stage_table=stage_table,
        meta_job_instance_id = my_job_instance.get_job_instance_id(),
        meta_inserted_at = my_job_instance.get_job_started_at() )

        dvf_execute_elt_statement_list(dwh_cursor, statement_list)

        # ----------


        statement_list = dvf_get_datavault_esat_elt_sql(vault_esat_table='rbbe_order_item_order_esat',
        vault_schema='rvlt_billbee',
        stage_lk_column='LK_RBBE_ORDER_ITEM_ORDER',
        vault_lnk_table='rbbe_order_item_order_lnk',
        vault_driving_key_column_list=['HK_RBBE_ORDER_ITEM'],
        stage_driving_key_column_list=['HK_RBBE_ORDER_ITEM'],
        with_deletion_detection=False,
        db_connection=dwh_connection,
        stage_schema=stage_schema,
        stage_table=stage_table,
        meta_job_instance_id = my_job_instance.get_job_instance_id(),
        meta_inserted_at = my_job_instance.get_job_started_at() )

        dvf_execute_elt_statement_list(dwh_cursor, statement_list)

        # ----------


        statement_list = dvf_get_datavault_esat_elt_sql(vault_esat_table='rbbe_order_item_product_esat',
        vault_schema='rvlt_billbee',
        stage_lk_column='LK_RBBE_ORDER_ITEM_PRODUCT',
        vault_lnk_table='rbbe_order_item_product_lnk',
        vault_driving_key_column_list=['HK_RBBE_ORDER_ITEM'],
        stage_driving_key_column_list=['HK_RBBE_ORDER_ITEM'],
        with_deletion_detection=False,
        db_connection=dwh_connection,
        stage_schema=stage_schema,
        stage_table=stage_table,
        meta_job_instance_id = my_job_instance.get_job_instance_id(),
        meta_inserted_at = my_job_instance.get_job_started_at() )

        dvf_execute_elt_statement_list(dwh_cursor, statement_list)

        # ----------


        singularity_check_statement_list = dvf_get_check_singularity_sat_elt_sql(vault_table='rbbe_order_item_order_p1_l10_sat',
        stage_hk_column='HK_RBBE_ORDER_ITEM',
        stage_rh_column='RH_RBBE_ORDER_ITEM_ORDER_P1_L10_SAT',
        stage_schema=stage_schema,
        stage_table=stage_table )

        dvf_execute_elt_statement_list(dwh_cursor, singularity_check_statement_list)

        statement_list = dvf_get_datavault_sat_elt_sql(vault_table='rbbe_order_item_order_p1_l10_sat',
        vault_schema='rvlt_billbee',
        stage_hk_column='HK_RBBE_ORDER_ITEM',
        stage_rh_column='RH_RBBE_ORDER_ITEM_ORDER_P1_L10_SAT',
        stage_content_column_list=['DISCOUNT', 'INVOICESKU', 'LASTMODIFIEDAT','PRODUCT_COUNTRYOFORIGIN', 'PRODUCT_EAN', 'PRODUCT_SKU', 'PRODUCT_TARICCODE', 'PRODUCT_TITLE', 'QUANTITY', 'TAXAMOUNT', 'TAXINDEX', 'TOTALPRICE', 'UNREBATEDTOTALPRICE'],
        with_deletion_detection=False,
        db_connection=dwh_connection,
        stage_schema=stage_schema,
        stage_table=stage_table,
        meta_job_instance_id = my_job_instance.get_job_instance_id(),
        meta_inserted_at = my_job_instance.get_job_started_at() )

        dvf_execute_elt_statement_list(dwh_cursor, statement_list)

        # ----------

# END LOAD DATA TO VLT PART

        dwh_connection.commit()
    # ### FRAMEWORK PHASE: Log any exception to instance
    except Exception:
        my_job_instance.end_instance_with_error(1, 'something went wrong')
        raise
    # # ### FRAMEWORK PHASE: End the instance normally
    my_job_instance.end_instance()



@cimtjobinstance_job
def load_vault_1(data, parent_job_instance=None, **kwargs):

    # ### FRAMEWORK PHASE: setup your job_instance
    my_job_instance = CimtJobInstance(kwargs['instance_job_name'], parent_job_instance)
    my_job_instance.start_instance()

    try:
        # ### FRAMEWORK PHASE: do processing here
        dwh_connection = connection_snf_for_dwh_connection_type(Dvf_dwh_connection_type.raw_vault)
        stage_data(data,my_job_instance, dwh_connection)
        load_data_to_vault(my_job_instance, dwh_connection)
        dwh_connection.close()

    # ### FRAMEWORK PHASE: Log any exception to instance
    except Exception:
        my_job_instance.end_instance_with_error(1, 'some processing failed')
        raise

    # # ### FRAMEWORK PHASE: End the instance normally
    my_job_instance.end_instance()


if __name__ == '__main__':
    # local execution
    load_vault_1()