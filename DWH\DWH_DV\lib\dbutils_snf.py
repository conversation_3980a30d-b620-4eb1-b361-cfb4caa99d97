from lib.dvf_sqlByConvention_snf import dvf_get_datavault_column_lists_from_snf

def get_snf_dict_insert_sql(db_connection, table_schema, table_name):
    """Generates an insert statement that can be used in a  psycopg2 "execute" function
    to insert data content from a dictionary that has a key for every columnname of the table"""

    # Generates
    # INSERT INTO s.t (a,b,c,d) value(%(a)s,%(b)s,%(c)s,%(d)s)

    # can then be excecuted with row data in a dictionary
    # execute_dict_statement(cursor, insert_statement,{'b':1 ,'a':'hello','d':'world','c':'beautiful'}

    column_dict = dvf_get_datavault_column_lists_from_snf(db_connection, table_schema, table_name)
    column_list = []
    column_list.extend(column_dict['meta_columns'])
    column_list.extend(column_dict['dv_key_columns'])
    column_list.extend(column_dict['dv_compare_columns'])
    column_list.extend(column_dict['content_columns'])

    column_section = ", ".join(column_list)
    value_section = ", ".join(map(lambda column_name: f'%({column_name})s', column_list))

    if len(column_section) == 0:
        raise Exception("no columns collected")

    return f"""INSERT INTO {table_schema}.{table_name} ({column_section}) VALUES ({value_section})"""


def execute_snf_dict_bulk_insert(db_cursor, insert_statement, data_row_dict):
    """Executes a insert statement including annotation of errors"""
    chunk_size = 200000 #limit for snowflake insert elements
    try:
        for i in range(0, len(data_row_dict), chunk_size):
            data_row_dict_chunk = data_row_dict[i:i + chunk_size]
            db_cursor.executemany(insert_statement, data_row_dict_chunk)
    except KeyError as kerr:
        raise Exception(kerr, 'Check if key matches name of table column or is missing')
    except Exception:
        print(data_row_dict)
        raise



