# Data Vault模型详解：完整图示说明

## 引言

本文档将详细解释您提供的完整Data Vault模型图，包括所有符号、连接线和区域的含义。这份解释适合没有IT背景的业务人员阅读，旨在帮助您理解数据仓库中的数据结构和关系。

## Data Vault图示符号说明

在Data Vault模型图中，不同的形状和颜色代表不同类型的表和关系：

### 表类型符号

1. **深蓝色椭圆/六边形**：代表**Hub表**（业务核心实体）
   - 例如图中的`invoice_line_inv1`、`invoice_oinv`等

2. **浅蓝色椭圆/六边形**：代表**Link表**（实体之间的关系）
   - 例如图中的`invoice_line_inv1_inv2`、`invoice_line_inv1_invoice`等

3. **黄色矩形**：代表**Satellite表**（描述性信息和历史变化）
   - 例如图中的`invoice_line_inv1_j1_l10`、`invoice_line_inv1_p1_l20`等

4. **绿色矩形**（图底部）：代表**参考表**或**辅助表**
   - 例如图中底部的绿色矩形区域

### 连接线类型

1. **实线**：表示**强关系**，通常连接Hub和Link，或Link和Hub
   - 这种关系是必须的，表示实体之间的核心业务关系
   - 例如`invoice_line_inv1`与`invoice_line_inv1_invoice`之间的实线

2. **虚线**：表示**包含关系**，通常连接Hub/Link与其Satellite
   - 这种关系表示Satellite包含了Hub/Link的描述性信息
   - 例如`invoice_line_inv1`与`invoice_line_inv1_j1_l10`之间的虚线

3. **虚线框**：表示**逻辑分组**，将相关的表组合在一起
   - 这些分组通常基于业务领域或数据源
   - 例如左侧包含`invoice_line_inv1_inv2`相关表的虚线框

## 图中主要组件详解

现在让我们详细解释图中的主要组件和它们之间的关系：

### 1. 发票行及其扩展（左侧区域）

这个区域描述了发票行及其扩展信息的数据结构：

- **Hub表**：`invoice_line_inv1`（深蓝色，中心位置）
  - 代表SAP系统中的发票行实体
  - 由公司代码、文档编号和行号唯一标识

- **Link表**：`invoice_line_inv1_inv2`（浅蓝色，左上方）
  - 连接发票行与其扩展信息（INV2表中的数据）
  - 表示一个发票行可能有额外的扩展信息（如折扣、费用等）

- **Satellite表**（黄色矩形）：
  - `invoice_line_inv1_j1_l10`：存储发票行的原始JSON数据
  - `invoice_line_inv1_p1_l20`：存储发票行的处理后数据
  - `invoice_line_inv1_inv2_j1_l10`：存储发票行扩展的原始JSON数据
  - `invoice_line_inv1_inv2_p1_l20`：存储发票行扩展的处理后数据

这些表通过虚线连接到各自的Hub或Link表，表示它们存储了这些实体的描述性信息。

### 2. 发票行与发票头的关系（中央区域）

这个区域描述了发票行与发票头之间的关系：

- **Link表**：`invoice_line_inv1_invoice`（浅蓝色，中央）
  - 连接发票行与发票头
  - 表示发票头包含多个发票行的关系

- **Hub表**：`invoice_oinv`（深蓝色，右侧）
  - 代表SAP系统中的发票头实体
  - 由公司代码和文档编号唯一标识

- **Satellite表**（黄色矩形）：
  - `invoice_oinv_j1_l10`：存储发票头的原始JSON数据
  - `invoice_oinv_p1_l20`：存储发票头的处理后数据

这些表之间的实线表示强关系，即发票行必须属于一个发票头。

### 3. 发票行与物料的关系（右下区域）

这个区域描述了发票行与物料之间的关系：

- **Link表**：`invoice_line_inv1_item_oitm`（浅蓝色，右下方）
  - 连接发票行与物料
  - 表示发票行引用了特定物料的关系

- **Hub表**：`item_oitm`（深蓝色，右下方）
  - 代表SAP系统中的物料实体
  - 由公司代码和物料代码唯一标识

- **Satellite表**（黄色矩形）：
  - `item_oitm_j1_l10`：存储物料的原始JSON数据
  - `item_oitm_p1_l20`：存储物料的处理后数据

这些表之间的实线表示强关系，即发票行必须引用一个物料。

### 4. 发票头与业务伙伴的关系（右上区域）

这个区域描述了发票头与业务伙伴之间的关系：

- **Link表**：`invoice_oinv_business_partner_ocrd`（浅蓝色，右上方）
  - 连接发票头与业务伙伴
  - 表示发票头关联到特定业务伙伴的关系

- **Hub表**：`business_partner_ocrd`（深蓝色，右上方）
  - 代表SAP系统中的业务伙伴实体
  - 由公司代码和业务伙伴代码唯一标识

- **Satellite表**（黄色矩形）：
  - `business_partner_ocrd_j1_l10`：存储业务伙伴的原始JSON数据
  - `business_partner_ocrd_p1_l20`：存储业务伙伴的处理后数据

这些表之间的实线表示强关系，即发票头必须关联到一个业务伙伴。

### 5. 参考表区域（底部绿色区域）

图底部的绿色矩形区域代表参考表或辅助表，这些表通常包含静态数据或代码值，用于支持主要业务实体。

## 命名约定解释

图中表名遵循特定的命名约定，让我们解释其中的模式：

1. **表名前缀**：
   - 没有显示前缀（如RSAP_），但在实际数据库中会有前缀
   - 例如：`invoice_line_inv1`在数据库中可能是`RSAP_INVOICE_LINE_INV1_HUB`

2. **后缀含义**：
   - `_j1_l10`：表示Load Layer 10的JSON数据（原始数据）
   - `_p1_l20`：表示Load Layer 20的处理后数据
   - 没有显示的后缀如`_HUB`、`_LNK`、`_SAT`，但在实际数据库中会有

## 数据流程示例

为了帮助理解这个模型，让我们通过一个具体的例子来说明数据如何在这些表之间流动：

### 场景：处理一个新的SAP发票

1. **数据提取**：
   - 从SAP系统提取新发票数据（OINV、INV1、INV2表）
   - 数据转换为JSON格式

2. **加载到Raw Vault**：
   - 发票头数据加载到`invoice_oinv`（Hub）和`invoice_oinv_j1_l10`（Satellite）
   - 发票行数据加载到`invoice_line_inv1`（Hub）和`invoice_line_inv1_j1_l10`（Satellite）
   - 发票行扩展数据加载到`invoice_line_inv1_inv2_j1_l10`（Satellite）
   - 创建必要的Link记录，连接发票行与发票头、发票行与物料等

3. **数据处理**：
   - 从JSON数据中提取结构化字段
   - 应用业务规则（如计算调整后的行总价）
   - 结果存储在`_p1_l20`后缀的Satellite表中

4. **业务应用**：
   - 业务用户可以查询处理后的数据
   - 例如，分析特定客户的销售额、特定产品的销售趋势等

## 虚拟数据示例

为了更具体地说明，让我们看一些虚拟数据示例：

### `invoice_oinv`（发票头Hub）：
| HK_RSAP_INVOICE_OINV | COMPANY | DOCENTRY | MD_RECORD_SOURCE | MD_INSERTED_AT |
|----------------------|---------|----------|------------------|----------------|
| a1b2c3d4e5f6         | DEISS_DE| 1001     | sap.oinv         | 2024-05-19 10:00:00 |

### `invoice_oinv_j1_l10`（发票头原始数据Satellite）：
| HK_RSAP_INVOICE_OINV | JSON_TEXT | RH_RSAP_INVOICE_OINV_J1_L10_SAT | MD_VALID_BEFORE | MD_INSERTED_AT |
|----------------------|-----------|----------------------------------|-----------------|----------------|
| a1b2c3d4e5f6         | {"CANCELED": "N", "DocCur": "EUR", "DiscSum": 200.00, "DocTotal": 2000.00} | b2c3d4e5f6 | 9999-12-31 | 2024-05-19 10:00:00 |

### `invoice_line_inv1`（发票行Hub）：
| HK_RSAP_INVOICE_LINE_INV1 | COMPANY | DOCENTRY | LINENUM | MD_RECORD_SOURCE | MD_INSERTED_AT |
|---------------------------|---------|----------|---------|------------------|----------------|
| c3d4e5f6g7h8              | DEISS_DE| 1001     | 1       | sap.inv1         | 2024-05-19 10:00:00 |
| d4e5f6g7h8i9              | DEISS_DE| 1001     | 2       | sap.inv1         | 2024-05-19 10:00:00 |

### `invoice_line_inv1_j1_l10`（发票行原始数据Satellite）：
| HK_RSAP_INVOICE_LINE_INV1 | JSON_TEXT | RH_RSAP_INVOICE_LINE_INV1_J1_L10_SAT | MD_VALID_BEFORE | MD_INSERTED_AT |
|---------------------------|-----------|--------------------------------------|-----------------|----------------|
| c3d4e5f6g7h8              | {"LineTotal": 1000.00, "Currency": "EUR", "ItemCode": "P001"} | e5f6g7h8i9 | 9999-12-31 | 2024-05-19 10:00:00 |
| d4e5f6g7h8i9              | {"LineTotal": 1000.00, "Currency": "EUR", "ItemCode": "P002"} | f6g7h8i9j0 | 9999-12-31 | 2024-05-19 10:00:00 |

### `invoice_line_inv1_invoice`（发票行-发票头Link）：
| LK_RSAP_INVOICE_LINE_INV1_INVOICE | HK_RSAP_INVOICE_LINE_INV1 | HK_RSAP_INVOICE_OINV | MD_RECORD_SOURCE | MD_INSERTED_AT |
|-----------------------------------|---------------------------|----------------------|------------------|----------------|
| g7h8i9j0k1l2                      | c3d4e5f6g7h8              | a1b2c3d4e5f6         | sap.inv1         | 2024-05-19 10:00:00 |
| h8i9j0k1l2m3                      | d4e5f6g7h8i9              | a1b2c3d4e5f6         | sap.inv1         | 2024-05-19 10:00:00 |

## 总结

这个Data Vault模型图展示了SAP发票数据的完整结构，包括发票头、发票行、物料和业务伙伴等实体及其关系。通过使用Hub、Link和Satellite的组合，模型能够：

1. **保持业务实体的完整性**（通过Hub）
2. **跟踪实体之间的关系**（通过Link）
3. **存储详细信息并保留历史变化**（通过Satellite）
4. **支持灵活的业务分析**（通过分离核心实体和描述性信息）

这种结构虽然看起来复杂，但它提供了极大的灵活性和可扩展性，能够适应业务变化并支持各种分析需求。
