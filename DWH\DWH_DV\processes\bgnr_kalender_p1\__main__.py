"""

__main__.py

"""
from lib.cimtjobinstance import CimtJobInstance, cimtjobinstance_job
from lib.dvf_basics import Dvf_dwh_connection_type
from lib.dvf_ddl_deploymentmanager_snf import Dvf_ddl_deploymentmanager_snf
from lib.connection_snf import  connection_snf_for_dwh_connection_type
from lib.blobstorage_utils import get_source_files_from_blob_storage, move_processed_file_to_processed_container
from lib.connection_azrblob import connection_azrblob

from load_vault_1 import load_vault_1


def deploy_datamodel(my_job_instance):
    run_id=my_job_instance.get_job_instance_id()
    insert_date=my_job_instance.get_job_started_at()
    deployment_manager = Dvf_ddl_deploymentmanager_snf(connection_snf_for_dwh_connection_type(Dvf_dwh_connection_type.owner),run_id,insert_date)
    deployment_manager.deploy_stage_table("bvlt_general", "bgnr_kalender_p1_stage")
    deployment_manager.deploy_table("rvlt_general", "rgnr_gesellschaft_hub")
    deployment_manager.deploy_table("rvlt_general", "rgnr_gesellschaft_kalender_dlnk")
    deployment_manager.deploy_table("bvlt_general", "bgnr_gesellschaft_kalender_p1_b10_sat")
    deployment_manager.deploy_view("rvlt_sap", "rsap_invoice_oinv_rgnr_gesellschaft_lnk")
    deployment_manager.deploy_view("rvlt_sap", "rsap_credit_memo_orin_rgnr_gesellschaft_lnk")
    deployment_manager.deploy_view("rvlt_sap", "rsap_business_partner_ocrd_rgnr_gesellschaft_lnk")



@cimtjobinstance_job
def main(**kwargs):
    # ### FRAMEWORK PHASE: setup job_instance for main module
    my_job_instance = CimtJobInstance(kwargs['instance_job_name'])
    my_job_instance.start_instance()


    try:        # ### FRAMEWORK PHASE: do processing here
        deploy_datamodel(my_job_instance)
        load_vault_1(parent_job_instance=my_job_instance)
    # # ### FRAMEWORK PHASE: End with bad or good result
    except Exception as e:
        my_job_instance.end_instance_with_error(1, 'some processing failed')
        raise e
    my_job_instance.end_instance()


if __name__ == '__main__':
    main()
