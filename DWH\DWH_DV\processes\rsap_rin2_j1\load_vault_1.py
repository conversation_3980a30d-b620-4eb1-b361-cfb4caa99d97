"""
RIN2数据加载模块
实现从JSON文件到Data Vault模型的数据加载

作者: Data Vault ETL Framework
创建日期: 2024
描述: 处理RIN2数据的暂存和Data Vault加载逻辑
"""

import sys
import os
import json

# 添加lib路径到Python路径
sys.path.append(os.path.join(os.path.dirname(__file__), '..', '..', 'lib'))

from cimtjobinstance import CimtJobInstance, cimtjobinstance_job
from connection_azrblob import connection_azrblob
from connection_snf import connection_snf_for_dwh_connection_type, Dvf_dwh_connection_type
from blobstorage_utils import fetch_json_source_file_from_blob_container
from dbutils_snf import get_snf_dict_insert_sql
from dvf_sqlByConvention_snf import (
    dvf_get_datavault_hub_elt_sql,
    dvf_get_datavault_lnk_elt_sql,
    dvf_get_datavault_sat_elt_sql,
    dvf_execute_elt_statement_list
)
import hashlib


def calculate_hash_key(values):
    """
    计算哈希键
    
    Args:
        values: 用于计算哈希的值列表
        
    Returns:
        str: 28位哈希键
    """
    if not values:
        return None
    
    # 将所有值转换为字符串并连接
    concat_string = '||'.join([str(v) if v is not None else '' for v in values])
    
    # 计算SHA1哈希并截取前28位
    hash_object = hashlib.sha1(concat_string.encode('utf-8'))
    return hash_object.hexdigest()[:28].upper()


def fetch_and_stage_file(job_instance, dwh_connection, arzblob_connection, file_name, bk_keys, exclude_from_hash_diff):
    """
    获取文件并加载到暂存表
    
    Args:
        job_instance: 作业实例
        dwh_connection: 数据仓库连接
        arzblob_connection: Azure Blob连接
        file_name: 文件名
        bk_keys: 业务键列表
        exclude_from_hash_diff: 差异哈希排除字段
    """
    try:
        # 准备暂存表插入操作
        dwh_cursor = dwh_connection.cursor()
        dwh_cursor.execute("TRUNCATE TABLE stage_rvlt.rsap_rin2_j1_stage")
        
        insert_statement = get_snf_dict_insert_sql(
            dwh_connection, 
            "stage_rvlt", 
            "rsap_rin2_j1_stage"
        )
        
        # 准备基础元数据
        stage_data_row = {
            'md_inserted_at': job_instance.get_job_started_at().isoformat(),
            'md_record_source': 'sap.rin2',
            'md_run_id': job_instance.get_job_instance_id(),
            'md_is_deleted': False
        }
        
        # 从Blob Storage获取源文件
        source_container = "rawdata"
        blob_client = arzblob_connection.get_blob_client(
            container=source_container, 
            blob=file_name
        )
        source_json = fetch_json_source_file_from_blob_container(blob_client)
        
        # 准备暂存数据
        stage_data_rows = []
        
        for record in source_json:
            try:
                # 提取业务键
                company = record.get('Company', '')
                docentry = record.get('DocEntry', 0)
                linenum = record.get('LineNum', 0)
                groupnum = record.get('GroupNum', 0)
                
                # 计算Hub哈希键
                hk_rin1 = calculate_hash_key([company, docentry, linenum])
                
                # 计算Link哈希键
                lk_rin1_rin2 = calculate_hash_key([hk_rin1, groupnum])
                
                # 准备JSON文本（排除指定字段）
                json_record = record.copy()
                for exclude_field in exclude_from_hash_diff or []:
                    json_record.pop(exclude_field, None)
                
                json_text = json.dumps(json_record, ensure_ascii=False, sort_keys=True)
                
                # 计算Satellite差异哈希
                rh_sat = calculate_hash_key([json_text])
                
                # 准备暂存记录
                stage_record = {
                    **stage_data_row,
                    'company': company,
                    'docentry': docentry,
                    'linenum': linenum,
                    'groupnum': groupnum,
                    'json_text': json_text,
                    'hk_rsap_credit_memo_line_rin1': hk_rin1,
                    'lk_rsap_credit_memo_line_rin1_rin2': lk_rin1_rin2,
                    'rh_rsap_credit_memo_line_rin1_rin2_j1_l10_sat': rh_sat
                }
                
                stage_data_rows.append(stage_record)
                
            except Exception as e:
                job_instance.log_error(f"处理记录失败: {record}, 错误: {str(e)}")
                continue
        
        # 批量插入暂存表
        if stage_data_rows:
            dwh_cursor.executemany(insert_statement, stage_data_rows)
            dwh_connection.commit()
            job_instance.log_info(f"成功加载 {len(stage_data_rows)} 条记录到暂存表")
        else:
            job_instance.log_warning("没有有效记录加载到暂存表")
            
    except Exception as e:
        job_instance.log_error(f"暂存文件处理失败: {str(e)}")
        raise


def load_data_to_vault(job_instance, dwh_connection, file_name):
    """
    从暂存表加载数据到Data Vault表
    
    Args:
        job_instance: 作业实例
        dwh_connection: 数据仓库连接
        file_name: 文件名
    """
    try:
        dwh_cursor = dwh_connection.cursor()
        stage_schema = "stage_rvlt"
        stage_table = "rsap_rin2_j1_stage"
        
        job_instance.log_info("开始加载数据到Data Vault表...")
        
        # 1. 加载Hub表（如果记录不存在）
        job_instance.log_info("加载Hub表...")
        hub_statement_list = dvf_get_datavault_hub_elt_sql(
            vault_hub_table='rsap_credit_memo_line_rin1_hub',
            vault_schema='rvlt_sap',
            stage_hk_column='HK_RSAP_CREDIT_MEMO_LINE_RIN1',
            vault_hk_column='HK_RSAP_CREDIT_MEMO_LINE_RIN1',
            stage_bk_column_list=['COMPANY', 'DOCENTRY', 'LINENUM'],
            vault_bk_column_list=['COMPANY', 'DOCENTRY', 'LINENUM'],
            with_deletion_detection=False,
            db_connection=dwh_connection,
            stage_schema=stage_schema,
            stage_table=stage_table,
            meta_job_instance_id=job_instance.get_job_instance_id(),
            meta_inserted_at=job_instance.get_job_started_at()
        )
        dvf_execute_elt_statement_list(dwh_cursor, hub_statement_list)
        
        # 2. 加载依赖Link表
        job_instance.log_info("加载依赖Link表...")
        link_statement_list = dvf_get_datavault_lnk_elt_sql(
            vault_lnk_table='rsap_credit_memo_line_rin1_rin2_dlnk',
            vault_schema='rvlt_sap',
            stage_lk_column='LK_RSAP_CREDIT_MEMO_LINE_RIN1_RIN2',
            vault_lk_column='LK_RSAP_CREDIT_MEMO_LINE_RIN1_RIN2',
            stage_hk_column_list=['HK_RSAP_CREDIT_MEMO_LINE_RIN1'],
            vault_hk_column_list=['HK_RSAP_CREDIT_MEMO_LINE_RIN1'],
            stage_dependent_child_key_column_list=['GROUPNUM'],
            vault_dependent_child_key_column_list=['GROUPNUM'],
            with_deletion_detection=False,
            db_connection=dwh_connection,
            stage_schema=stage_schema,
            stage_table=stage_table,
            meta_job_instance_id=job_instance.get_job_instance_id(),
            meta_inserted_at=job_instance.get_job_started_at()
        )
        dvf_execute_elt_statement_list(dwh_cursor, link_statement_list)
        
        # 3. 加载Satellite表
        job_instance.log_info("加载Satellite表...")
        sat_statement_list = dvf_get_datavault_sat_elt_sql(
            vault_sat_table='rsap_credit_memo_line_rin1_rin2_j1_l10_sat',
            vault_schema='rvlt_sap',
            stage_pk_column='LK_RSAP_CREDIT_MEMO_LINE_RIN1_RIN2',
            vault_pk_column='LK_RSAP_CREDIT_MEMO_LINE_RIN1_RIN2',
            stage_diff_hash_column='RH_RSAP_CREDIT_MEMO_LINE_RIN1_RIN2_J1_L10_SAT',
            vault_diff_hash_column='RH_RSAP_CREDIT_MEMO_LINE_RIN1_RIN2_J1_L10_SAT',
            stage_content_column_list=['JSON_TEXT'],
            vault_content_column_list=['JSON_TEXT'],
            with_deletion_detection=False,
            db_connection=dwh_connection,
            stage_schema=stage_schema,
            stage_table=stage_table,
            meta_job_instance_id=job_instance.get_job_instance_id(),
            meta_inserted_at=job_instance.get_job_started_at()
        )
        dvf_execute_elt_statement_list(dwh_cursor, sat_statement_list)
        
        job_instance.log_info("Data Vault表加载完成")
        
    except Exception as e:
        job_instance.log_error(f"Data Vault加载失败: {str(e)}")
        raise


@cimtjobinstance_job
def load_vault_1(file_name, parent_job_instance=None, **kwargs):
    """
    RIN2数据加载主函数
    
    Args:
        file_name: 要处理的文件名
        parent_job_instance: 父作业实例
        **kwargs: 其他参数
    """
    # ### FRAMEWORK PHASE: 设置作业实例
    my_job_instance = CimtJobInstance(
        kwargs['instance_job_name'], 
        parent_job_instance
    )
    my_job_instance.set_work_item(file_name)
    my_job_instance.start_instance()
    
    # 业务键定义
    bk_keys = ['Company', 'DocEntry', 'LineNum', 'GroupNum']
    exclude_from_hash_diff = ['UpdateDate']
    
    try:
        # ### FRAMEWORK PHASE: 执行处理逻辑
        dwh_connection = connection_snf_for_dwh_connection_type(
            Dvf_dwh_connection_type.raw_vault
        )
        arzblob_connection = connection_azrblob()
        
        # 获取并暂存文件
        fetch_and_stage_file(
            my_job_instance, 
            dwh_connection, 
            arzblob_connection, 
            file_name, 
            bk_keys, 
            exclude_from_hash_diff
        )
        
        # 加载到Data Vault
        load_data_to_vault(my_job_instance, dwh_connection, file_name)
        
        dwh_connection.close()
        
    except Exception:
        # ### FRAMEWORK PHASE: 记录异常到实例
        my_job_instance.end_instance_with_error(1, 'RIN2数据处理失败')
        raise
    
    # ### FRAMEWORK PHASE: 正常结束实例
    my_job_instance.end_instance()


if __name__ == '__main__':
    """
    本地执行测试
    """
    # 本地执行
    load_vault_1(file_name="test_rin2_file.json")
